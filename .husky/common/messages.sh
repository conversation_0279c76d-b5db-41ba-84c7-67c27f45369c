. ./.husky/common/colors.sh

start_section() {
  echo -e "${CYAN}🚀 Starting $1 checks...${NC}"
}

run_step() {
  local step_name="$1"
  local command="$2"

  echo -e "${CYAN}▸ ${NC}Running ${CYAN}${step_name}${NC}"
  $command || {
    echo -e "${RED}❌ ${step_name} failed!${NC}" >&2
    return 1
  }
  echo -e "${GREEN}✔ ${step_name} passed${NC}"
}

final_success() {
  echo -e "\n${GREEN}✅ All $1 checks completed successfully!${NC}"
}

final_error() {
  echo -e "${RED}❌ Error: $1 failed, please check the error${NC}" >&2
  exit 1
}