. ./.husky/common/messages.sh

section_name="commit-msg"
start_section "Commit message validation"

# Initial validation
if pnpm commitlint --edit "$1"; then
  final_success "$section_name"
  exit 0
fi

# If validation fails
echo -e "${RED}❌ Invalid commit message format${NC}"
echo -e "${CYAN}⚡ Starting interactive message helper...${NC}"

# Run Commitizen to fix message
exec < /dev/tty
pnpm commit --hook

# Post-correction validation
if pnpm commitlint --edit "$1"; then
  echo -e "${GREEN}✅ Message corrected successfully${NC}"
  exit 0
else
  final_error "$section_name"
  exit 1
fi