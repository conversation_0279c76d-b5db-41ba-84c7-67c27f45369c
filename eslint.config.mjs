import perfectionist from 'eslint-plugin-perfectionist';
import { dirname } from 'path';
import tseslint from 'typescript-eslint';
import { fileURLToPath } from 'url';

import { FlatCompat } from '@eslint/eslintrc';
import tanstackQuery from '@tanstack/eslint-plugin-query';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const eslintConfig = [
  ...tanstackQuery.configs['flat/recommended'],
  ...compat.config({
    extends: [
      'next/core-web-vitals',
      'next/typescript',
      'prettier',
      'plugin:@typescript-eslint/strict',
    ],
  }),
  { ignores: ['node_modules', '.next', 'out', 'public'] },
  {
    plugins: { perfectionist, tseslint },
    rules: {
      semi: ['error', 'always'],

      'react/no-unescaped-entities': 'off',

      // Custom rule to restrict imports from next/link and next/navigation
      'no-restricted-imports': [
        'error',
        {
          name: 'next/link',
          message: 'Please import from `@/i18n/routing` instead.',
        },
        {
          name: 'next/navigation',
          importNames: [
            'redirect',
            'permanentRedirect',
            'useRouter',
            'usePathname',
          ],
          message: 'Please import from `@/i18n/routing` instead.',
        },
      ],

      'perfectionist/sort-exports': [1, { order: 'asc', type: 'line-length' }],
      'perfectionist/sort-named-imports': [
        1,
        { order: 'asc', type: 'line-length' },
      ],
      'perfectionist/sort-named-exports': [
        1,
        { order: 'asc', type: 'line-length' },
      ],
      'perfectionist/sort-imports': [
        1,
        {
          order: 'asc',
          type: 'natural', // line-length, 'alphabetical', natural, none
          newlinesBetween: 'always',
          groups: [
            'type',
            ['react', 'next', 'external', 'builtin'],
            ['lucide-icons'],
            'custom-features',
            ['custom-types', 'custom-dtos'],
            'custom-utils',
            'custom-hooks',
            'internal',
            'custom-config',
            'ui-components',
            'custom-components',
            ['parent-type', 'sibling-type', 'index-type'],
            ['parent', 'sibling', 'index'],
            'object',
            'unknown',
            'custom-styles',
          ],
          customGroups: {
            value: {
              react: '^react',
              next: '^next',
              'lucide-icons': 'lucide-react',
              'custom-features': '@/features/*',
              'custom-hooks': '@/hooks/*',
              'custom-utils': '@/lib/*',
              'custom-types': '@/types/*',
              'custom-dtos': '@/api/models/dtos/*',
              'custom-components': '@/components/*',
              'ui-components': '@/components/ui/*',
              'custom-config': '@/config/*',
              'custom-styles': '^@/styles/(.css)',
            },
          },
          internalPattern: ['@/*'],
        },
      ],
    },
  },
];

export default eslintConfig;
