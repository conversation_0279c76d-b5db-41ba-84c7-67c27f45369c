# This is the new frontend version of [Kwore V2](https://front-v2-359903658076.europe-west1.run.app/en)

[![image](docs/assets/images/cover.png)](https://github.com/new?template_name=nestjs-boilerplate&template_owner=brocoders)

Belongs to the [Kwore](https://github.com/Kwore) organization.

---

## Table of Contents <!-- omit in toc -->

- [Description](#description)
- [Features](#features)
- [Contributors](#contributors)
- [Support](#support)

---

## Description

Kwore seamlessly integrated all the modern AI generation tools into one platform so that you can generate content with a single click.

![Demo](docs/assets/images/all-vid.gif)
Full documentation [here](docs/readme.md)

- Demo: <https://front-v2-359903658076.europe-west1.run.app/en>
- Backend: <https://github.com/Kwore/backend-golang>

## Features

- [x] Next.js
- [x] TypeScript
- [x] ESLint
- [x] Internationalization/Translations (I18N) ([Next Intl](https://next-intl.dev))
- [x] Auth (Sign in, Sign up, Reset password, Confirm email) ([Next Auth](https://next-auth.js.org))
- [x] Social sign in (Microsoft, Facebook, Google) ([Firebase](https://firebase.google.com))
- [x] [Shadcn UI](https://ui.shadcn.com). Supports dark mode
- [x] [TanStack Form](https://tanstack.com/form/latest)
- [x] [TanStack React Query](https://tanstack.com/query/latest)
- [x] [TanStack Table](https://tanstack.com/query/latest)
- [x] E2E tests ([Playwright](https://playwright.dev))
- [x] Units tests ([Vitest](https://vitest.dev/guide))
- [x] Docker
- [x] Deploy to Google Cloud Run
- [ ] Type-safe search params state manager ([Nuqs](https://nuqs.47ng.com/))
- [ ] File Uploads using Google Cloud Storage (GCS)
- [ ] CI (GitHub Actions).

## Contributors

<!-- ALL-CONTRIBUTORS-LIST:START - Do not remove or modify this section -->
<!-- prettier-ignore-start -->
<!-- markdownlint-disable -->
<table>
  <tbody>
    <tr>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/Alaedd1ne"><img src="https://avatars.githubusercontent.com/u/15046878?v=4" width="100px;" alt="Alaedd1ne"/><br /><sub><b>Alaeddine Bouattour</b></sub></a><br /><a href="#maintenance-AlaeddineBouattour" title="Maintenance">🚧</a> <a href="#business-AhmedTrabelsi" title="Business logic">💼</a> <a href="#code-AlaeddineBouattour" title="Code">💻</a> <a href="#bug-AlaeddineBouattour" title="Bug reports">🐛</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/AhmedTrabelsi01"><img src="https://avatars.githubusercontent.com/u/73963512?v=4" width="100px;" alt="AhmedTrabelsi01"/><br /><sub><b>Ahmed Trabelsi</b></sub></a><br /><a href="#business-AhmedTrabelsi" title="Business logic">💼</a> <a href="#bug-AhmedTrabselsi" title="Bug reports">🐛</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/sabrine-benabdallah"><img src="https://avatars.githubusercontent.com/u/119415405?v=4" width="100px;" alt="sabrine-benabdallah"/><br /><sub><b>Sabrine Benabdallah</b></sub></a><br /><a href="#maintenance-SabrineBenbdallah" title="Maintenance">🚧</a> <a href="#code-SabrineBenabdallah" title="Code">💻</a> <a href="#test-SabrineBenbdallah" title="Tests">⚠️</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/Aymen-Khanfir"><img src="https://avatars.githubusercontent.com/u/110935485?v=4" width="100px;" alt="Aymen-Khanfir"/><br /><sub><b>Aymen Khanfir</b></sub></a><br /><a href="#maintenance-AymenKhanfir" title="Maintenance">🚧</a> <a href="#code-AymenKhanfir" title="Code">💻</a> <a href="#doc-AymenKhanfir" title="Documentation">📖</a> <a href="#test-AymenKhanfir" title="Tests">⚠️</a></td>
    </tr>
  </tbody>
</table>
<!-- markdownlint-restore -->
<!-- prettier-ignore-end -->
<!-- ALL-CONTRIBUTORS-LIST:END -->

## Support

If you seek consulting, support or wish to collaborate. Please contact the owner via Email [Alaeddine Bouattour](mailto:<EMAIL>). For any inquiries regarding the front project, feel free to ask on [GitHub Discussions](https://github.com/Kwore/kwore-front-v2/discussions).

---

<div align="center">

Next: [Get Started](docs/readme.md)

</div>
