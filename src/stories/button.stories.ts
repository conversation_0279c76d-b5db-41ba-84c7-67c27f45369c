import { <PERSON>a, StoryObj } from '@storybook/react';

import { Button } from '@/components/ui/button';

const meta = {
  title: 'ui/Button',
  component: Button,
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: { type: 'select' },
      options: [
        'default',
        'secondary',
        'destructive',
        'ghost',
        'link',
        'outline',
      ],
    },
    size: {
      control: { type: 'select' },
      options: ['default', 'icon', 'sm', 'lg'],
    },
  },
  parameters: {
    layout: 'centered',
  },
} satisfies Meta<typeof Button>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    variant: 'default',
    children: 'text',
  },
};

export const Secondary: Story = {
  args: {
    variant: 'secondary',
    children: 'text',
  },
};

export const Destructive: Story = {
  args: {
    variant: 'destructive',
    children: 'text',
  },
};

export const Ghost: Story = {
  args: {
    variant: 'ghost',
    children: 'text',
  },
};

export const Link: Story = {
  args: {
    variant: 'link',
    children: 'text',
  },
};

export const Outline: Story = {
  args: {
    variant: 'outline',
    children: 'text',
  },
};

export const SizeDefault: Story = {
  args: {
    size: 'default',
    children: 'text',
  },
};

export const SizeLarge: Story = {
  args: {
    size: 'lg',
    children: 'text',
  },
};

export const SizeSmall: Story = {
  args: {
    size: 'sm',
    children: 'text',
  },
};

export const SizeIcon: Story = {
  args: {
    size: 'icon',
    children: 'icon',
  },
};
