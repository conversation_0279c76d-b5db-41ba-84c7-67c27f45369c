import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';

import { Tabs, TabsList, Ta<PERSON>Content, TabsTrigger } from '@/components/ui/tabs';

const meta = {
  title: 'ui/Tabs',
  component: Tabs,
  tags: ['autodocs'],
  argTypes: {
    defaultValue: {
      control: { type: 'select' },
      options: ['account', 'password'],
      description: 'Default active tab value',
    },
    className: {
      control: 'text',
      description: 'Additional class names for the root tab component',
    },
  },
  args: {
    defaultValue: 'account',
  },
  render: (args, { defaultValue }) => (
    <Tabs defaultValue={defaultValue} {...args} className='w-52'>
      <TabsList className='flex w-full items-center justify-around'>
        <TabsTrigger value='account'>Account</TabsTrigger>
        <TabsTrigger value='password'>Password</TabsTrigger>
      </TabsList>
      <TabsContent value='account' className='p-4'>
        Make changes to your account here. Update your username, email, and
        security settings.
      </TabsContent>
      <TabsContent value='password' className='p-4'>
        Change your password here. Ensure it's at least 8 characters and
        includes a mix of letters, numbers, and symbols.
      </TabsContent>
    </Tabs>
  ),
  parameters: {
    layout: 'centered',
  },
} satisfies Meta<typeof Tabs>;

export default meta;

type Story = StoryObj<typeof meta>;

/** Default tabs component with account and password panels */
export const Default: Story = {};
