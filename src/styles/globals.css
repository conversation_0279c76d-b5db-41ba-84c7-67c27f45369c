@tailwind base;
@tailwind components;
@tailwind utilities;
@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 217 70% 54%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --success: 142.1 76.2% 36.3%;
    --success-foreground: 355.7 100% 97.3%;
    --rose: 346.8 77.2% 49.8%;
    --rose-foreground: 355.7 100% 97.3%;
    --violet: 262.1 83.3% 57.8%;
    --violet-foreground: 210 20% 98%;
    --yellow: 47.9 95.8% 53.1%;
    --yellow-foreground: 26 83.3% 14.1%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
    --chart-1: 220 70% 50%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 217 70% 16%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 210 91% 90%;
    --sidebar-accent-foreground: 215 90% 48%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --success: 142.1 70.6% 45.3%;
    --success-foreground: 144.9 80.4% 10%;
    --rose: 346.8 77.2% 49.8%;
    --rose-foreground: 355.7 100% 97.3%;
    --violet: 263.4 70% 50.4%;
    --violet-foreground: 210 20% 98%;
    --yellow: 47.9 95.8% 53.1%;
    --yellow-foreground: 26 83.3% 14.1%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 48%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 222.2 89% 8%; /* changed: 240 5.9% 10%; */
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 0 0% 100%;
    --sidebar-accent-foreground: 215 90% 48%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}
@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer hue {
  .sketch-picker > div > div > .saturation-white > div > div {
    @apply !w-5 !h-5 !border-[5px] !border-white !shadow-none rounded-full;
  }

  .sketch-picker {
    & > div:nth-child(2) > div > div {
      @apply !overflow-visible;
    }
  }

  .hue-horizontal {
    @apply !overflow-visible !rounded-md;
  }

  .hue-horizontal > div {
    @apply !overflow-visible !top-1/2 !-translate-y-1/2;
  }

  .hue-horizontal > div > div {
    @apply !w-4 !h-4 !border-[5px] !border-white outline !outline-gray-300 !outline-1 !shadow-none !rounded-full !m-0 !bg-transparent;
  }
}