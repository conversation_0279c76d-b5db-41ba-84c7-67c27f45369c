import { useTranslations } from 'next-intl';

import { type LucideIcon } from 'lucide-react';

import { Icons } from '@/components/shared/icons';

type RouteGroup = {
  groupName: string;
  children: RouteItem[];
};

type RouteItem = {
  name: string;
  url: string;
  icon: LucideIcon | React.FC<React.SVGProps<SVGSVGElement>>;
};

type BottomNavItem = {
  title: string;
  url: string;
  icon: LucideIcon | React.FC<React.SVGProps<SVGSVGElement>>;
};

export type SidebarConfig = {
  mainRoutes: RouteGroup[];
  bottomNav: BottomNavItem[];
};

type TFunction = ReturnType<typeof useTranslations>;

export function createSidebarConfig(t: TFunction): SidebarConfig {
  return {
    mainRoutes: [
      {
        groupName: t('main_routes.overview_group'),
        children: [
          {
            name: t('main_routes.dashboard'),
            url: '/dashboard',
            icon: Icons.layoutDashboard,
          },
          {
            name: t('main_routes.campaigns'),
            url: '/dashboard/campaigns',
            icon: Icons.megaphone,
          },
          {
            name: t('main_routes.calendar'),
            url: '/dashboard/calendar',
            icon: Icons.calendar,
          },
        ],
      },
      {
        groupName: t('main_routes.content_group'),
        children: [
          {
            name: t('main_routes.posts'),
            url: '/dashboard/posts',
            icon: Icons.galleryHorizontal,
          },
        ],
      },
      {
        groupName: t('main_routes.brandAssets_group'),
        children: [
          {
            name: t('main_routes.knowledge'),
            url: '/dashboard/knowledge',
            icon: Icons.folderOpen,
          },
          {
            name: t('main_routes.private_model'),
            url: '/dashboard/private-model',
            icon: Icons.crown,
          },
        ],
      },
    ],

    bottomNav: [
      {
        title: t('bottom_nav.demo'),
        url: '/demo',
        icon: Icons.circlePlay,
      },
      {
        title: t('bottom_nav.support'),
        url: '/support',
        icon: Icons.headset,
      },
    ],
  };
}
