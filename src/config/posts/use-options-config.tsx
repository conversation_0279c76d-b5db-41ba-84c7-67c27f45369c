import { useTranslations } from 'next-intl';

import { FilterConfig } from '@/components/shared/table/data-table-filter-options';
import { SearchConfig } from '@/components/shared/table/data-table-search-options';

export function useOptionsConfig() {
  const t = useTranslations();

  const searchConfig: SearchConfig = {
    columns: ['Ad.title'],
    placeholder: t('posts_page.search_config.placeholder'),
  };

  const filterConfig: FilterConfig[] = [
    {
      column: 'SocialMedia',
      type: 'select',
      label: t('posts_page.columns.social_media'),
      options: [
        { label: 'Facebook', value: 'facebook' },
        { label: 'Instagram', value: 'instagram' },
        { label: 'LinkedIn', value: 'linkedin' },
        { label: 'X', value: 'x' },
      ],
    },
    {
      column: 'AdType',
      type: 'select',
      label: t('posts_page.filter_config.post_type.label'),
      options: [
        {
          label: t('posts_page.filter_config.post_type.text_only'),
          value: 'text_only',
        },
        {
          label: t('posts_page.filter_config.post_type.text_for_image'),
          value: 'text_for_image',
        },
        {
          label: t('posts_page.filter_config.post_type.text_for_pdf'),
          value: 'text_for_pdf',
        },
        {
          label: t('posts_page.filter_config.post_type.text_image'),
          value: 'text_image',
        },
        {
          label: t('posts_page.filter_config.post_type.text_many_image'),
          value: 'text_many_image',
        },
      ],
    },
    {
      column: 'Status',
      type: 'select',
      label: t('posts_page.columns.status'),
      options: [
        {
          label: t('posts_page.filter_config.status.draft'),
          value: 'Draft',
        },
        {
          label: t('posts_page.filter_config.status.ready'),
          value: 'Ready',
        },
        {
          label: t('posts_page.filter_config.status.approved'),
          value: 'Approved',
        },
        {
          label: t('posts_page.filter_config.status.scheduled'),
          value: 'Scheduled',
        },
        {
          label: t('posts_page.filter_config.status.posted'),
          value: 'Posted',
        },
      ],
    },
  ];

  return {
    searchConfig,
    filterConfig,
  };
}
