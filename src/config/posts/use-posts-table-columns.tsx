'use client';

import type { FilterFn, ColumnDef } from '@tanstack/react-table';

import { format } from 'date-fns';
import { useTranslations } from 'next-intl';

import { PostSuggestion } from '@/api/models/dtos/post.dto';

import { filterByDate } from '@/lib/filter-by-date';
import { cn } from '@/lib/utils';

import {
  useDeletePost,
  useMarkPostAsFavorite,
} from '@/api/hooks/posts/mutations';
import { useRouter } from '@/i18n/routing';
import { useCreativePostStore } from '@/stores/creative-post-store';
import { usePostStore } from '@/stores/post-store';

import { PlatformBadge } from '@/components/posts/atoms/platform-badge';
import { PostTypeBadge } from '@/components/posts/atoms/post-type-badge';
import { StatusBadge } from '@/components/posts/atoms/status-badge';
import { Icons } from '@/components/shared/icons';
import { Button } from '@/components/ui/button';

const typeFilterFn: FilterFn<PostSuggestion> = (
  row,
  columnId,
  filterValue: string[]
) => {
  if (!filterValue?.length) return true;
  const type = row.getValue<string>(columnId);
  return filterValue.includes(type);
};

export const usePostsColumns = () => {
  const t = useTranslations();
  const router = useRouter();

  const { setPosts, setOpenPostDetail } = usePostStore();
  const { setCurrentPostSuggestion, setPreviousPage } = useCreativePostStore();

  const { mutateAsync: handleFavorite } = useMarkPostAsFavorite();
  const { mutateAsync: handleDelete } = useDeletePost();

  const postColumnsDef: ColumnDef<PostSuggestion>[] = [
    {
      id: 'Ad.title',
      header: t('posts_page.columns.name'),
      accessorFn: (row) => row.Ad.title,
      size: 200,
    },
    {
      header: t('posts_page.columns.status'),
      accessorKey: 'Status',
      size: 100,
      cell: ({ row }) => <StatusBadge status={row.getValue('Status')} />,
    },
    {
      header: t('posts_page.columns.social_media'),
      accessorKey: 'SocialMedia',
      size: 100,
      enableSorting: false,
      cell: ({ row }) => (
        <PlatformBadge platform={row.getValue('SocialMedia')} />
      ),
    },
    {
      header: t('posts_page.columns.type'),
      accessorKey: 'AdType',
      cell: ({ row }) => <PostTypeBadge type={row.getValue('AdType')} />,
      size: 100,
      filterFn: typeFilterFn,
    },
    {
      header: t('posts_page.columns.created_by'),
      accessorKey: 'UserEmail',
      size: 180,
    },
    {
      header: t('posts_page.columns.creation_date'),
      accessorKey: 'CreationTimestamp',
      size: 120,
      cell: ({ row }) => {
        return format(new Date(row.getValue('CreationTimestamp')), 'PPP');
      },
      filterFn: filterByDate(),
      enableSorting: false,
    },
    {
      id: 'actions',
      header: t('posts_page.columns.actions'),
      cell: ({ row, table }) => {
        const isFavorite = row.original.Favorite;

        const handleViewPost = () => {
          const allRows = table.getRowModel().rows;
          const currentIndex = allRows.findIndex(
            (r) => r.original.Id === row.original.Id
          );

          const previousPost =
            currentIndex > 0 ? allRows[currentIndex - 1]?.original : null;
          const nextPost =
            currentIndex < allRows.length - 1
              ? allRows[currentIndex + 1]?.original
              : null;

          setPosts({
            previous: previousPost?.Id || '',
            current: row.original.Id,
            next: nextPost?.Id || '',
          });

          setOpenPostDetail(true);
        };

        return (
          <div className='flex items-center gap-1'>
            <Button
              variant='ghost'
              size='setting'
              className='text-gray-400 hover:text-primary'
              onClick={() => {
                handleViewPost();
              }}
            >
              <Icons.eye />
            </Button>

            <Button
              variant='ghost'
              size='setting'
              className={cn({
                'text-muted-foreground hover:text-[#F5A524]': !isFavorite,
                'text-[#F5A524] hover:text-muted-foreground': isFavorite,
              })}
              onClick={async () => await handleFavorite(row.original.Id)}
            >
              <Icons.star fill={isFavorite ? 'currentColor' : 'white'} />
            </Button>

            <Button
              variant='ghost'
              size='setting'
              className='text-muted-foreground hover:text-primary'
              onClick={() => {
                setCurrentPostSuggestion(row.original);
                setPreviousPage('/dashboard/posts');
                router.push('/dashboard/creative/edit-text');
              }}
            >
              <Icons.penEdit />
            </Button>

            <Button
              variant='ghost'
              size='icon'
              className='size-8 text-muted-foreground hover:text-destructive'
              onClick={async () => await handleDelete(row.original.Id)}
            >
              <Icons.trash />
            </Button>
          </div>
        );
      },
      size: 60,
    },
  ];
  return { postColumnsDef };
};
