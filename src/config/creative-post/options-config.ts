import { useTranslations } from 'next-intl';

import { Icons } from '@/components/shared/icons';

export const useGenerationTypes = () => {
  const t = useTranslations();

  return [
    {
      value: 'text_image',
      label: t('creative_card.tabs.visual.children.image.title'),
    },
    {
      value: 'video',
      label: t('creative_card.tabs.visual.children.video.title'),
      disabled: true,
    },
  ];
};

export const useImageSizes = () => {
  const t = useTranslations();

  return [
    {
      value: 'custom',
      label: t('creative_card.form.image_generation.fields.image_sizes.custom'),
      width: 1200,
      height: 1200,
      icon: Icons.magicStick,
    },
    {
      value: '1:1',
      label: t('creative_card.form.image_generation.fields.image_sizes.square'),
      width: 1080,
      height: 1080,
    },
    {
      value: '2:1',
      label: t(
        'creative_card.form.image_generation.fields.image_sizes.landscape'
      ),
      width: 1024,
      height: 512,
    },
    {
      value: '16:4',
      label: t(
        'creative_card.form.image_generation.fields.image_sizes.cinemascope'
      ),
      width: 1600,
      height: 400,
    },
    {
      value: '4:3',
      label: t(
        'creative_card.form.image_generation.fields.image_sizes.landscape'
      ),
      width: 940,
      height: 788,
    },
    {
      value: '16:9',
      label: t(
        'creative_card.form.image_generation.fields.image_sizes.portrait'
      ),
      width: 1200,
      height: 628,
    },
    {
      value: '9:16',
      label: t(
        'creative_card.form.image_generation.fields.image_sizes.vertical'
      ),
      width: 1080,
      height: 1920,
    },
  ];
};

export const imageTypes = [
  {
    category: 'image_choice_card.type.categories.abstract_and_experimental',
    content: [
      {
        id: 'abstract-photography',
        title: 'image_choice_card.type.content.abstract_photography',
        src: '/images/creative/types/abstract-and-experimental/abstract-photography.webp',
      },
      {
        id: 'experimental-photography',
        title: 'image_choice_card.type.content.experimental_photography',
        src: '/images/creative/types/abstract-and-experimental/experimental-photography.webp',
      },
      {
        id: 'infrared-photography',
        title: 'image_choice_card.type.content.infrared_photography',
        src: '/images/creative/types/abstract-and-experimental/infrared-photography.webp',
      },
      {
        id: '360-photography',
        title: 'image_choice_card.type.content.360_photography',
        src: '/images/creative/types/abstract-and-experimental/360-photography.webp',
      },
      {
        id: 'light-painting-photography',
        title: 'image_choice_card.type.content.light_painting_photography',
        src: '/images/creative/types/abstract-and-experimental/light-painting-photography.webp',
      },
      {
        id: 'long-exposure-photography',
        title: 'image_choice_card.type.content.long_exposure_photography',
        src: '/images/creative/types/abstract-and-experimental/long-exposure-photography.webp',
      },
    ],
  },
  {
    category: 'image_choice_card.type.categories.artistic_and_creative',
    content: [
      {
        id: 'conceptual-photography',
        title: 'image_choice_card.type.content.conceptual_photography',
        src: '/images/creative/types/artistic-and-creative/conceptual-photography.webp',
      },
      {
        id: 'fine-art-photography',
        title: 'image_choice_card.type.content.fine_art_photography',
        src: '/images/creative/types/artistic-and-creative/fine-art-photography.webp',
      },
      {
        id: 'hdr-photography',
        title: 'image_choice_card.type.content.hdr_photography',
        src: '/images/creative/types/artistic-and-creative/hdr-photography.webp',
      },
      {
        id: 'surreal-photography',
        title: 'image_choice_card.type.content.surreal_photography',
        src: '/images/creative/types/artistic-and-creative/surreal-photography.webp',
      },
    ],
  },
  {
    category: 'image_choice_card.type.categories.event_and_documentary',
    content: [
      {
        id: 'documentary-photography',
        title: 'image_choice_card.type.content.documentary_photography',
        src: '/images/creative/types/event-and-documentary/documentary-photography.webp',
      },
      {
        id: 'event-photography',
        title: 'image_choice_card.type.content.event_photography',
        src: '/images/creative/types/event-and-documentary/event-photography.webp',
      },
      {
        id: 'photojournalism',
        title: 'image_choice_card.type.content.photojournalism',
        src: '/images/creative/types/event-and-documentary/photojournalism.webp',
      },
    ],
  },
  {
    category: 'image_choice_card.type.categories.fashion_and_commercial',
    content: [
      {
        id: 'commercial-photography',
        title: 'image_choice_card.type.content.commercial_photography',
        src: '/images/creative/types/fashion-and-commercial/commercial-photography.webp',
      },
      {
        id: 'fashion-photography',
        title: 'image_choice_card.type.content.fashion_photography',
        src: '/images/creative/types/fashion-and-commercial/fashion-photography.webp',
      },
      {
        id: 'fashion-editorial-photography',
        title: 'image_choice_card.type.content.fashion_editorial_photography',
        src: '/images/creative/types/fashion-and-commercial/fashion-editorial-photography.webp',
      },
      {
        id: 'commercial-product-photography',
        title: 'image_choice_card.type.content.commercial_product_photography',
        src: '/images/creative/types/fashion-and-commercial/commercial-product-photography.webp',
      },
    ],
  },
  {
    category: 'image_choice_card.type.categories.nature_and_environment',
    content: [
      {
        id: 'aerial-photography',
        title: 'image_choice_card.type.content.aerial_photography',
        src: '/images/creative/types/nature-and-environment/aerial-photography.webp',
      },
      {
        id: 'astro-photography',
        title: 'image_choice_card.type.content.astro_photography',
        src: '/images/creative/types/nature-and-environment/astro-photography.webp',
      },
      {
        id: 'landscape-photography',
        title: 'image_choice_card.type.content.landscape_photography',
        src: '/images/creative/types/nature-and-environment/landscape-photography.webp',
      },
      {
        id: 'macro-photography',
        title: 'image_choice_card.type.content.macro_photography',
        src: '/images/creative/types/nature-and-environment/macro-photography.webp',
      },
      {
        id: 'underwater-photography',
        title: 'image_choice_card.type.content.underwater_photography',
        src: '/images/creative/types/nature-and-environment/underwater-photography.webp',
      },
      {
        id: 'wildlife-photography',
        title: 'image_choice_card.type.content.wildlife_photography',
        src: '/images/creative/types/nature-and-environment/wildlife-photography.webp',
      },
    ],
  },
  {
    category: 'image_choice_card.type.categories.people_and_lifestyle',
    content: [
      {
        id: 'lifestyle-photography',
        title: 'image_choice_card.type.content.lifestyle_photography',
        src: '/images/creative/types/people-and-lifestyle/lifestyle-photography.webp',
      },
      {
        id: 'portrait-photography',
        title: 'image_choice_card.type.content.portrait_photography',
        src: '/images/creative/types/people-and-lifestyle/portrait-photography.webp',
      },
      {
        id: 'pet-photography',
        title: 'image_choice_card.type.content.pet_photography',
        src: '/images/creative/types/people-and-lifestyle/pet-photography.webp',
      },
      {
        id: 'wedding-photography',
        title: 'image_choice_card.type.content.wedding_photography',
        src: '/images/creative/types/people-and-lifestyle/wedding-photography.webp',
      },
    ],
  },
  {
    category: 'image_choice_card.type.categories.professional_services',
    content: [
      {
        id: 'commercial-photography',
        title: 'image_choice_card.type.content.commercial_photography',
        src: '/images/creative/types/professional-services/commercial-photography.webp',
      },
      {
        id: 'commercial-product-photography',
        title: 'image_choice_card.type.content.commercial_product_photography',
        src: '/images/creative/types/professional-services/commercial-product-photography.webp',
      },
      {
        id: 'professional-branding',
        title: 'image_choice_card.type.content.professional_branding',
        src: '/images/creative/types/professional-services/professional-branding.webp',
      },
      {
        id: 'professional-editorial',
        title: 'image_choice_card.type.content.professional_editorial',
        src: '/images/creative/types/professional-services/professional-editorial.webp',
      },
    ],
  },
  {
    category: 'image_choice_card.type.categories.specialized_photography',
    content: [
      {
        id: 'food-photography',
        title: 'image_choice_card.type.content.food_photography',
        src: '/images/creative/types/specialized-photography/food-photography.webp',
      },
      {
        id: 'medical-photography',
        title: 'image_choice_card.type.content.medical_photography',
        src: '/images/creative/types/specialized-photography/medical-photography.webp',
      },
      {
        id: 'night-photography',
        title: 'image_choice_card.type.content.night_photography',
        src: '/images/creative/types/specialized-photography/night-photography.webp',
      },
      {
        id: 'panoramic-photography',
        title: 'image_choice_card.type.content.panoramic_photography',
        src: '/images/creative/types/specialized-photography/panoramic-photography.webp',
      },
      {
        id: 'sports-action-photography',
        title: 'image_choice_card.type.content.sports_action_photography',
        src: '/images/creative/types/specialized-photography/sports-action-photography.webp',
      },
      {
        id: 'sports-photography',
        title: 'image_choice_card.type.content.sports_photography',
        src: '/images/creative/types/specialized-photography/sports-photography.webp',
      },
    ],
  },
  {
    category: 'image_choice_card.type.categories.urban_and_architecture',
    content: [
      {
        id: 'architecture-photography',
        title: 'image_choice_card.type.content.architecture_photography',
        src: '/images/creative/types/urban-and-architecture/architectural-photography.webp',
      },
      {
        id: 'real-estate-photography',
        title: 'image_choice_card.type.content.real_estate_photography',
        src: '/images/creative/types/urban-and-architecture/real-estate-photography.webp',
      },
      {
        id: 'street-photography',
        title: 'image_choice_card.type.content.street_photography',
        src: '/images/creative/types/urban-and-architecture/street-photography.webp',
      },
    ],
  },
];

export const imageStyles = [
  {
    category: 'image_choice_card.style.categories.basic',
    content: [
      {
        id: 'game-like-graphics',
        title: 'image_choice_card.style.content.game_like_graphics',
        src: '/images/creative/styles/basic/game-like-graphics.webp',
      },
      {
        id: 'luxurious-lighting',
        title: 'image_choice_card.style.content.luxurious_lighting',
        src: '/images/creative/styles/basic/luxurious-lighting.webp',
      },
      {
        id: 'dark-mysterious',
        title: 'image_choice_card.style.content.dark_mysterious',
        src: '/images/creative/styles/basic/dark-mysterious.webp',
      },
      {
        id: 'vintage-japanese',
        title: 'image_choice_card.style.content.vintage_japanese',
        src: '/images/creative/styles/basic/vintage-japanese.webp',
      },
      {
        id: 'photo-illustration',
        title: 'image_choice_card.style.content.photo_illustration',
        src: '/images/creative/styles/basic/photo-illustration.webp',
      },
      {
        id: 'dark-classical-themes',
        title: 'image_choice_card.style.content.dark_classical_themes',
        src: '/images/creative/styles/basic/dark-classical-themes.webp',
      },
      {
        id: 'retro-dark-anime',
        title: 'image_choice_card.style.content.retro_dark_anime',
        src: '/images/creative/styles/basic/retro-dark-anime.webp',
      },
      {
        id: 'fisheye-lens',
        title: 'image_choice_card.style.content.fisheye_lens',
        src: '/images/creative/styles/basic/fisheye-lens.webp',
      },
      {
        id: 'soft-watercolors',
        title: 'image_choice_card.style.content.soft_watercolors',
        src: '/images/creative/styles/basic/soft-watercolors.webp',
      },
      {
        id: 'double-exposure',
        title: 'image_choice_card.style.content.double_exposure',
        src: '/images/creative/styles/basic/double-exposure.webp',
      },
      {
        id: 'film-style-grading',
        title: 'image_choice_card.style.content.film_style_grading',
        src: '/images/creative/styles/basic/film-style-grading.webp',
      },
      {
        id: 'shiny-surfaces',
        title: 'image_choice_card.style.content.shiny_surfaces',
        src: '/images/creative/styles/basic/shiny-surfaces.webp',
      },
      {
        id: 'glitch-effects',
        title: 'image_choice_card.style.content.glitch_effects',
        src: '/images/creative/styles/basic/glitch-effects.webp',
      },
      {
        id: 'neon-retro-glow',
        title: 'image_choice_card.style.content.neon_retro_glow',
        src: '/images/creative/styles/basic/neon-retro-glow.webp',
      },
      {
        id: 'fine-art-themes',
        title: 'image_choice_card.style.content.fine_art_themes',
        src: '/images/creative/styles/basic/fine-art-themes.webp',
      },
      {
        id: 'painted-imagery',
        title: 'image_choice_card.style.content.painted_imagery',
        src: '/images/creative/styles/basic/painted-imagery.webp',
      },
      {
        id: 'high-contrast-ink',
        title: 'image_choice_card.style.content.high_contrast_ink',
        src: '/images/creative/styles/basic/high-contrast-ink.webp',
      },
      {
        id: 'dreamlike-themes',
        title: 'image_choice_card.style.content.dreamlike_themes',
        src: '/images/creative/styles/basic/dreamlike-themes.webp',
      },
    ],
  },
  {
    category: 'image_choice_card.style.categories.advanced',
    content: [
      {
        id: 'ultra-realistic',
        title: 'image_choice_card.style.content.ultra_realistic',
        src: '/images/creative/styles/advanced/utlra-realistic.jpeg',
      },
      {
        id: 'cute-3d',
        title: 'image_choice_card.style.content.cute_3d',
        src: '/images/creative/styles/advanced/cute-3d.webp',
      },
      {
        id: 'cartoonish',
        title: 'image_choice_card.style.content.cartoonish',
        src: '/images/creative/styles/advanced/cartoonish.jpeg',
      },
      {
        id: 'stippled',
        title: 'image_choice_card.style.content.stippled',
        src: '/images/creative/styles/advanced/stippled.webp',
      },
      {
        id: 'polaroid',
        title: 'image_choice_card.style.content.polaroid',
        src: '/images/creative/styles/advanced/polaroid.webp',
      },
      {
        id: '3D',
        title: 'image_choice_card.style.content.3D',
        src: '/images/creative/styles/advanced/3D.webp',
      },
      {
        id: 'illustration',
        title: 'image_choice_card.style.content.illustration',
        src: '/images/creative/styles/advanced/illustration.webp',
      },
      {
        id: 'black-and-white',
        title: 'image_choice_card.style.content.black_and_white',
        src: '/images/creative/styles/advanced/black-and-white.webp',
      },
      {
        id: 'painting',
        title: 'image_choice_card.style.content.painting',
        src: '/images/creative/styles/advanced/painting.webp',
      },
      {
        id: 'neon',
        title: 'image_choice_card.style.content.neon',
        src: '/images/creative/styles/advanced/neon.webp',
      },
      {
        id: 'glow',
        title: 'image_choice_card.style.content.glow',
        src: '/images/creative/styles/advanced/glow.webp',
      },
      {
        id: 'funky',
        title: 'image_choice_card.style.content.funky',
        src: '/images/creative/styles/advanced/funky.webp',
      },
      {
        id: 'cartoon-in-reality',
        title: 'image_choice_card.style.content.cartoon_in_reality',
        src: '/images/creative/styles/advanced/cartoon-in-reality.webp',
      },
      {
        id: 'retro',
        title: 'image_choice_card.style.content.retro',
        src: '/images/creative/styles/advanced/retro.webp',
      },
      {
        id: 'sketchy',
        title: 'image_choice_card.style.content.sketchy',
        src: '/images/creative/styles/advanced/sketchy.webp',
      },
      {
        id: 'long-exposure',
        title: 'image_choice_card.style.content.long_exposure',
        src: '/images/creative/styles/advanced/long-exposure.webp',
      },
      {
        id: 'multi-angles',
        title: 'image_choice_card.style.content.multi_angles',
        src: '/images/creative/styles/advanced/multi-angles.webp',
      },
    ],
  },
];

export const imageEffects = [
  {
    category: 'image_choice_card.effects.categories.color',
    content: [
      {
        id: 'pastel',
        title: 'image_choice_card.effects.content.pastel',
        src: '/images/creative/effects/color/pastel.webp',
      },
      {
        id: 'black-and-white',
        title: 'image_choice_card.effects.content.black_and_white',
        src: '/images/creative/effects/color/black-and-white.webp',
      },
      {
        id: 'gold-glow',
        title: 'image_choice_card.effects.content.gold_glow',
        src: '/images/creative/effects/color/gold-glow.webp',
      },
      {
        id: 'vibrant',
        title: 'image_choice_card.effects.content.vibrant',
        src: '/images/creative/effects/color/vibrant.webp',
      },
      {
        id: 'cold-neon',
        title: 'image_choice_card.effects.content.cold_neon',
        src: '/images/creative/effects/color/cold-neon.webp',
      },
    ],
  },
  {
    category: 'image_choice_card.effects.categories.camera',
    content: [
      {
        id: 'portrait',
        title: 'image_choice_card.effects.content.portrait',
        src: '/images/creative/effects/camera/portrait.webp',
      },
      {
        id: 'low-angle',
        title: 'image_choice_card.effects.content.low_angle',
        src: '/images/creative/effects/camera/low-angle.webp',
      },
      {
        id: 'mid-shot',
        title: 'image_choice_card.effects.content.mid_shot',
        src: '/images/creative/effects/camera/mid-shot.webp',
      },
      {
        id: 'wide-shot',
        title: 'image_choice_card.effects.content.wide_shot',
        src: '/images/creative/effects/camera/wide-shot.webp',
      },
      {
        id: 'tilt-shot',
        title: 'image_choice_card.effects.content.tilt_shot',
        src: '/images/creative/effects/camera/tilt-shot.webp',
      },
      {
        id: 'aerial',
        title: 'image_choice_card.effects.content.aerial',
        src: '/images/creative/effects/camera/aerial.webp',
      },
    ],
  },
  {
    category: 'image_choice_card.effects.categories.lighting',
    content: [
      {
        id: 'iridescent',
        title: 'image_choice_card.effects.content.iridescent',
        src: '/images/creative/effects/lighting/iridescent.webp',
      },
      {
        id: 'dramatic',
        title: 'image_choice_card.effects.content.dramatic',
        src: '/images/creative/effects/lighting/dramatic.webp',
      },
      {
        id: 'long-exposure',
        title: 'image_choice_card.effects.content.long_exposure',
        src: '/images/creative/effects/lighting/long-exposure.webp',
      },
      {
        id: 'indoor',
        title: 'image_choice_card.effects.content.indoor',
        src: '/images/creative/effects/lighting/indoor.webp',
      },
      {
        id: 'high-flash',
        title: 'image_choice_card.effects.content.high_flash',
        src: '/images/creative/effects/lighting/high-flash.webp',
      },
      {
        id: 'neon',
        title: 'image_choice_card.effects.content.neon',
        src: '/images/creative/effects/lighting/neon.webp',
      },
    ],
  },
];

export const useCtaTexts = () => {
  const t = useTranslations();

  return [
    {
      label: t(
        'creative_card.form.cta_content.select.cta_texts.save_50_buy_now'
      ),
      value: 'save-50-buy-now',
    },
    {
      label: t('creative_card.form.cta_content.select.cta_texts.get_started'),
      value: 'get-started',
    },
    {
      label: t(
        'creative_card.form.cta_content.select.cta_texts.join_newsletter'
      ),
      value: 'join-newsletter',
    },
    {
      label: t('creative_card.form.cta_content.select.cta_texts.discover_more'),
      value: 'discover-more',
    },
    {
      label: t('creative_card.form.cta_content.select.cta_texts.try_it_now'),
      value: 'try-it-now',
    },
    {
      label: t(
        'creative_card.form.cta_content.select.cta_texts.schedule_free_demo'
      ),
      value: 'schedule-free-demo',
    },
    {
      label: t(
        'creative_card.form.cta_content.select.cta_texts.instant_download'
      ),
      value: 'instant-download',
    },
    {
      label: t('creative_card.form.cta_content.select.cta_texts.secure_cart'),
      value: 'secure-cart',
    },
    {
      label: t(
        'creative_card.form.cta_content.select.cta_texts.subscribe_savings'
      ),
      value: 'subscribe-savings',
    },
    {
      label: t(
        'creative_card.form.cta_content.select.cta_texts.follow_for_updates'
      ),
      value: 'follow-for-updates',
    },
    {
      label: t(
        'creative_card.form.cta_content.select.cta_texts.unlock_potential'
      ),
      value: 'unlock-potential',
    },
    {
      label: t(
        'creative_card.form.cta_content.select.cta_texts.claim_free_trial'
      ),
      value: 'claim-free-trial',
    },
    {
      label: t('creative_card.form.cta_content.select.cta_texts.watch_demo'),
      value: 'watch-demo',
    },
    {
      label: t('creative_card.form.cta_content.select.cta_texts.contact_us'),
      value: 'contact-us',
    },
  ];
};

export const useMarketingStrategies = () => {
  const t = useTranslations();

  return [
    {
      label: t(
        'creative_card.form.marketing_infos.select.marketing_strategy.marketing_strategies_options.aida'
      ),
      value: 'aida',
    },
    {
      label: t(
        'creative_card.form.marketing_infos.select.marketing_strategy.marketing_strategies_options.before_and_after'
      ),
      value: 'before-and-after',
    },
    {
      label: t(
        'creative_card.form.marketing_infos.select.marketing_strategy.marketing_strategies_options.consistency_voice_tone'
      ),
      value: 'consistency-voice-tone',
    },
    {
      label: t(
        'creative_card.form.marketing_infos.select.marketing_strategy.marketing_strategies_options.educational_content'
      ),
      value: 'educational-content',
    },
    {
      label: t(
        'creative_card.form.marketing_infos.select.marketing_strategy.marketing_strategies_options.emotive_appeals'
      ),
      value: 'emotive-appeals',
    },
    {
      label: t(
        'creative_card.form.marketing_infos.select.marketing_strategy.marketing_strategies_options.exclusivity_scarcity'
      ),
      value: 'exclusivity-scarcity',
    },
    {
      label: t(
        'creative_card.form.marketing_infos.select.marketing_strategy.marketing_strategies_options.fomo'
      ),
      value: 'fomo',
    },
    {
      label: t(
        'creative_card.form.marketing_infos.select.marketing_strategy.marketing_strategies_options.interactive_posts'
      ),
      value: 'interactive-posts',
    },
    {
      label: t(
        'creative_card.form.marketing_infos.select.marketing_strategy.marketing_strategies_options.personalization'
      ),
      value: 'personalization',
    },
    {
      label: t(
        'creative_card.form.marketing_infos.select.marketing_strategy.marketing_strategies_options.problem_solution'
      ),
      value: 'problem-solution',
    },
    {
      label: t(
        'creative_card.form.marketing_infos.select.marketing_strategy.marketing_strategies_options.product_descriptions'
      ),
      value: 'product-descriptions',
    },
    {
      label: t(
        'creative_card.form.marketing_infos.select.marketing_strategy.marketing_strategies_options.promotion'
      ),
      value: 'promotion',
    },
    {
      label: t(
        'creative_card.form.marketing_infos.select.marketing_strategy.marketing_strategies_options.retargeting'
      ),
      value: 'retargeting',
    },
    {
      label: t(
        'creative_card.form.marketing_infos.select.marketing_strategy.marketing_strategies_options.storytelling'
      ),
      value: 'storytelling',
    },
    {
      label: t(
        'creative_card.form.marketing_infos.select.marketing_strategy.marketing_strategies_options.testimonial'
      ),
      value: 'testimonial',
    },
    {
      label: t(
        'creative_card.form.marketing_infos.select.marketing_strategy.marketing_strategies_options.trending_topics'
      ),
      value: 'trending-topics',
    },
    {
      label: t(
        'creative_card.form.marketing_infos.select.marketing_strategy.marketing_strategies_options.user_centric_messaging'
      ),
      value: 'user-centric-messaging',
    },
    {
      label: t(
        'creative_card.form.marketing_infos.select.marketing_strategy.marketing_strategies_options.value_proposition'
      ),
      value: 'value-proposition',
    },
    {
      label: t(
        'creative_card.form.marketing_infos.select.marketing_strategy.marketing_strategies_options.visual_storytelling'
      ),
      value: 'visual-storytelling',
    },
  ];
};

export const useLanguages = () => {
  const t = useTranslations();

  return [
    {
      label: t(
        'creative_card.form.advanced_options.select.language.options.english'
      ),
      value: 'english',
    },
    {
      label: t(
        'creative_card.form.advanced_options.select.language.options.french'
      ),
      value: 'french',
    },
    {
      label: t(
        'creative_card.form.advanced_options.select.language.options.arabic'
      ),
      value: 'arabic',
    },
    {
      label: t(
        'creative_card.form.advanced_options.select.language.options.italian'
      ),
      value: 'italian',
    },
    {
      label: t(
        'creative_card.form.advanced_options.select.language.options.spanish'
      ),
      value: 'spanish',
    },
    {
      label: t(
        'creative_card.form.advanced_options.select.language.options.korean'
      ),
      value: 'korean',
    },
  ];
};

export const useToneOfVoices = () => {
  const t = useTranslations();

  return [
    {
      label: t(
        'creative_card.form.advanced_options.select.tone_of_voice.options.formal'
      ),
      value: 'formal',
    },
    {
      label: t(
        'creative_card.form.advanced_options.select.tone_of_voice.options.informal'
      ),
      value: 'informal',
    },
    {
      label: t(
        'creative_card.form.advanced_options.select.tone_of_voice.options.humorous'
      ),
      value: 'humorous',
    },
    {
      label: t(
        'creative_card.form.advanced_options.select.tone_of_voice.options.serious'
      ),
      value: 'serious',
    },
    {
      label: t(
        'creative_card.form.advanced_options.select.tone_of_voice.options.optimistic'
      ),
      value: 'optimistic',
    },
    {
      label: t(
        'creative_card.form.advanced_options.select.tone_of_voice.options.motivating'
      ),
      value: 'motivating',
    },
    {
      label: t(
        'creative_card.form.advanced_options.select.tone_of_voice.options.respectful'
      ),
      value: 'respectful',
    },
    {
      label: t(
        'creative_card.form.advanced_options.select.tone_of_voice.options.assertive'
      ),
      value: 'assertive',
    },
    {
      label: t(
        'creative_card.form.advanced_options.select.tone_of_voice.options.conversational'
      ),
      value: 'conversational',
    },
    {
      label: t(
        'creative_card.form.advanced_options.select.tone_of_voice.options.persuasive'
      ),
      value: 'persuasive',
    },
    {
      label: t(
        'creative_card.form.advanced_options.select.tone_of_voice.options.inspirational'
      ),
      value: 'inspirational',
    },
    {
      label: t(
        'creative_card.form.advanced_options.select.tone_of_voice.options.educational'
      ),
      value: 'educational',
    },
    {
      label: t(
        'creative_card.form.advanced_options.select.tone_of_voice.options.emotional'
      ),
      value: 'emotional',
    },
    {
      label: t(
        'creative_card.form.advanced_options.select.tone_of_voice.options.dramatic'
      ),
      value: 'dramatic',
    },
  ];
};
