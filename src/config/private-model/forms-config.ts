import {
  SaveImageModelSchema,
  PrepareTextModelSchema,
  PrepareImageModelSchema,
} from '@/api/models/schemas/private-model.schema';
import { formOptions } from '@tanstack/react-form';

export const privateModelTextFormOpts = formOptions({
  defaultValues: {
    CompanyId: '',
    BrandId: '',
    UserEmail: '',
    ModelName: '',
    Description: '',
    Tags: [''],
    SocialMediaData: [
      {
        platform: 'facebook',
        pageName: '',
      },
    ],
  } as PrepareTextModelSchema,
});

export const preparePrivateModelImageFormOpts = formOptions({
  defaultValues: {
    files: [],
    modelRequest: {
      CompanyId: '',
      BrandId: '',
      UserEmail: '',
      ModelName: '',
      Description: '',
      Tags: [''],
      ImageUrlsUpload: [],
      Type: '',
      Keyword: '',
      Object: '',
    },
  } as PrepareImageModelSchema,
});

export const createPrivateModelImageFormOpts = formOptions({
  defaultValues: {
    CompanyId: '',
    BrandId: '',
    UserEmail: '',
    ModelName: '',
    Description: '',
    Tags: [''],
    ImageUrlsUpload: [],
    ImageList: [],
    Type: '',
    Keyword: '',
    Object: '',
    BaseModel: '',
    ModelId: '',
    DummyRequest: false,
  } as SaveImageModelSchema,
});
