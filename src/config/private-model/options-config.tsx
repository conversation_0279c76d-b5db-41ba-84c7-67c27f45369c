import React from 'react';

import { Icons } from '@/components/shared/icons';

const imageCategories = [
  {
    label: 'Animals',
    value: 'animals',
  },
  {
    label: 'Beverages',
    value: 'beverages',
  },
  {
    label: 'Cars',
    value: 'cars',
  },
  {
    label: 'Clothes',
    value: 'clothes',
  },
  {
    label: 'Cosmetics',
    value: 'cosmetics',
  },
  {
    label: 'Electronics',
    value: 'electronics',
  },
  {
    label: 'Foods',
    value: 'foods',
  },
  {
    label: 'Furniture',
    value: 'furniture',
  },
  {
    label: 'Home Decor',
    value: 'home-decor',
  },
  {
    label: 'Object',
    value: 'object',
  },
  {
    label: 'Person',
    value: 'person',
  },
  {
    label: 'Pets',
    value: 'pets',
  },
  {
    label: 'Shoes',
    value: 'shoes',
  },
  {
    label: 'Styles',
    value: 'styles',
  },
  {
    label: 'Other',
    value: 'other',
  },
];

const socialMediaOptions = [
  {
    label: 'Facebook',
    value: 'facebook',
    icon: <Icons.facebook className='size-5 text-primary' />,
  },
  {
    label: 'Instagram',
    value: 'instagram',
    icon: <Icons.instagram className='size-5 text-rose' />,
  },
  {
    label: 'LinkedIn',
    value: 'linkedin',
    icon: <Icons.linkedin className='size-5 text-sky-500' />,
  },
  {
    label: 'X',
    value: 'x',
    icon: <Icons.x className='size-5 text-black' />,
  },
];

export function usePrivateModelOptionsConfig() {
  return {
    imageCategories,
    socialMediaOptions,
  };
}
