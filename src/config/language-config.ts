import { useTranslations } from 'next-intl';
import React from 'react';

type languageItem = {
  code: string;
  title: string;
  image: string;
  icon?: React.ReactNode;
};

type TFunction = ReturnType<typeof useTranslations>;

export function languageConfig(t: TFunction): languageItem[] {
  return [
    {
      code: 'en',
      title: t('english'),
      image: '/flags/gb.png',
    },
    {
      code: 'fr',
      title: t('french'),
      image: '/flags/fr.png',
    },
    {
      code: 'ar',
      title: t('arabic'),
      image: '/flags/sa.png',
    },
    {
      code: 'it',
      title: t('italian'),
      image: '/flags/it.png',
    },
    {
      code: 'de',
      title: t('german'),
      image: '/flags/de.png',
    },
  ];
}
