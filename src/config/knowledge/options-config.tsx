import { useTranslations } from 'next-intl';

export const contentTabs = ['text', 'url', 'file', 'drive'] as const;
export type TContentTab = (typeof contentTabs)[number] | null;

export const externalDriveTabs = [
  'google-drive',
  'share-point',
  'airtable',
] as const;
export type TExternalDriveTab = (typeof externalDriveTabs)[number] | null;

export const mediaTabs = ['logo', 'color'] as const;
export type TMediaTab = (typeof mediaTabs)[number] | null;

export function useColorType() {
  return {
    text_plain: 'bg-blue-900',
    text_doc: 'bg-blue-900',
    text_website: 'bg-blue-900',
    audience: 'bg-[#C48E86]',
    media_logo: 'bg-[#E7D2CF]',
    media_color: 'bg-[#E7D2CF]',
  };
}

export function useTagType() {
  const t = useTranslations();
  return {
    text_plain: {
      label: t('text'),
      color: 'bg-blue-900',
      textClass: 'text-white',
    },
    text_doc: {
      label: t('file'),
      color: 'bg-blue-900',
      textClass: 'text-white',
    },
    text_website: {
      label: t('website'),
      color: 'bg-blue-900',
      textClass: 'text-white',
    },
    audience: {
      label: t('audience'),
      color: 'bg-[#C48E86]',
      textClass: 'text-white',
    },
    media_logo: {
      label: t('logo'),
      color: 'bg-[#E7D2CF]',
      textClass: 'text-blue-950',
    },
    media_color: {
      label: t('color'),
      color: 'bg-[#E7D2CF]',
      textClass: 'text-blue-950',
    },
  };
}

export function useGenderOptions() {
  const t = useTranslations();

  return [
    {
      label: t('audience_form.gender_options.male'),
      value: 'male',
    },
    {
      label: t('audience_form.gender_options.female'),
      value: 'female',
    },
    {
      label: t('audience_form.gender_options.neutral'),
      value: 'neutral',
    },
  ];
}

export function useSpendingBehaviorOptionsConfig() {
  const t = useTranslations();
  return [
    {
      label: t('audience_form.spending_behavior_options.budget_conscious'),
      value: 'Budget-conscious',
    },
    {
      label: t('audience_form.spending_behavior_options.value_oriented'),
      value: 'Value-oriented',
    },
    {
      label: t('audience_form.spending_behavior_options.aspirational'),
      value: 'Aspirational',
    },
    {
      label: t('audience_form.spending_behavior_options.luxury'),
      value: 'Luxury',
    },
    {
      label: t('audience_form.spending_behavior_options.experiential'),
      value: 'Experiential',
    },
    {
      label: t('audience_form.spending_behavior_options.impulsive'),
      value: 'Impulsive',
    },
  ];
}
