import { FilterConfig } from '@/components/shared/table/data-table-filter-options';
import { SearchConfig } from '@/components/shared/table/data-table-search-options';

export function useKnowledgeTableOptionsConfig() {
  const searchConfig: SearchConfig = {
    columns: ['Name'],
    placeholder: 'Search',
  };

  const filterConfig: FilterConfig[] = [
    {
      column: 'Type',
      type: 'select',
      label: 'Knowledge Type',
      options: [
        { label: 'Text', value: 'text_plain' },
        { label: 'Website', value: 'text_website' },
        { label: 'Doc', value: 'text_doc' },
        { label: 'Audience', value: 'audience' },
        { label: 'Logo', value: 'media_logo' },
        { label: 'Color', value: 'media_color' },
      ],
    },
  ];

  return {
    searchConfig,
    filterConfig,
  };
}
