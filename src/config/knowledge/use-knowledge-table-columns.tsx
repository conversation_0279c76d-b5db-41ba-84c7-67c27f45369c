'use client';

import type { ColumnDef } from '@tanstack/react-table';

import { useTranslations } from 'next-intl';

import { KnowledgeResponse } from '@/api/models/dtos/knowledge.dto';

import { cn } from '@/lib/utils';

import { useFormatDate } from '@/hooks/use-date-fns-locale';

import {
  useDeleteKnowledge,
  useMarkKnowledgeAsFavorite,
} from '@/api/hooks/knowledge/mutations';
import { useKnowledgeStore } from '@/stores/use-knowledge-store';

import { useKnowledgeOptionsConfig } from '@/config/knowledge/use-knowledge-options-config';

import { Icons } from '@/components/shared/icons';
import { Button } from '@/components/ui/button';

export const useKnowledgeColumns = () => {
  const t = useTranslations();
  const { format } = useFormatDate();

  const { colorType } = useKnowledgeOptionsConfig();

  const { setDetailOpen, setKnowledgeItem } = useKnowledgeStore();

  const { mutateAsync: handleStarClick } = useMarkKnowledgeAsFavorite();
  const { mutateAsync: handleDeleteClick } = useDeleteKnowledge();

  const { tagType } = useKnowledgeOptionsConfig();

  const columns: ColumnDef<KnowledgeResponse>[] = [
    {
      header: t('name'),
      accessorKey: 'Name',
      cell: ({ row }) => {
        const type = row.getValue('Type') as keyof typeof colorType;
        const colorBar = colorType[type] || 'bg-gray-500';

        return (
          <div className='flex items-center'>
            {type && (
              <div className={cn('mx-2 h-6 w-2 rounded-full', colorBar)} />
            )}
            <div>{row.getValue('Name')}</div>
          </div>
        );
      },
      size: 200,
      enableHiding: false,
    },
    {
      header: t('creation_Date'),
      accessorKey: 'CreationTimestamp',
      size: 120,
      cell: ({ row }) => {
        return format(new Date(row.getValue('CreationTimestamp')), 'PP');
      },
    },
    {
      header: t('type'),
      accessorKey: 'Type',
      cell: ({ row }) => {
        const type = row.getValue('Type') as keyof typeof tagType;
        const tag = tagType[type] || {
          label: row.getValue('Type'),
          color: 'bg-gray-500',
          textClass: 'text-white',
        };

        return (
          <div
            className={cn(
              'inline-block rounded-3xl px-3 py-1 text-xs',
              tag.color,
              {
                'text-white':
                  type === 'text_plain' ||
                  type === 'text_website' ||
                  type === 'text_doc' ||
                  type === 'audience',
                'text-blue-950':
                  type === 'media_logo' || type === 'media_color',
              }
            )}
          >
            {tag.label}
          </div>
        );
      },
      size: 100,
    },
    {
      header: t('created_by'),
      accessorKey: 'UserEmail',
      size: 180,
    },
    {
      id: 'actions',
      header: t('actions'),
      cell: ({ row }) => {
        const isFavorite = row.original.Favorite;

        return (
          <div className='flex items-center gap-2'>
            <Button
              variant='ghost'
              size='setting'
              className='text-muted-foreground hover:text-primary'
              onClick={() => {
                setKnowledgeItem(row.original);
                setDetailOpen(true);
              }}
            >
              <Icons.eye />
            </Button>

            <Button
              variant='ghost'
              size='setting'
              className={cn({
                'text-muted-foreground hover:text-[#F5A524]': !isFavorite,
                'text-[#F5A524] hover:text-muted-foreground': isFavorite,
              })}
              onClick={async () => await handleStarClick(row.original.Id)}
            >
              <Icons.star fill={isFavorite ? 'currentColor' : 'white'} />
            </Button>

            <Button
              variant='ghost'
              size='icon'
              className='size-8 text-muted-foreground hover:text-destructive'
              onClick={async () => await handleDeleteClick(row.original.Id)}
            >
              <Icons.trash />
            </Button>
          </div>
        );
      },
      size: 60,
      enableHiding: false,
    },
  ];

  return columns;
};
