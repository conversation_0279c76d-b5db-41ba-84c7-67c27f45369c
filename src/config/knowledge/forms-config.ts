import {
  AudienceSchema,
  CreateKnowledgeLogoSchema,
  CreateKnowledgeTextSchema,
  CreateKnowledgeFileSchema,
  UpdateKnowledgeTextSchema,
  UpdateKnowledgeFileSchema,
  UpdateKnowledgeLogoSchema,
  SelectTableAirtableSchema,
  SelectFileSharepointSchema,
  CreateKnowledgeColorSchema,
  UpdateKnowledgeColorSchema,
  SelectFileGoogleDriveSchema,
  CreateKnowledgeWebsiteSchema,
  UpdateKnowledgeWebsiteSchema,
  UpdateKnowledgeAudienceSchema,
  KnowledgeScrapingWebsiteSchema,
} from '@/api/models/schemas/knowledge.schema';
import { formOptions } from '@tanstack/react-form';

export const textKnowledgeFormOpts = formOptions({
  defaultValues: {
    knowledge: {
      CompanyId: '',
      BrandId: '',
      UserEmail: '',
      Type: 'text_plain',
      Name: '',
      Text: {
        Title: '',
        Description: '',
      },
    },
  } as CreateKnowledgeTextSchema,
});

export const textKnowledgeFormUpdateOpts = formOptions({
  defaultValues: {
    knowledge: {
      Id: '',
      CompanyId: '',
      BrandId: '',
      UserEmail: '',
      Type: 'text_plain',
      Name: '',
      Text: {
        Title: '',
        Description: '',
      },
    },
  } as UpdateKnowledgeTextSchema,
});

export const prepareUrlContentFormOpts = formOptions({
  defaultValues: {
    title: '',
    url: '',
  } as KnowledgeScrapingWebsiteSchema,
});

export const websiteKnowledgeFormOpts = formOptions({
  defaultValues: {
    knowledge: {
      CompanyId: '',
      BrandId: '',
      UserEmail: '',
      Type: 'text_website',
      Name: '',
      Website: {
        Title: '',
        Link: '',
        Description: '',
      },
    },
  } as CreateKnowledgeWebsiteSchema,
});

export const websiteKnowledgeFormUpdateOpts = formOptions({
  defaultValues: {
    knowledge: {
      Id: '',
      CompanyId: '',
      BrandId: '',
      UserEmail: '',
      Type: 'text_website',
      Name: '',
      Website: {
        Title: '',
        Link: '',
        Description: '',
      },
    },
  } as UpdateKnowledgeWebsiteSchema,
});

export const fileKnowledgeFormOpts = formOptions({
  defaultValues: {
    knowledge: {
      Name: '',
      UserEmail: '',
      CompanyId: '',
      BrandId: '',
      Type: 'text_doc',
      Doc: {
        Title: '',
        Type: 'pdf',
      },
    },
  } as CreateKnowledgeFileSchema,
});
export const fileKnowledgeFormUpdateOpts = formOptions({
  defaultValues: {
    knowledge: {
      Id: '',
      Name: '',
      UserEmail: '',
      CompanyId: '',
      BrandId: '',
      Type: 'text_doc',
      Doc: {
        Title: '',
        Type: 'pdf',
      },
    },
  } as UpdateKnowledgeFileSchema,
});

export const logoKnowledgeFormOpts = formOptions({
  defaultValues: {
    knowledge: {
      CompanyId: '',
      BrandId: '',
      UserEmail: '',
      Type: 'media_logo',
      Name: '',
      Logo: {
        Name: '',
      },
    },
  } as CreateKnowledgeLogoSchema,
});

export const logoKnowledgeFormUpdateOpts = formOptions({
  defaultValues: {
    knowledge: {
      Id: '',
      CompanyId: '',
      BrandId: '',
      UserEmail: '',
      Type: 'media_logo',
      Name: '',
      Logo: {
        Name: '',
      },
    },
    file: undefined,
  } as unknown as UpdateKnowledgeLogoSchema,
});

export const colorKnowledgeFormOpts = formOptions({
  defaultValues: {
    knowledge: {
      CompanyId: '',
      BrandId: '',
      UserEmail: '',
      Type: 'media_color',
      Name: '',
      Color: [
        { Index: 0, ColorHex: '' },
        { Index: 1, ColorHex: '' },
        { Index: 2, ColorHex: '' },
        { Index: 3, ColorHex: '' },
        { Index: 4, ColorHex: '' },
        { Index: 5, ColorHex: '' },
      ],
    },
  } as CreateKnowledgeColorSchema,
});
export const colorKnowledgeFormUpdateOpts = formOptions({
  defaultValues: {
    knowledge: {
      Id: '',
      CompanyId: '',
      BrandId: '',
      UserEmail: '',
      Type: 'media_color',
      Name: '',
      Color: [
        { Index: 0, ColorHex: '' },
        { Index: 1, ColorHex: '' },
        { Index: 2, ColorHex: '' },
        { Index: 3, ColorHex: '' },
        { Index: 4, ColorHex: '' },
        { Index: 5, ColorHex: '' },
      ],
    },
  } as UpdateKnowledgeColorSchema,
});

export const audienceKnowledgeFormOpts = formOptions({
  defaultValues: {
    knowledge: {
      CompanyId: '',
      BrandId: '',
      UserEmail: '',
      Type: 'audience',
      Name: '',
      Audience: {
        Name: '',
        AgeRange: [25, 45],
        Business: false,
      },
    },
  } as AudienceSchema,
});

export const audienceKnowledgeFormUpdateOpts = formOptions({
  defaultValues: {
    knowledge: {
      Id: '',
      CompanyId: '',
      BrandId: '',
      UserEmail: '',
      Type: 'audience',
      Name: '',
      Audience: {
        AgeRange: [25, 45],
        Name: '',
        Business: false,
        Gender: 'male',
        InterestAndPref: [],
        SpendingBehavior: [],
        BusinessIndustry: [],
      },
    },
  } as UpdateKnowledgeAudienceSchema,
});

export const sharepointKnowledgeFormOpts = formOptions({
  defaultValues: {
    CompanyId: '',
    BrandId: '',
    AccountEmail: '',
    Title: '',
    SiteId: '',
    DriveId: '',
    FolderId: '',
    FileName: '',
    FileId: '',
  } as SelectFileSharepointSchema,
});

export const airtableKnowledgeFormOpts = formOptions({
  defaultValues: {
    CompanyId: '',
    BrandId: '',
    AccountEmail: '',
    Title: '',
    BaseId: '',
    TableId: '',
    TableName: '',
  } as SelectTableAirtableSchema,
});

export const googleDriveKnowledgeFormOpts = formOptions({
  defaultValues: {
    CompanyId: '',
    BrandId: '',
    AccountEmail: '',
    Title: '',
    FileId: '',
    FileName: '',
  } as SelectFileGoogleDriveSchema,
});
