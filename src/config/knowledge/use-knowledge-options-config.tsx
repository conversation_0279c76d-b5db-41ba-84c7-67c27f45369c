import {
  mediaTabs,
  useTagType,
  contentTabs,
  useColorType,
  useGenderOptions,
  externalDriveTabs,
  useSpendingBehaviorOptionsConfig,
} from '@/config/knowledge/options-config';

export function useKnowledgeOptionsConfig() {
  return {
    contentTabs,
    externalDriveTabs,
    mediaTabs,
    genderOptions: useGenderOptions(),
    spendingBehaviorOptions: useSpendingBehaviorOptionsConfig(),
    colorType: useColorType(),
    tagType: useTagType(),
  };
}
