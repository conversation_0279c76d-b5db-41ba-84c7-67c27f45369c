'use client';

import { useTranslations } from 'next-intl';

import {
  Ban,
  Heart,
  Share2,
  LucideIcon,
  Link as LinkIcon,
  MessageSquareText,
} from 'lucide-react';

export interface StatCardConfig {
  name: string;
  icon: LucideIcon;
  bgColor: string;
  textColor: string;
}

export enum StatCardKeys {
  'TOTAL_LIKES' = 'Total Likes',
  'TOTAL_COMMENTS' = 'Total Comments',
  'TOTAL_SHARES' = 'Total Shares',
  'LINKS_CLICKED' = 'Links Clicked',
  default = 'default',
}

type TFunction = ReturnType<typeof useTranslations>;
type StatCardsConfigType = Record<StatCardKeys, StatCardConfig>;

export function useStatCardsConfig(t: TFunction): StatCardsConfigType {
  return {
    [StatCardKeys.TOTAL_LIKES]: {
      name: t('stat_cards.total_likes'),
      icon: Heart,
      bgColor: 'bg-rose/15',
      textColor: 'text-rose',
    },
    [StatCardKeys.TOTAL_COMMENTS]: {
      name: t('stat_cards.total_comments'),
      icon: MessageSquareText,
      bgColor: 'bg-success/15',
      textColor: 'text-success',
    },
    [StatCardKeys.TOTAL_SHARES]: {
      name: t('stat_cards.total_shares'),
      icon: Share2,
      bgColor: 'bg-yellow/15',
      textColor: 'text-yellow',
    },
    [StatCardKeys.LINKS_CLICKED]: {
      name: t('stat_cards.links_clicked'),
      icon: LinkIcon,
      bgColor: 'bg-violet/15',
      textColor: 'text-violet',
    },
    [StatCardKeys.default]: {
      name: t('stat_cards.default'),
      icon: Ban,
      bgColor: 'bg-muted',
      textColor: 'text-accent',
    },
  };
}
