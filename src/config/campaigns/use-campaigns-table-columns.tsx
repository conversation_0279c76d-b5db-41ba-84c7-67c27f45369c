'use client';

import type { ColumnDef } from '@tanstack/react-table';

import { format } from 'date-fns';
import React from 'react';

import { CampaignResponse } from '@/api/models/dtos/compaign.dto';

import { filterByDate } from '@/lib/filter-by-date';

import { StatusBadge } from '@/components/campaigns/atoms/status-badge';
import { Icons } from '@/components/shared/icons';
import { Button } from '@/components/ui/button';

export const useCampaignsTableColumns = () => {
  const columnsDef: ColumnDef<CampaignResponse>[] = [
    {
      header: 'Name',
      accessorKey: 'Name',
      size: 100,
    },
    {
      header: 'Platform',
      accessorKey: 'Platforms',
      size: 200,
    },
    {
      header: 'Status',
      accessorKey: 'Status',
      size: 80,
      cell: ({ row }) => <StatusBadge status={row.getValue('Status')} />,
    },
    {
      header: 'Objective',
      accessorKey: 'Objective',
      size: 100,
      enableSorting: false,
    },
    {
      header: 'Total View',
      accessorKey: 'ViewsStat',
      size: 80,
      cell: ({ row }) => {
        const value: CampaignResponse['ViewsStat'] = row.getValue('ViewsStat');

        return (
          <div className='flex w-fit items-center justify-center gap-1 rounded-xl bg-[#D7E4F8] px-2 py-1.5 text-[#2C5EB0]'>
            <Icons.arrowRight className='size-4' />
            {value.Trend.toFixed(1)}%
          </div>
        );
      },
      enableSorting: false,
    },
    {
      header: 'Total Interactions',
      accessorKey: 'InteractionStat',
      size: 80,
      cell: ({ row }) => {
        const value: CampaignResponse['InteractionStat'] =
          row.getValue('InteractionStat');

        return (
          <div className='flex w-fit items-center justify-center gap-1 rounded-xl bg-[#E9D5EF] px-2 py-1.5 text-[#694875]'>
            <Icons.arrowRight className='size-4' />
            {value.Trend.toFixed(1)}%
          </div>
        );
      },
      enableSorting: false,
    },
    {
      header: 'Total Reach',
      accessorKey: 'ReachStat',
      size: 80,
      cell: ({ row }) => {
        const value: CampaignResponse['ReachStat'] = row.getValue('ReachStat');

        return (
          <div className='flex w-fit items-center justify-center gap-1 rounded-xl bg-[#17C96433] px-2 py-1.5 text-[#17C964]'>
            <Icons.arrowRight className='size-4' />
            {value.Trend.toFixed(1)}%
          </div>
        );
      },
      enableSorting: false,
    },
    {
      header: 'Creation Date',
      accessorKey: 'CreationTimestamp',
      size: 120,
      cell: ({ row }) => {
        return format(new Date(row.getValue('CreationTimestamp')), 'PPP');
      },
      filterFn: filterByDate(),
      enableSorting: false,
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: () => {
        return (
          <div className='flex items-center gap-1'>
            <Button
              variant='ghost'
              size='setting'
              className='text-gray-400 hover:text-primary'
            >
              <Icons.eye />
            </Button>

            <Button
              variant='ghost'
              size='icon'
              className='size-8 text-muted-foreground hover:text-destructive'
            >
              <Icons.trash />
            </Button>
          </div>
        );
      },
      size: 60,
    },
  ];
  return { columnsDef };
};
