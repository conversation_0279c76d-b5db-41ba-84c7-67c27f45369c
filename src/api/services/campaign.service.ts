import { CampaignResponse } from '@/api/models/dtos/compaign.dto';

import { timeStringToMilliseconds } from '@/lib/time-to-milliseconds';

import { apiGet, apiPost } from '@/api/config/client';
import { ENDPOINTS } from '@/api/config/endpoints';
import {
  CreateCampaignSchema,
  CreateAICampaignSchema,
} from '@/api/models/schemas/campaign.schema';

export const campaignService = {
  async getAllByCompanyBrandId(companyId?: string, brandId?: string) {
    if (!companyId || !brandId) {
      throw new Error('companyId and brandId are required');
    }

    return await apiGet<CampaignResponse[]>({
      endpoint: ENDPOINTS.CAMPAIGNS.ALL_BY_COMPANY_BRAND_ID(companyId, brandId),
    });
  },

  async getCampaignById(campaignId?: string) {
    if (!campaignId) {
      throw new Error('campaignId is required');
    }

    return await apiGet<CampaignResponse>({
      endpoint: ENDPOINTS.CAMPAIGNS.BY_ID(campaignId),
    });
  },

  async createCampaign(payload: CreateCampaignSchema) {
    return await apiPost<CampaignResponse>({
      endpoint: ENDPOINTS.CAMPAIGNS.CREATE,
      payload,
    });
  },

  async generateAICampaignProposals(payload: CreateAICampaignSchema) {
    return await apiPost<CampaignResponse[], CreateAICampaignSchema>({
      endpoint: ENDPOINTS.CAMPAIGNS.CREATE_AI_PROPOSALS,
      payload,
      config: {
        timeout: timeStringToMilliseconds({ timeString: '1min' }),
      },
    });
  },
};
