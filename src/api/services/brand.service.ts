import { BrandResponse } from '@/api/models/dtos/brand.dto';

import { apiGet } from '@/api/config/client';
import { ENDPOINTS } from '@/api/config/endpoints';

export const brandService = {
  async getAllByCompanyId(companyId?: string) {
    if (!companyId) {
      throw new Error('companyId is required');
    }
    return await apiGet<BrandResponse[]>({
      endpoint: ENDPOINTS.BRANDS.ALL_BY_COMPANY_ID(companyId),
    });
  },

  async getBrandById(brandId?: string) {
    if (!brandId) {
      throw new Error('brandId is required');
    }
    return await apiGet<BrandResponse>({
      endpoint: ENDPOINTS.BRANDS.BY_ID(brandId),
    });
  },
};
