import {
  AirtableResponse,
  KnowledgeResponse,
  SharePointResponse,
  GoogleDriveResponse,
  PrepareUrlContentKnowledgeResponse,
} from '@/api/models/dtos/knowledge.dto';

import { API_URL } from '@/api/config/axios-instance';
import { apiGet, apiPost, apiDelete } from '@/api/config/client';
import { ENDPOINTS } from '@/api/config/endpoints';
import {
  CreateKnowledgeFileSchema,
  CreateKnowledgeLogoSchema,
  CreateKnowledgeTextSchema,
  UpdateKnowledgeTextSchema,
  UpdateKnowledgeLogoSchema,
  UpdateKnowledgeFileSchema,
  SelectTableAirtableSchema,
  SelectFileSharepointSchema,
  CreateKnowledgeColorSchema,
  UpdateKnowledgeColorSchema,
  SelectFileGoogleDriveSchema,
  CreateKnowledgeWebsiteSchema,
  UpdateKnowledgeWebsiteSchema,
  CreateKnowledgeAudienceSchema,
  KnowledgeScrapingWebsiteSchema,
  KnowledgeAudienceUpdatedSchema,
} from '@/api/models/schemas/knowledge.schema';

export const knowledgeService = {
  async getAllByCompanyBrandId(companyId?: string, brandId?: string) {
    if (!companyId || !brandId) {
      throw new Error('companyId and brandId are required');
    }

    return await apiGet<KnowledgeResponse[]>({
      endpoint: ENDPOINTS.KNOWLEDGE.ALL_BY_COMPANY_BRAND_ID(companyId, brandId),
    });
  },

  async prepareUrlContentKnowledge(payload: KnowledgeScrapingWebsiteSchema) {
    return await apiPost<PrepareUrlContentKnowledgeResponse>({
      endpoint: ENDPOINTS.KNOWLEDGE.GET_WEBSITE_URL_SCRAPER,
      payload: payload,
      config: {
        baseURL: process.env.NEXT_PUBLIC_BACKEND_NEXT_SERVER_URL,
        timeout: 120000,
      },
    });
  },

  async createTextKnowledge(payload: CreateKnowledgeTextSchema) {
    const formData = new FormData();

    formData.append('knowledge', JSON.stringify(payload.knowledge));

    return await apiPost<KnowledgeResponse>({
      endpoint: ENDPOINTS.KNOWLEDGE.CREATE_KNOWLEDGE,
      payload: formData,
      config: {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      },
    });
  },

  async createWebsiteKnowledge(payload: CreateKnowledgeWebsiteSchema) {
    const formData = new FormData();

    formData.append('knowledge', JSON.stringify(payload.knowledge));

    return await apiPost<KnowledgeResponse>({
      endpoint: ENDPOINTS.KNOWLEDGE.CREATE_KNOWLEDGE,
      payload: formData,
      config: {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      },
    });
  },

  async createFileKnowledge(payload: CreateKnowledgeFileSchema) {
    const formData = new FormData();

    formData.append('knowledge', JSON.stringify(payload.knowledge));
    formData.append('file', payload.file);

    return await apiPost<KnowledgeResponse>({
      endpoint: ENDPOINTS.KNOWLEDGE.CREATE_KNOWLEDGE,
      payload: formData,
      config: {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      },
    });
  },

  async createLogoKnowledge(payload: CreateKnowledgeLogoSchema) {
    const formData = new FormData();

    formData.append('knowledge', JSON.stringify(payload.knowledge));
    formData.append('file', payload.file);

    return await apiPost<KnowledgeResponse>({
      endpoint: ENDPOINTS.KNOWLEDGE.CREATE_KNOWLEDGE,
      payload: formData,
      config: {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      },
    });
  },

  async createColorKnowledge(payload: CreateKnowledgeColorSchema) {
    const formData = new FormData();

    formData.append('knowledge', JSON.stringify(payload.knowledge));

    return await apiPost<KnowledgeResponse>({
      endpoint: ENDPOINTS.KNOWLEDGE.CREATE_KNOWLEDGE,
      payload: formData,
      config: {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      },
    });
  },

  async createAudienceKnowledge(payload: CreateKnowledgeAudienceSchema) {
    const formData = new FormData();

    formData.append('knowledge', JSON.stringify(payload.knowledge));

    return await apiPost<KnowledgeResponse>({
      endpoint: ENDPOINTS.KNOWLEDGE.CREATE_KNOWLEDGE,
      payload: formData,
      config: {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      },
    });
  },

  async updateTextKnowledge(payload: UpdateKnowledgeTextSchema) {
    const formData = new FormData();

    formData.append('knowledge', JSON.stringify(payload.knowledge));

    return await apiPost({
      endpoint: ENDPOINTS.KNOWLEDGE.UPDATE_KNOWLEDGE,
      payload: formData,
      config: {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      },
    });
  },

  async updateWebsiteKnowledge(payload: UpdateKnowledgeWebsiteSchema) {
    const formData = new FormData();

    formData.append('knowledge', JSON.stringify(payload.knowledge));

    return await apiPost({
      endpoint: ENDPOINTS.KNOWLEDGE.UPDATE_KNOWLEDGE,
      payload: formData,
      config: {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      },
    });
  },

  async updateFileKnowledge(payload: UpdateKnowledgeFileSchema) {
    const formData = new FormData();

    formData.append('knowledge', JSON.stringify(payload.knowledge));
    if (payload.file) {
      formData.append('file', payload.file);
    }

    return await apiPost({
      endpoint: ENDPOINTS.KNOWLEDGE.UPDATE_KNOWLEDGE,
      payload: formData,
      config: {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      },
    });
  },

  async updateLogoKnowledge(payload: UpdateKnowledgeLogoSchema) {
    const formData = new FormData();

    formData.append('knowledge', JSON.stringify(payload.knowledge));
    if (payload.file) {
      formData.append('file', payload.file);
    }

    return await apiPost({
      endpoint: ENDPOINTS.KNOWLEDGE.UPDATE_KNOWLEDGE,
      payload: formData,
      config: {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      },
    });
  },

  async updateColorKnowledge(payload: UpdateKnowledgeColorSchema) {
    const formData = new FormData();

    formData.append('knowledge', JSON.stringify(payload.knowledge));

    return await apiPost({
      endpoint: ENDPOINTS.KNOWLEDGE.UPDATE_KNOWLEDGE,
      payload: formData,
      config: {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      },
    });
  },

  async updateAudienceKnowledge(payload: KnowledgeAudienceUpdatedSchema) {
    const formData = new FormData();

    formData.append('knowledge', JSON.stringify(payload.knowledge));

    return await apiPost({
      endpoint: ENDPOINTS.KNOWLEDGE.UPDATE_KNOWLEDGE,
      payload: formData,
      config: {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      },
    });
  },

  async markKnowledgeAsFavorite(knowledgeId?: string) {
    if (!knowledgeId) {
      throw new Error('privateModelId is required');
    }

    return await apiPost<KnowledgeResponse>({
      endpoint: ENDPOINTS.KNOWLEDGE.MARK_FAVORITE(knowledgeId),
    });
  },

  async deleteKnowledge(knowledgeId?: string) {
    if (!knowledgeId) {
      throw new Error('knowloadgeId is required');
    }

    return await apiDelete<KnowledgeResponse>({
      endpoint: ENDPOINTS.KNOWLEDGE.DELETE(knowledgeId),
    });
  },

  //sharepoint
  async getAccountsSharepoint(companyId?: string, brandId?: string) {
    if (!brandId) {
      throw new Error('brandId is required');
    }

    if (!companyId) {
      throw new Error('companyId is required');
    }

    return await apiPost<string[]>({
      endpoint: ENDPOINTS.KNOWLEDGE.SHAREPOINT_ACCOUNTS,
      payload: {
        companyId,
        brandId,
      },
    });
  },

  async getSitesSharepoint(
    companyId?: string,
    brandId?: string,
    accountEmail?: string
  ) {
    return await apiPost<SharePointResponse[]>({
      endpoint: ENDPOINTS.KNOWLEDGE.SHAREPOINT_SITES,
      payload: {
        companyId,
        brandId,
        accountEmail,
      },
    });
  },

  async getDrivesSharepoint(
    companyId?: string,
    brandId?: string,
    accountEmail?: string,
    siteId?: string
  ) {
    return await apiPost<SharePointResponse[]>({
      endpoint: ENDPOINTS.KNOWLEDGE.SHAREPOINT_DRIVES,
      payload: {
        companyId,
        brandId,
        accountEmail,
        siteId,
      },
    });
  },

  async getFoldersSharepoint(
    companyId?: string,
    brandId?: string,
    accountEmail?: string,
    siteId?: string,
    driveId?: string
  ) {
    return await apiPost<SharePointResponse[]>({
      endpoint: ENDPOINTS.KNOWLEDGE.SHAREPOINT_FOLDERS,
      payload: { companyId, brandId, accountEmail, siteId, driveId },
    });
  },

  async getFilesSharepoint(
    companyId?: string,
    brandId?: string,
    accountEmail?: string,
    siteId?: string,
    driveId?: string,
    folderId?: string
  ) {
    return await apiPost<SharePointResponse[]>({
      endpoint: ENDPOINTS.KNOWLEDGE.SHAREPOINT_FILES,
      payload: { companyId, brandId, accountEmail, siteId, driveId, folderId },
    });
  },

  async selectFileSharepoint(payload: SelectFileSharepointSchema) {
    return await apiPost<KnowledgeResponse>({
      endpoint: ENDPOINTS.KNOWLEDGE.SHAREPOINT_SELECT_FILE,
      payload: payload,
    });
  },

  async revokeSharepoint(
    companyId?: string,
    brandId?: string,
    accountEmail?: string
  ) {
    return await apiPost({
      endpoint: ENDPOINTS.KNOWLEDGE.SHAREPOINT_REVOKE,
      payload: {
        companyId,
        brandId,
        accountEmail,
      },
    });
  },

  async getGoogleDriveAccounts(companyId?: string, brandId?: string) {
    if (!brandId) {
      throw new Error('brandId is required');
    }

    if (!companyId) {
      throw new Error('companyId is required');
    }

    return await apiPost<string[]>({
      endpoint: ENDPOINTS.KNOWLEDGE.ALL_GOOGLE_DRIVE_ACCOUNTS,
      payload: {
        companyId,
        brandId,
      },
    });
  },

  async getGoogleDriveFiles(
    companyId?: string,
    brandId?: string,
    accountEmail?: string
  ) {
    return await apiPost<GoogleDriveResponse[]>({
      endpoint: ENDPOINTS.KNOWLEDGE.GOOGLE_DRIVE_FILES,
      payload: {
        companyId,
        brandId,
        accountEmail,
      },
    });
  },

  async selectFileGoogleDrive(payload: SelectFileGoogleDriveSchema) {
    return await apiPost<KnowledgeResponse>({
      endpoint: ENDPOINTS.KNOWLEDGE.GOOGLE_SELECT_FILE,
      payload: payload,
    });
  },

  async revokeGoogleDrive(
    companyId?: string,
    brandId?: string,
    accountEmail?: string
  ) {
    return await apiPost({
      endpoint: ENDPOINTS.KNOWLEDGE.GOOGLE_REVOKE,
      payload: {
        companyId,
        brandId,
        accountEmail,
      },
    });
  },

  async getAccountsAirtable(companyId?: string, brandId?: string) {
    if (!brandId) {
      throw new Error('brandId is required');
    }

    if (!companyId) {
      throw new Error('companyId is required');
    }

    return await apiPost<string[]>({
      endpoint: ENDPOINTS.KNOWLEDGE.AIRTABLE_ACCOUNTS,
      payload: {
        companyId,
        brandId,
      },
    });
  },

  async getBasesAirtable(
    companyId?: string,
    brandId?: string,
    accountEmail?: string
  ) {
    return await apiPost<AirtableResponse[]>({
      endpoint: ENDPOINTS.KNOWLEDGE.AIRTABLE_BASES,
      payload: {
        companyId,
        brandId,
        accountEmail,
      },
    });
  },

  async getTablesAirtable(
    companyId?: string,
    brandId?: string,
    accountEmail?: string,
    BaseId?: string
  ) {
    return await apiPost<AirtableResponse[]>({
      endpoint: ENDPOINTS.KNOWLEDGE.AIRTABLE_TABLES,
      payload: {
        companyId,
        brandId,
        accountEmail,
        BaseId,
      },
    });
  },

  async selectTableAirtable(payload: SelectTableAirtableSchema) {
    return await apiPost<KnowledgeResponse>({
      endpoint: ENDPOINTS.KNOWLEDGE.AIRTABLE_SELECT_TABLE,
      payload: payload,
    });
  },

  async revokeAirtable(
    companyId?: string,
    brandId?: string,
    accountEmail?: string
  ) {
    return await apiPost({
      endpoint: ENDPOINTS.KNOWLEDGE.AIRTABLE_REVOKE,
      payload: {
        companyId,
        brandId,
        accountEmail,
      },
    });
  },

  buildExternalDriveUrl(
    externalDriveName: 'GOOGLE_DRIVE' | 'SHAREPOINT' | 'AIRTABLE',
    companyId?: string,
    brandId?: string
  ): string {
    if (!companyId || !brandId) {
      throw new Error('companyId and brandId are required');
    }

    switch (externalDriveName) {
      case 'GOOGLE_DRIVE':
        return `${API_URL}${ENDPOINTS.KNOWLEDGE.GOOGLE_AUTH}?companyId=${companyId}&brandId=${brandId}`;
      case 'SHAREPOINT':
        return `${API_URL}${ENDPOINTS.KNOWLEDGE.SHAREPOINT_AUTH}?companyId=${companyId}&brandId=${brandId}`;
      case 'AIRTABLE':
        return `${API_URL}${ENDPOINTS.KNOWLEDGE.AIRTABLE_AUTH}?companyId=${companyId}&brandId=${brandId}`;
      default:
        throw new Error('Invalid external drive name');
    }
  },

  openPopupAndMonitor(url: string, onCloseCallback: () => void) {
    const popup = window.open(
      url,
      '_blank',
      'width=600,height=800,scrollbars=yes,resizable=yes'
    );

    if (!popup) {
      throw new Error('Popup blocked! Please allow popups for this site.');
    }

    popup.focus();

    const interval = setInterval(() => {
      if (popup.closed) {
        clearInterval(interval);
        onCloseCallback();
      }
    }, 500);
  },
};
