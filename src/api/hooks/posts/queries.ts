import { PromptType } from '@/api/models/dtos/post.dto';

import { QUERY_KEYS } from '@/api/config/query-keys';
import { postService } from '@/api/services/post.service';
import { useCurrentUserStore } from '@/stores/current-user-store';
import { useQuery } from '@tanstack/react-query';

export const usePostsByCompanyBrandId = () => {
  const { getUser } = useCurrentUserStore();
  const user = getUser();

  return useQuery({
    queryKey: QUERY_KEYS.POSTS.BY_COMPANY_BRAND_ID(
      user?.companyId,
      user?.brandId
    ),
    queryFn: async () =>
      await postService.getPostsByCompanyBrandId(
        user?.companyId,
        user?.brandId
      ),
    enabled: !!user?.companyId && !!user?.brandId,
  });
};

export const usePostsById = (postId?: string) => {
  return useQuery({
    queryKey: QUERY_KEYS.POSTS.DETAIL(postId),
    queryFn: async () => await postService.getPostById(postId),
    enabled: !!postId,
  });
};

export const useSavedSuggestionsById = (postId: string) => {
  return useQuery({
    queryKey: QUERY_KEYS.POSTS.SAVE_SUGGESTIONS_BY_ID(postId),
    queryFn: async () => await postService.getSavedSuggestionsById(postId),
    enabled: !!postId,
  });
};

export const useGetGeneratedPostPrompt = (
  type: PromptType,
  adContentId?: string
) => {
  return useQuery({
    queryKey: QUERY_KEYS.POSTS.GET_GENERATED_POST_PROMPT(type, adContentId),
    queryFn: async () =>
      await postService.getGeneratedPostPrompt(type, adContentId),
    enabled: !!type && !!adContentId,
  });
};
