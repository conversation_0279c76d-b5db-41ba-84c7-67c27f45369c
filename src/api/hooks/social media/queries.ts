import { QUERY_KEYS } from '@/api/config/query-keys';
import { socialMediaService } from '@/api/services/social-media.service';
import { useQuery } from '@tanstack/react-query';

export const useAllInsights = (brandId?: string, companyId?: string) => {
  return useQuery({
    queryKey: QUERY_KEYS.SOCIAL_MEDIA.INSIGHTS_BY_PLATFORM(brandId, companyId),
    queryFn: async () => {
      const [facebook, instagram, linkedin] = await Promise.all([
        socialMediaService.getFacebookInsights(brandId, companyId),
        socialMediaService.getInstagramInsights(brandId, companyId),
        socialMediaService.getLinkedinInsights(brandId, companyId),
      ]);

      return { facebook, instagram, linkedin };
    },
    enabled: !!brandId && !!companyId,
  });
};
