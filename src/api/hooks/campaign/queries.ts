import { CampaignResponse } from '@/api/models/dtos/compaign.dto';

import { sumArrayValues } from '@/lib/sum-array-values';

import { QUERY_KEYS } from '@/api/config/query-keys';
import { campaignService } from '@/api/services/campaign.service';
import { useCurrentUserStore } from '@/stores/current-user-store';
import { useQuery } from '@tanstack/react-query';

export const useAllCampaigns = () => {
  const user = useCurrentUserStore().getUser();

  return useQuery({
    queryKey: QUERY_KEYS.CAMPAIGNS.ALL_BY_COMPANY_BRAND_ID(
      user?.companyId,
      user?.brandId
    ),
    queryFn: async () =>
      await campaignService.getAllByCompanyBrandId(
        user?.companyId,
        user?.brandId
      ),
    enabled: !!user?.companyId && !!user?.brandId,
    select: (campaigns): CampaignResponse[] =>
      campaigns.map((campaign) => {
        const { FbInsights, InstaInsights, LinkedinInsights, XInsights } =
          campaign;

        const viewsInsights = [
          FbInsights.Impression,
          InstaInsights.Impression,
          LinkedinInsights.Impression,
          XInsights.Impression,
        ];

        const reachInsights = [
          FbInsights.Reach,
          InstaInsights.Reach,
          LinkedinInsights.Reach,
          XInsights.Reach,
        ];

        const interactionInsights = [
          FbInsights.Reaction,
          InstaInsights.Reaction,
          LinkedinInsights.Reaction,
          XInsights.Reaction,
        ];

        return {
          ...campaign,
          ViewsStat: {
            Title: 'Views',
            Total: sumArrayValues(viewsInsights),
            Trend: 0.0,
            TrendStatus: 'neutral',
          },
          ReachStat: {
            Title: 'Reach',
            Total: sumArrayValues(reachInsights),
            Trend: 0.0,
            TrendStatus: 'neutral',
          },
          InteractionStat: {
            Title: 'Interactions',
            Total: sumArrayValues(interactionInsights),
            Trend: 0.0,
            TrendStatus: 'neutral',
          },
        };
      }),
  });
};

export const useCampaignById = (campaignId?: string) => {
  return useQuery({
    queryKey: QUERY_KEYS.CAMPAIGNS.DETAIL(campaignId),
    queryFn: async () => await campaignService.getCampaignById(campaignId),
    enabled: !!campaignId,
  });
};
