import { toast } from 'sonner';

import { QUERY_KEYS } from '@/api/config/query-keys';
import {
  CreateCampaignSchema,
  CreateAICampaignSchema,
} from '@/api/models/schemas/campaign.schema';
import { campaignService } from '@/api/services/campaign.service';
import { useCampaignStore } from '@/stores/campaign-store';
import { useMutation, useQueryClient } from '@tanstack/react-query';

export const useCreateCampaign = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (payload: CreateCampaignSchema) => {
      return campaignService.createCampaign(payload);
    },
    onSuccess: async () => {
      toast.success('Campaign created');

      await queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.CAMPAIGNS.ALL,
      });
    },
    onError: () => {
      toast.error('Error while creating campaign');
    },
  });
};

export const useGenerateAICampaignProposals = () => {
  const { setIsAICampaignProposalsSuccess, setAiCampaignResponseData } =
    useCampaignStore();

  return useMutation({
    mutationFn: async (payload: CreateAICampaignSchema) => {
      return await campaignService.generateAICampaignProposals(payload);
    },
    onSuccess: async (data) => {
      setAiCampaignResponseData(data);
      setIsAICampaignProposalsSuccess(true);
      toast.success('AI campaign proposals generated');
    },
    onError: () => {
      toast.error('Error while creating campaign');
    },
  });
};
