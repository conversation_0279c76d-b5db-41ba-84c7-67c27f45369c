import { timeStringToMilliseconds } from '@/lib/time-to-milliseconds';

import { QUERY_KEYS } from '@/api/config/query-keys';
import { PrepareTextModelSchema } from '@/api/models/schemas/private-model.schema';
import { aiService } from '@/api/services/ai.service';
import { useCurrentUserStore } from '@/stores/current-user-store';
import { useQuery } from '@tanstack/react-query';

const PREPARE_DATA_STALE_TIME = '15min';

export const useAllByCompanyBrandId = () => {
  const { getUser } = useCurrentUserStore();
  const user = getUser();

  return useQuery({
    queryKey: QUERY_KEYS.PRIVATE_MODELS.ALL_BY_COMPANY_BRAND_ID(
      user?.companyId,
      user?.brandId
    ),
    queryFn: async () =>
      await aiService.getAllByCompanyBrandId(user?.companyId, user?.brandId),
    enabled: !!user?.companyId && !!user?.brandId,
  });
};

export const usePrepareTextPrivateModel = (
  platformName?: PrepareTextModelSchema['SocialMediaData'][number]['platform'],
  pageName?: string
) => {
  return useQuery({
    queryKey: QUERY_KEYS.PRIVATE_MODELS.PREPARE_TEXT_PRIVATE_MODEL(
      platformName,
      pageName
    ),
    queryFn: async () =>
      await aiService.prepareTextPrivateModel({
        platformName: platformName,
        pageName: pageName,
      }),
    enabled: !!platformName || !!pageName,
    staleTime: timeStringToMilliseconds({
      timeString: PREPARE_DATA_STALE_TIME,
    }),
  });
};

export const useGetPrivateModelDetail = (id: string) => {
  return useQuery({
    queryKey: QUERY_KEYS.PRIVATE_MODELS.DETAIL(id),
    queryFn: async () => await aiService.getPrivateModelDetail(id),
    enabled: !!id,
  });
};
