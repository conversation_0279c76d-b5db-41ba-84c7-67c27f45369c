import { useTranslations } from 'next-intl';
import { toast } from 'sonner';

import { QUERY_KEYS } from '@/api/config/query-keys';
import {
  SaveTextModelSchema,
  SaveImageModelSchema,
  PrepareImageModelSchema,
} from '@/api/models/schemas/private-model.schema';
import { aiService } from '@/api/services/ai.service';
import { useCurrentUserStore } from '@/stores/current-user-store';
import { useMutation, useQueryClient } from '@tanstack/react-query';

export const useCheckLimit = () => {
  const { getUser } = useCurrentUserStore();
  const user = getUser();

  return useMutation({
    mutationFn: () => aiService.checkLimit(user?.companyId),
  });
};

export const useCreateLLMTextModel = () => {
  const t = useTranslations();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: SaveTextModelSchema) =>
      aiService.createLLMTextModel(payload),
    onSuccess: async () => {
      await queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.PRIVATE_MODELS.All,
      });
      toast.success(t('toast.model_created_successfully'));
    },
  });
};

export const useCreateDummyLLMTextModel = () => {
  return useMutation({
    mutationFn: (payload: SaveTextModelSchema) =>
      aiService.createDummyLLMTextModel(payload),
  });
};

export const usePrepareImagePrivateModel = () => {
  const t = useTranslations();

  return useMutation({
    mutationFn: async (payload: PrepareImageModelSchema) => {
      return aiService.prepareImagePrivateModel(payload);
    },
    onSuccess: () => {
      toast.success(t('toast.model_prapared_successfully'));
    },
    onError: () => {
      toast.error(t('toast.error_preparing_model'));
    },
  });
};

export const useCreateImagePrivateModel = () => {
  const t = useTranslations();

  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: SaveImageModelSchema) =>
      aiService.createImagePrivateModel(payload),
    onSuccess: async () => {
      await queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.PRIVATE_MODELS.All,
      });
      toast.success(t('toast.model_created_successfully'));
    },
  });
};

export const useMarkPrivateModelAsFavorite = () => {
  const t = useTranslations();

  return useMutation({
    mutationFn: async (privateModelId?: string) =>
      await aiService.markPrivateModelAsFavorite(privateModelId),

    onSuccess: async (data) => {
      if (data.Favorite) {
        toast.success(t('toast.model_marked_as_favorite'));
      } else {
        toast.success(t('toast.model_marked_as_not_favorite'));
      }
    },

    onError: () => {
      toast.error(t('toast.error_deleting_model'));
    },
  });
};

export const useDeletePrivateModel = () => {
  return useMutation({
    mutationFn: async (privateModelId?: string) =>
      await aiService.deletePrivateModel(privateModelId),
  });
};
