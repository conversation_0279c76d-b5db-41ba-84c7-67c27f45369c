import { QUERY_KEYS } from '@/api/config/query-keys';
import { brandService } from '@/api/services/brand.service';
import { useCurrentUserStore } from '@/stores/current-user-store';
import { useQuery } from '@tanstack/react-query';

export const useBrandsByCompanyId = () => {
  const { getUser } = useCurrentUserStore();
  const user = getUser();

  return useQuery({
    queryKey: QUERY_KEYS.BRANDS.ALL_BY_COMPANY_ID(user?.companyId),
    queryFn: async () => await brandService.getAllByCompanyId(user?.companyId),
    enabled: !!user?.companyId,
  });
};

export const useBrandById = (brandId?: string) => {
  return useQuery({
    queryKey: QUERY_KEYS.BRANDS.DETAIL(brandId),
    queryFn: async () => await brandService.getBrandById(brandId),
    enabled: !!brandId,
  });
};
