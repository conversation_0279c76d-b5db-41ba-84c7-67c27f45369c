'use client';

import { LoginSchema } from '@/api/models/schemas/login.schema';
import { SignupSchema } from '@/api/models/schemas/signup.schema';
import { authService } from '@/api/services/auth.service';
import { useCurrentUserStore } from '@/stores/current-user-store';
import { useMutation } from '@tanstack/react-query';

export const useLogin = () => {
  const { setUser } = useCurrentUserStore();

  return useMutation({
    mutationFn: ({ UserEmail, UserPassword }: LoginSchema) =>
      authService.login(UserEmail, UserPassword),

    onSuccess: (data) => {
      setUser({
        name: data?.name,
        email: data?.email,
        brandId: data?.brandId,
        companyId: data?.companyId,
      });
      localStorage.setItem('auth_token', data.token);
    },
  });
};

export const useRegister = () => {
  return useMutation({
    mutationFn: ({ UserName, UserEmail, UserPassword }: SignupSchema) =>
      authService.register(UserName, UserEmail, UserPassword),

    onError: (error) => {
      if (error.message !== 'server_error') {
        throw new Error('this_email_is_already_used');
      }
    },
  });
};
