import { useTranslations } from 'next-intl';
import { toast } from 'sonner';

import { QUERY_KEYS } from '@/api/config/query-keys';
import {
  AudienceSchema,
  CreateKnowledgeFileSchema,
  CreateKnowledgeLogoSchema,
  CreateKnowledgeTextSchema,
  UpdateKnowledgeTextSchema,
  UpdateKnowledgeFileSchema,
  UpdateKnowledgeLogoSchema,
  SelectTableAirtableSchema,
  SelectFileSharepointSchema,
  CreateKnowledgeColorSchema,
  UpdateKnowledgeColorSchema,
  SelectFileGoogleDriveSchema,
  CreateKnowledgeWebsiteSchema,
  UpdateKnowledgeWebsiteSchema,
  UpdateKnowledgeAudienceSchema,
  KnowledgeScrapingWebsiteSchema,
} from '@/api/models/schemas/knowledge.schema';
import { knowledgeService } from '@/api/services/knowledge.service';
import { useKnowledgeScrapedWebsiteStore } from '@/stores/knowledge-scraped-website-store';
import { useMutation, useQueryClient } from '@tanstack/react-query';

export const usePrepareUrlContentKnowledge = () => {
  const t = useTranslations();
  const setWebsiteScrapedResults = useKnowledgeScrapedWebsiteStore(
    (state) => state.setWebsiteScrapedResults
  );
  const setShowResults = useKnowledgeScrapedWebsiteStore(
    (state) => state.setShowResults
  );

  return useMutation({
    mutationFn: async (payload: KnowledgeScrapingWebsiteSchema) => {
      return knowledgeService.prepareUrlContentKnowledge(payload);
    },
    onMutate: (variables) => {
      setWebsiteScrapedResults({
        ...variables,
        isLoading: true,
        HtmlText: '',
      });
    },
    onSuccess: ({ HtmlText }, variables) => {
      setWebsiteScrapedResults({
        ...variables,
        HtmlText: HtmlText,
        isLoading: false,
      });

      setShowResults(false);

      toast.success(t('toast_knowledge.website_scraped'));
    },
    onError: () => {
      toast.error(t('toast_knowledge.website_not_scraped'));
    },
  });
};

export const useCreateTextKnowledge = () => {
  const t = useTranslations();

  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (payload: CreateKnowledgeTextSchema) => {
      return knowledgeService.createTextKnowledge(payload);
    },
    onSuccess: async () => {
      toast.success(t('toast_knowledge.text_created'));

      await queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.KNOWLEDGES.ALL,
      });
    },
    onError: () => {
      toast.error(t('toast_knowledge.text_not_created'));
    },
  });
};

export const useCreateWebsiteKnowledge = () => {
  const t = useTranslations();

  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (payload: CreateKnowledgeWebsiteSchema) => {
      return knowledgeService.createWebsiteKnowledge(payload);
    },
    onSuccess: async () => {
      toast.success(t('toast_knowledge.website_created'));

      await queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.KNOWLEDGES.ALL,
      });
    },
    onError: () => {
      toast.error(t('toast_knowledge.website_not_created'));
    },
  });
};

export const useCreateFileKnowledge = () => {
  const t = useTranslations();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (payload: CreateKnowledgeFileSchema) => {
      return knowledgeService.createFileKnowledge(payload);
    },
    onSuccess: async () => {
      toast.success(t('toast_knowledge.file_created'));

      await queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.KNOWLEDGES.ALL,
      });
    },
    onError: () => {
      toast.error(t('toast_knowledge.file_not_created'));
    },
  });
};

export const useCreateLogoKnowledge = () => {
  const t = useTranslations();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (payload: CreateKnowledgeLogoSchema) => {
      return knowledgeService.createLogoKnowledge(payload);
    },
    onSuccess: async () => {
      toast.success(t('toast_knowledge.logo_created'));

      await queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.KNOWLEDGES.ALL,
      });
    },
    onError: () => {
      toast.error(t('toast_knowledge.logo_not_created'));
    },
  });
};

export const useCreateColorKnowledge = () => {
  const t = useTranslations();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (payload: CreateKnowledgeColorSchema) => {
      return knowledgeService.createColorKnowledge(payload);
    },
    onSuccess: async () => {
      toast.success(t('toast_knowledge.color_created'));

      await queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.KNOWLEDGES.ALL,
      });
    },
    onError: () => {
      toast.error(t('toast_knowledge.color_not_created'));
    },
  });
};

export const useCreateAudienceKnowledge = () => {
  const t = useTranslations();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (payload: AudienceSchema) => {
      const { AgeRange, ...rest } = payload.knowledge.Audience;

      return knowledgeService.createAudienceKnowledge({
        ...payload,
        knowledge: {
          ...payload.knowledge,
          Audience: {
            ...rest,
            AgeMin: AgeRange[0],
            AgeMax: AgeRange[1],
          },
        },
      });
    },
    onSuccess: async () => {
      toast.success(t('toast_knowledge.audience_created'));
      await queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.KNOWLEDGES.ALL,
      });
    },
    onError: () => {
      toast.error(t('toast_knowledge.audience_not_created'));
    },
  });
};

export const useMarkKnowledgeAsFavorite = () => {
  const t = useTranslations();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (knowledgeId?: string) =>
      await knowledgeService.markKnowledgeAsFavorite(knowledgeId),
    onSuccess: async (data) => {
      if (data.Favorite) {
        toast.success(t('toast_knowledge.model_marked_as_favorite'));
      } else {
        toast.success(t('toast_knowledge.model_marked_as_not_favorite'));
      }

      await queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.KNOWLEDGES.ALL,
      });
    },
    onError: () => {
      toast.error(t('toast_knowledge.error_marking_model_as_favorite'));
    },
  });
};

export const useDeleteKnowledge = () => {
  const t = useTranslations();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (knowledgeId?: string) =>
      await knowledgeService.deleteKnowledge(knowledgeId),
    onSuccess: async () => {
      await queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.KNOWLEDGES.ALL,
      });
      toast.success(t('toast_knowledge.knowledge_deleted'));
    },
  });
};

export const useUpdateTextKnowledge = () => {
  const t = useTranslations();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (payload: UpdateKnowledgeTextSchema) => {
      return knowledgeService.updateTextKnowledge(payload);
    },
    onSuccess: async () => {
      toast.success(t('toast_knowledge.text_updated'));

      await queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.KNOWLEDGES.ALL,
      });
    },
    onError: () => {
      toast.error(t('toast_knowledge.text_not_updated'));
    },
  });
};

export const useUpdateWebsiteKnowledge = () => {
  const t = useTranslations();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (payload: UpdateKnowledgeWebsiteSchema) => {
      return knowledgeService.updateWebsiteKnowledge(payload);
    },
    onSuccess: async () => {
      toast.success(t('toast_knowledge.website_updated'));

      await queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.KNOWLEDGES.ALL,
      });
    },
    onError: () => {
      toast.error(t('toast_knowledge.website_not_updated'));
    },
  });
};

export const useUpdateFileKnowledge = () => {
  const t = useTranslations();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (payload: UpdateKnowledgeFileSchema) => {
      return knowledgeService.updateFileKnowledge(payload);
    },
    onSuccess: async () => {
      toast.success(t('toast_knowledge.file_updated'));

      await queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.KNOWLEDGES.ALL,
      });
    },
    onError: () => {
      toast.error(t('toast_knowledge.file_not_updated'));
    },
  });
};

export const useUpdateLogoKnowledge = () => {
  const t = useTranslations();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (payload: UpdateKnowledgeLogoSchema) => {
      return knowledgeService.updateLogoKnowledge(payload);
    },
    onSuccess: async () => {
      toast.success(t('toast_knowledge.logo_updated'));

      await queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.KNOWLEDGES.ALL,
      });
    },
    onError: () => {
      toast.error(t('toast_knowledge.logo_not_updated'));
    },
  });
};

export const useUpdateColorKnowledge = () => {
  const t = useTranslations();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (payload: UpdateKnowledgeColorSchema) => {
      return knowledgeService.updateColorKnowledge(payload);
    },
    onSuccess: async () => {
      toast.success(t('toast_knowledge.color_updated'));

      await queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.KNOWLEDGES.ALL,
      });
    },
    onError: () => {
      toast.error(t('toast_knowledge.color_not_updated'));
    },
  });
};

export const useUpdateAudienceKnowledge = () => {
  const t = useTranslations();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (payload: UpdateKnowledgeAudienceSchema) => {
      const { AgeRange, ...rest } = payload.knowledge.Audience;

      return knowledgeService.updateAudienceKnowledge({
        ...payload,
        knowledge: {
          ...payload.knowledge,
          Audience: {
            ...rest,
            AgeMin: AgeRange[0],
            AgeMax: AgeRange[1],
          },
        },
      });
    },
    onSuccess: async () => {
      toast.success(t('toast_knowledge.audience_updated'));

      await queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.KNOWLEDGES.ALL,
      });
    },
    onError: () => {
      toast.error(t('toast_knowledge.audience_not_updated'));
    },
  });
};

export const useSelectFileGoogleDrive = () => {
  return useMutation({
    mutationFn: async (payload: SelectFileGoogleDriveSchema) => {
      return knowledgeService.selectFileGoogleDrive(payload);
    },
  });
};

export const useSelectFileKnowledge = () => {
  return useMutation({
    mutationFn: async (payload: SelectFileSharepointSchema) => {
      return knowledgeService.selectFileSharepoint(payload);
    },
  });
};

export const useSelectTableAirtable = () => {
  return useMutation({
    mutationFn: async (payload: SelectTableAirtableSchema) => {
      return knowledgeService.selectTableAirtable(payload);
    },
  });
};

export const useRevokeAccountGoogle = (
  companyId?: string,
  brandId?: string
) => {
  const t = useTranslations();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (accountEmail: string) => {
      return knowledgeService.revokeGoogleDrive(
        companyId,
        brandId,
        accountEmail
      );
    },
    onSuccess: async (accountEmail) => {
      toast.success(t('toast_knowledge.google_drive_revoked'));

      await queryClient.invalidateQueries({
        queryKey: ['google-drive-accounts', companyId, brandId],
      });
      queryClient.invalidateQueries({
        queryKey: ['google-drive-files', companyId, brandId, accountEmail],
      });
    },
    onError: () => {
      toast.error(t('toast_knowledge.google_drive_not_revoked'));
    },
  });
};

export const useRevokeAccountSharepoint = (
  companyId?: string,
  brandId?: string
) => {
  const t = useTranslations();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (accountEmail: string) => {
      if (!accountEmail) {
        throw new Error('Account email is required');
      }
      return knowledgeService.revokeSharepoint(
        companyId,
        brandId,
        accountEmail
      );
    },
    onSuccess: (_, accountEmail) => {
      toast.success(t('toast_knowledge.sharepoint_revoked'));
      queryClient.invalidateQueries({
        queryKey: ['sharepoint-accounts', companyId, brandId],
      });
      queryClient.invalidateQueries({
        queryKey: ['sharepoint-files', companyId, brandId, accountEmail],
      });
    },
    onError: () => {
      toast.error(t('toast_knowledge.sharepoint_not_revoked'));
    },
  });
};

export const useRevokeAccountAirtable = (
  companyId?: string,
  brandId?: string
) => {
  const t = useTranslations();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (accountEmail: string) => {
      if (!accountEmail) {
        throw new Error('Account email is required');
      }
      return knowledgeService.revokeAirtable(companyId, brandId, accountEmail);
    },
    onSuccess: (_, accountEmail) => {
      toast.success(t('toast_knowledge.aitable_revoked'));
      queryClient.invalidateQueries({
        queryKey: ['airtable-accounts', companyId, brandId],
      });
      queryClient.invalidateQueries({
        queryKey: ['airtable-files', companyId, brandId, accountEmail],
      });
    },
    onError: () => {
      toast.error(t('toast_knowledge.aitable_not_revoked'));
    },
  });
};
