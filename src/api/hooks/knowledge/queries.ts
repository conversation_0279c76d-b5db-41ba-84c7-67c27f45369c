import { QUERY_KEYS } from '@/api/config/query-keys';
import { knowledgeService } from '@/api/services/knowledge.service';
import { useQuery } from '@tanstack/react-query';

export const useAllKnowledge = (companyId?: string, brandId?: string) => {
  return useQuery({
    queryKey: QUERY_KEYS.KNOWLEDGES.ALL_BY_COMPANY_BRAND_ID(companyId, brandId),
    queryFn: async () =>
      await knowledgeService.getAllByCompanyBrandId(companyId, brandId),
    enabled: !!companyId && !!brandId,
  });
};

export const useGetGoogleDriveAccounts = (
  companyId?: string,
  brandId?: string
) => {
  return useQuery({
    queryKey: QUERY_KEYS.GOOGLE_DRIVE.ALL_BY_COMPANY_BRAND_ID(
      companyId,
      brandId
    ),
    queryFn: async () =>
      await knowledgeService.getGoogleDriveAccounts(companyId, brandId),
    enabled: !!brandId && !!companyId,
  });
};

export const useGetGoogleDriveFiles = (
  companyId?: string,
  brandId?: string,
  accountEmail?: string
) => {
  return useQuery({
    queryKey: QUERY_KEYS.GOOGLE_DRIVE.ALL_FILES(
      companyId,
      brandId,
      accountEmail
    ),
    queryFn: async () =>
      await knowledgeService.getGoogleDriveFiles(
        companyId,
        brandId,
        accountEmail
      ),
    enabled: !!brandId && !!companyId && !!accountEmail,
  });
};

export const useGetSharepointAccounts = (
  companyId?: string,
  brandId?: string
) => {
  return useQuery({
    queryKey: QUERY_KEYS.SHARE_POINT.ALL_BY_COMPANY_BRAND_ID(
      companyId,
      brandId
    ),
    queryFn: async () =>
      await knowledgeService.getAccountsSharepoint(companyId, brandId),
    enabled: !!brandId && !!companyId,
  });
};

export const useGetSitesSharepoint = (
  companyId?: string,
  brandId?: string,
  accountEmail?: string
) => {
  return useQuery({
    queryKey: QUERY_KEYS.SHARE_POINT.ALL_SITES(
      companyId,
      brandId,
      accountEmail
    ),
    queryFn: async () =>
      await knowledgeService.getSitesSharepoint(
        companyId,
        brandId,
        accountEmail
      ),
    enabled: !!brandId && !!companyId && !!accountEmail,
  });
};

export const useGetDrivesSharepoint = (
  companyId?: string,
  brandId?: string,
  accountEmail?: string,
  siteId?: string
) => {
  return useQuery({
    queryKey: QUERY_KEYS.SHARE_POINT.ALL_DRIVES(
      companyId,
      brandId,
      accountEmail,
      siteId
    ),
    queryFn: async () =>
      await knowledgeService.getDrivesSharepoint(
        companyId,
        brandId,
        accountEmail,
        siteId
      ),
    enabled: !!brandId && !!companyId && !!accountEmail && !!siteId,
  });
};

export const useGetFoldersSharepoint = (
  companyId?: string,
  brandId?: string,
  accountEmail?: string,
  siteId?: string,
  driveId?: string
) => {
  return useQuery({
    queryKey: QUERY_KEYS.SHARE_POINT.ALL_FOLDERS(
      companyId,
      brandId,
      accountEmail,
      siteId,
      driveId
    ),
    queryFn: async () =>
      await knowledgeService.getFoldersSharepoint(
        companyId,
        brandId,
        accountEmail,
        siteId,
        driveId
      ),
    enabled:
      !!brandId && !!companyId && !!accountEmail && !!siteId && !!driveId,
  });
};

export const useGetFilesSharepoint = (
  companyId?: string,
  brandId?: string,
  accountEmail?: string,
  siteId?: string,
  driveId?: string,
  folderId?: string
) => {
  return useQuery({
    queryKey: QUERY_KEYS.SHARE_POINT.ALL_FILES(
      companyId,
      brandId,
      accountEmail,
      siteId,
      driveId,
      folderId
    ),
    queryFn: async () =>
      await knowledgeService.getFilesSharepoint(
        companyId,
        brandId,
        accountEmail,
        siteId,
        driveId,
        folderId
      ),
    enabled:
      !!brandId &&
      !!companyId &&
      !!accountEmail &&
      !!siteId &&
      !!driveId &&
      !!folderId,
  });
};

export const useGetAccountsAirtable = (
  companyId?: string,
  brandId?: string
) => {
  return useQuery({
    queryKey: QUERY_KEYS.AIRTABLE.ALL_BY_COMPANY_BRAND_ID(companyId, brandId),
    queryFn: async () =>
      await knowledgeService.getAccountsAirtable(companyId, brandId),
    enabled: !!brandId && !!companyId,
  });
};
export const useGetBasesAirtable = (
  companyId?: string,
  brandId?: string,
  accountEmail?: string
) => {
  return useQuery({
    queryKey: QUERY_KEYS.AIRTABLE.ALL_BASES(companyId, brandId, accountEmail),
    queryFn: async () =>
      await knowledgeService.getBasesAirtable(companyId, brandId, accountEmail),
    enabled: !!brandId && !!companyId && !!accountEmail,
  });
};

export const useGetTablesAirtable = (
  companyId?: string,
  brandId?: string,
  accountEmail?: string,
  baseId?: string
) => {
  return useQuery({
    queryKey: QUERY_KEYS.AIRTABLE.ALL_TABLES(
      companyId,
      brandId,
      accountEmail,
      baseId
    ),
    queryFn: async () =>
      await knowledgeService.getTablesAirtable(
        companyId,
        brandId,
        accountEmail,
        baseId
      ),
    enabled: !!brandId && !!companyId && !!accountEmail && !!baseId,
  });
};
