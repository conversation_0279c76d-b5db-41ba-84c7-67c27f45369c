import axios from 'axios';

import { formatErrorMessage } from '@/api/helpers/format-error-message';

export const handleApiError = (error: unknown): never => {
  if (axios.isAxiosError(error)) {
    const status = error.response?.status;
    const serverError = error.response?.data?.error || error.message;

    const errorMessage = formatErrorMessage(serverError);
    const errorObj = new Error(errorMessage);
    errorObj.cause = { status, originalError: serverError };
    throw errorObj;
  }

  const errorMessage = error instanceof Error ? error.message : 'unknown_error';
  const errorObj = new Error(formatErrorMessage(errorMessage));
  errorObj.cause = { status: 500, originalError: errorMessage };
  throw errorObj;
};
