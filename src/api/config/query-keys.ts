import { PromptType } from '@/api/models/dtos/post.dto';

import { PrepareTextModelSchema } from '@/api/models/schemas/private-model.schema';

export const QUERY_KEYS = {
  USERS: {
    BY_EMAIL: (email?: string) => ['users', email],
  },
  BRANDS: {
    ALL_BY_COMPANY_ID: (companyId?: string) => ['brands', companyId],
    DETAIL: (brandId?: string) => ['brand', brandId],
  },
  SOCIAL_MEDIA: {
    INSIGHTS_BY_PLATFORM: (brandId?: string, companyId?: string) => [
      'allInsights',
      brandId,
      companyId,
    ],
  },
  POSTS: {
    ALL: ['posts'],
    BY_COMPANY_BRAND_ID: (companyId?: string, brandId?: string) => [
      'posts',
      companyId,
      brandId,
    ],
    DETAIL: (postId?: string) => ['post', postId],
    SAVE_SUGGESTIONS_BY_ID: (postId: string) => ['saveSuggestions', postId],
    GET_GENERATED_POST_PROMPT: (type: PromptType, adContentId?: string) => [
      'generatedPostPrompt',
      type,
      adContentId,
    ],
  },
  PRIVATE_MODELS: {
    All: ['privateModels'],
    ALL_BY_COMPANY_BRAND_ID: (companyId?: string, brandId?: string) => [
      'privateModels',
      companyId,
      brandId,
    ],
    PREPARE_TEXT_PRIVATE_MODEL: (
      platformName?: PrepareTextModelSchema['SocialMediaData'][number]['platform'],
      pageName?: string
    ) => ['privateModelPreviewData', platformName, pageName],
    DETAIL: (id: string) => ['privateModelDetail', id],
  },
  KNOWLEDGES: {
    ALL: ['knowledges'],
    ALL_BY_COMPANY_BRAND_ID: (companyId?: string, brandId?: string) => [
      'knowledges',
      companyId,
      brandId,
    ],
  },
  CAMPAIGNS: {
    ALL: ['campaigns'],
    ALL_BY_COMPANY_BRAND_ID: (companyId?: string, brandId?: string) => [
      'campaigns',
      companyId,
      brandId,
    ],
    DETAIL: (campaignId?: string) => ['campaign', campaignId],
  },
  GOOGLE_DRIVE: {
    ALL_BY_COMPANY_BRAND_ID: (companyId?: string, brandId?: string) => [
      'googleDriveAccount',
      companyId,
      brandId,
    ],
    ALL_FILES: (
      companyId?: string,
      brandId?: string,
      accountEmail?: string
    ) => ['googleDriveFiles', companyId, brandId, accountEmail],
  },
  SHARE_POINT: {
    ALL_BY_COMPANY_BRAND_ID: (companyId?: string, brandId?: string) => [
      'sharepointAccount',
      companyId,
      brandId,
    ],
    ALL_SITES: (
      companyId?: string,
      brandId?: string,
      accountEmail?: string
    ) => ['sharepointSites', companyId, brandId, accountEmail],
    ALL_DRIVES: (
      companyId?: string,
      brandId?: string,
      accountEmail?: string,
      siteId?: string
    ) => ['sharepointDrive', companyId, brandId, accountEmail, siteId],
    ALL_FOLDERS: (
      companyId?: string,
      brandId?: string,
      accountEmail?: string,
      siteId?: string,
      driveId?: string
    ) => [
      'sharepointFolders',
      companyId,
      brandId,
      accountEmail,
      siteId,
      driveId,
    ],
    ALL_FILES: (
      companyId?: string,
      brandId?: string,
      accountEmail?: string,
      siteId?: string,
      driveId?: string,
      folderId?: string
    ) => [
      'sharepointFiles',
      companyId,
      brandId,
      accountEmail,
      siteId,
      driveId,
      folderId,
    ],
  },
  AIRTABLE: {
    ALL_BY_COMPANY_BRAND_ID: (companyId?: string, brandId?: string) => [
      'airtableAccount',
      companyId,
      brandId,
    ],
    ALL_BASES: (
      companyId?: string,
      brandId?: string,
      accountEmail?: string
    ) => ['airtableBases', companyId, brandId, accountEmail],
    ALL_TABLES: (
      companyId?: string,
      brandId?: string,
      accountEmail?: string,
      baseId?: string
    ) => ['Table_knowledge_Airtable', companyId, brandId, accountEmail, baseId],
  },
};
