import { AxiosRequestConfig } from 'axios';

import { ApiErrorResponse } from '@/api/models/dtos/api.dto';

import { handleApiError } from './api-error-handler';
import { axiosInstance } from './axios-instance';

export async function apiGet<T>({
  endpoint,
  queryParams,
  config,
}: {
  endpoint: string;
  queryParams?: Record<string, unknown>;
  config?: AxiosRequestConfig;
}) {
  try {
    const response = await axiosInstance.get<T>(endpoint, {
      params: queryParams,
      ...config,
    });
    return response.data;
  } catch (error) {
    throw handleApiError(error);
  }
}

export async function apiPost<T, D = unknown>({
  endpoint,
  queryParams,
  payload,
  config,
}: {
  endpoint: string;
  queryParams?: Record<string, unknown>;
  payload?: D;
  config?: AxiosRequestConfig;
}) {
  try {
    const response = await axiosInstance.post<T>(endpoint, payload, {
      params: queryParams,
      ...config,
    });

    if (
      typeof response.data === 'object' &&
      response.data !== null &&
      'error' in response.data
    ) {
      new Error((response.data as ApiErrorResponse).error);
    }

    return response.data as T;
  } catch (error) {
    throw handleApiError(error);
  }
}

export async function apiPut<T, D = unknown>(
  url: string,
  data: D,
  config?: { params?: Record<string, unknown> }
): Promise<T> {
  try {
    const response = await axiosInstance.put<T>(url, data, config);
    return response.data;
  } catch (error) {
    throw handleApiError(error);
  }
}

export async function apiPatch<T, D = unknown>(
  url: string,
  data: D,
  config?: { params?: Record<string, unknown> }
): Promise<T> {
  try {
    const response = await axiosInstance.patch<T>(url, data, config);
    return response.data;
  } catch (error) {
    throw handleApiError(error);
  }
}

export async function apiDelete<T>({
  endpoint,
  config,
}: {
  endpoint: string;
  config?: AxiosRequestConfig;
}): Promise<T> {
  try {
    const response = await axiosInstance.delete<T>(endpoint, {
      ...config,
    });
    return response.data;
  } catch (error) {
    throw handleApiError(error);
  }
}
