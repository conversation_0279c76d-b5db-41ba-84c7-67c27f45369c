import { useTranslations } from 'next-intl';
import { z } from 'zod';

export const signupSchema = (t: ReturnType<typeof useTranslations>) =>
  z
    .object({
      UserName: z.string().min(3, {
        message: t('form.username_min_length'),
      }),
      UserEmail: z.string().email(t('form.email_required')),
      UserPassword: z.string().min(8, {
        message: t('form.password_min_length'),
      }),
      UserPasswordConfirm: z.string(),
      terms: z.boolean(),
    })
    .refine((data) => data.UserPassword === data.UserPasswordConfirm, {
      message: t('form.password_mismatch'),
      path: ['UserPasswordConfirm'],
    });

export type SignupSchema = z.infer<ReturnType<typeof signupSchema>>;
