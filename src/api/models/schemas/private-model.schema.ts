import { useTranslations } from 'next-intl';
import { z } from 'zod';

const basePrivateModelSchema = (t: ReturnType<typeof useTranslations>) =>
  z.object({
    Tags: z.array(z.string()).min(1, { message: t('text-schema.topic-error') }),
    CompanyId: z.string(),
    BrandId: z.string(),
    UserEmail: z.string(),
    ModelName: z
      .string()
      .min(1, { message: t('text-schema.model-name-error') }),
    Description: z
      .string()
      .min(1, { message: t('text-schema.model-description-error') }),
  });

const socialMediaDataSchema = (t: ReturnType<typeof useTranslations>) =>
  z
    .array(
      z.object({
        platform: z.enum(['facebook', 'instagram', 'linkedin', 'x'], {
          errorMap: () => ({
            message: t('text-schema.platform-error'),
          }),
        }),
        pageName: z.string().nonempty(t('text-schema.pageName-error')),
      })
    )
    .min(1, 'At least one Social Media is required');

export const prepareTextModelSchema = (t: ReturnType<typeof useTranslations>) =>
  z.object({
    ...basePrivateModelSchema(t).shape,
    SocialMediaData: socialMediaDataSchema(t),
  });

export type PrepareTextModelSchema = z.infer<
  ReturnType<typeof prepareTextModelSchema>
>;

export const saveTextModelSchema = (t: ReturnType<typeof useTranslations>) =>
  z.object({
    ...basePrivateModelSchema(t).shape,
    SocialMediaData: z
      .array(
        z.object({
          platform: z.enum(['facebook', 'instagram', 'linkedin', 'x'], {
            errorMap: () => ({
              message: t('text-schema.platform-error'),
            }),
          }),
          text: z.string().optional(),
        })
      )
      .min(1, 'At least one Social Media is required'),
  });

export type SaveTextModelSchema = z.infer<
  ReturnType<typeof saveTextModelSchema>
>;

export const prepareImageModelSchema = (
  t: ReturnType<typeof useTranslations>
) =>
  z.object({
    files: z
      .array(z.custom<File>())
      .min(10, 'Please select at least 10 images')
      .max(20, 'Please select up to 20 files')
      .refine((files) => files.every((file) => file.size <= 5 * 1024 * 1024), {
        message: 'File size must be less than 5MB',
        path: ['files'],
      }),
    modelRequest: z.object({
      ...basePrivateModelSchema(t).shape,
      Type: z.string().min(1, { message: 'Image Category is required' }),
      Keyword: z.string().min(1, { message: 'Keyword is required' }),
      Object: z.string().min(1, { message: 'Object Name is required' }),
      ImageUrlsUpload: z.array(z.string()).nullable(),
    }),
  });

export type PrepareImageModelSchema = z.infer<
  ReturnType<typeof prepareImageModelSchema>
>;

export const imageListSchema = (t: ReturnType<typeof useTranslations>) =>
  z.object({
    ...basePrivateModelSchema(t).omit({
      ModelName: true,
      Description: true,
      Tags: true,
    }).shape,
    ModelId: z.string(),
    Index: z.number(),
    RawGcsUrl: z.string(),
    RawPublicUrl: z.string(),
    CleanGcsUrl: z.string(),
    CleanPublicUrl: z.string(),
    Caption: z.string(),
    LastModifTimestamp: z.date(),
    CreationTimestamp: z.date(),
  });

export type ImageListSchema = z.infer<ReturnType<typeof imageListSchema>>;

export const saveImageModelSchema = (t: ReturnType<typeof useTranslations>) =>
  z.object({
    ...basePrivateModelSchema(t).shape,
    ModelId: z.string(),
    BaseModel: z.string(),
    Keyword: z.string().min(1, { message: 'Keyword is required' }),
    Object: z.string().min(1, { message: 'Object Name is required' }),
    Type: z.string().min(1, { message: 'Image Category is required' }),
    ImageUrlsUpload: z.array(z.string()).nullable(),
    ImageList: z.array(imageListSchema(t)).nullable(),
    DummyRequest: z.boolean().default(false),
  });

export type SaveImageModelSchema = z.infer<
  ReturnType<typeof saveImageModelSchema>
>;
