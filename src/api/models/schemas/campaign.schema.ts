import { z } from 'zod';

export const createCampaignSchema = () =>
  z.object({
    CompanyId: z.string().nonempty('CompanyId is required'),
    BrandId: z.string().nonempty('BrandId is required'),
    UserEmail: z.string().email('UserEmail is required'),
    Platforms: z
      .array(z.enum(['facebook', 'instagram', 'linkedin', 'x']))
      .min(1, 'At least one platform is required'),
    Name: z.string().nonempty('Name is required'),
    Description: z.string().nonempty('Description is required'),
    Color: z.string().optional(),
    Objective: z.string().optional(),
    StartDate: z.date().optional(),
    EndDate: z.date().optional(),
    Knowledges: z.array(z.string()).optional(),
    Audiences: z.array(z.string()).optional(),
    Period: z.string().optional(),
    Frequency: z.string().optional(),
    Language: z.string().optional(),
    MarketingStrategy: z.string().optional(),
    Audience: z.array(z.string()).optional(),
    SocialMedias: z.array(z.string()).optional(),
    TimeZoneRegion: z.string().optional(),
    Evergreen: z
      .object({
        WithEvergreen: z.boolean(),
        Topics: z.array(z.string()),
        RegionOrCountries: z.array(z.string()),
      })
      .optional(),
    Keywords: z.array(z.string()).optional(),
    ImageOptions: z
      .object({
        Format: z.string(),
        Dimensions: z.string(),
      })
      .optional(),
    Proposal: z.array(z.any()).optional(),
  });

export type CreateCampaignSchema = z.infer<
  ReturnType<typeof createCampaignSchema>
>;

export const createAICampaignSchema = () =>
  createCampaignSchema().omit({
    Proposal: true,
  });

export type CreateAICampaignSchema = z.infer<
  ReturnType<typeof createAICampaignSchema>
>;
