import { useTranslations } from 'next-intl';
import { z } from 'zod';

export const knowledgeScrapingWebsiteSchema = (
  t: ReturnType<typeof useTranslations>
) =>
  z.object({
    url: z.string().min(1, t('schema.url_required')),
    title: z.string().min(1, t('schema.name_required')),
  });

export type KnowledgeScrapingWebsiteSchema = z.infer<
  ReturnType<typeof knowledgeScrapingWebsiteSchema>
>;

const baseKnowledgeSchema = (t: ReturnType<typeof useTranslations>) =>
  z.object({
    CompanyId: z.string(),
    BrandId: z.string(),
    UserEmail: z.string().email(),
    Type: z.enum([
      'text_plain',
      'text_website',
      'text_doc',
      'media_logo',
      'media_color',
      'audience',
    ]),
    Name: z.string().nonempty(t('schema.name_required')),
  });

export type BaseKnowledgeSchema = z.infer<
  ReturnType<typeof baseKnowledgeSchema>
>;

export const createKnowledgeTextSchema = (
  t: ReturnType<typeof useTranslations>
) =>
  z.object({
    knowledge: z.object({
      ...baseKnowledgeSchema(t).shape,
      Type: z.literal('text_plain'),
      Text: z.object({
        Title: z.string().nonempty(t('schema.text_title_required')),
        Description: z.string().nonempty(t('schema.text_content_required')),
        Labels: z.array(z.string()).optional(),
      }),
    }),
  });

export type CreateKnowledgeTextSchema = z.infer<
  ReturnType<typeof createKnowledgeTextSchema>
>;

export const createKnowledgeWebsiteSchema = (
  t: ReturnType<typeof useTranslations>
) =>
  z.object({
    knowledge: z.object({
      ...baseKnowledgeSchema(t).shape,
      Type: z.literal('text_website'),
      Website: z.object({
        Title: z.string().nonempty(t('schema.website_title_required')),
        Link: z.string().url().nonempty(t('schema.website_url_required')),
        Description: z.string(),
      }),
    }),
  });

export type CreateKnowledgeWebsiteSchema = z.infer<
  ReturnType<typeof createKnowledgeWebsiteSchema>
>;

export const createKnowledgeFileSchema = (
  t: ReturnType<typeof useTranslations>
) =>
  z.object({
    knowledge: z.object({
      ...baseKnowledgeSchema(t).shape,
      Type: z.literal('text_doc'),
      Doc: z.object({
        Title: z.string().nonempty(t('schema.document_title_required')),
        Type: z.string().regex(/^(pdf|docx?|txt)$/i),
      }),
    }),
    file: z.instanceof(File, { message: t('schema.file_required') }),
  });

export type CreateKnowledgeFileSchema = z.infer<
  ReturnType<typeof createKnowledgeFileSchema>
>;

export const createKnowledgeLogoSchema = (
  t: ReturnType<typeof useTranslations>
) =>
  z.object({
    knowledge: z.object({
      ...baseKnowledgeSchema(t).shape,
      Type: z.literal('media_logo'),
      Logo: z.object({
        Name: z.string().nonempty(t('schema.logo_name_required')),
      }),
    }),
    file: z.instanceof(File, { message: t('schema.file_required') }),
  });

export type CreateKnowledgeLogoSchema = z.infer<
  ReturnType<typeof createKnowledgeLogoSchema>
>;

export const createKnowledgeColorSchema = (
  t: ReturnType<typeof useTranslations>
) =>
  z.object({
    knowledge: z.object({
      ...baseKnowledgeSchema(t).shape,
      Type: z.literal('media_color'),
      Color: z
        .array(
          z.object({
            Index: z.number(),
            ColorHex: z.string(),
          })
        )
        .min(1, t('schema.minimum_color_length'))
        .max(6, t('schema.maximum_color_length')),
    }),
  });

export type CreateKnowledgeColorSchema = z.infer<
  ReturnType<typeof createKnowledgeColorSchema>
>;

export const audienceSchema = (t: ReturnType<typeof useTranslations>) =>
  z.object({
    knowledge: z.object({
      ...baseKnowledgeSchema(t).shape,
      Type: z.literal('audience'),
      Audience: z.object({
        Name: z.string().nonempty(t('schema.audience_name_required')),
        Gender: z.enum(['male', 'female', 'neutral']),
        SpendingBehavior: z.array(z.string()).optional(),
        InterestAndPref: z.array(z.string()).optional(),
        BusinessIndustry: z.array(z.string()).optional(),
        Business: z.boolean(),
        AgeRange: z
          .array(z.number())
          .min(2, t('schema.min_value_age_required'))
          .max(2, t('schema.max_value_age_required')),
      }),
    }),
  });

export type AudienceSchema = z.infer<ReturnType<typeof audienceSchema>>;

export const createKnowledgeAudienceSchema = (
  t: ReturnType<typeof useTranslations>
) =>
  audienceSchema(t).merge(
    z.object({
      knowledge: z.object({
        ...audienceSchema(t).shape.knowledge.shape,
        Audience: z.object({
          ...audienceSchema(t)
            .shape.knowledge.shape.Audience.omit({
              AgeRange: true,
            })
            .extend({
              AgeMin: z.number().min(18, t('schema.min_age')),
              AgeMax: z.number().max(60, t('schema.max_age')),
            }).shape,
        }),
      }),
    })
  );

export type CreateKnowledgeAudienceSchema = z.infer<
  ReturnType<typeof createKnowledgeAudienceSchema>
>;

export const updateKnowledgeTextSchema = (
  t: ReturnType<typeof useTranslations>
) =>
  z.object({
    knowledge: z.object({
      ...createKnowledgeTextSchema(t).shape.knowledge.shape,
      Id: z.string().nonempty(t('schema.id_required')),
    }),
  });

export type UpdateKnowledgeTextSchema = z.infer<
  ReturnType<typeof updateKnowledgeTextSchema>
>;

export const updateKnowledgeWebsiteSchema = (
  t: ReturnType<typeof useTranslations>
) =>
  z.object({
    knowledge: z.object({
      ...createKnowledgeWebsiteSchema(t).shape.knowledge.shape,
      Id: z.string().nonempty(t('schema.id_required')),
    }),
  });

export type UpdateKnowledgeWebsiteSchema = z.infer<
  ReturnType<typeof updateKnowledgeWebsiteSchema>
>;

export const updateKnowledgeFileSchema = (
  t: ReturnType<typeof useTranslations>
) =>
  z.object({
    knowledge: z.object({
      ...createKnowledgeFileSchema(t).shape.knowledge.shape,
      Id: z.string().nonempty(t('schema.id_required')),
    }),
    file: z.instanceof(File, { message: t('schema.file_optional') }).optional(),
  });

export type UpdateKnowledgeFileSchema = z.infer<
  ReturnType<typeof updateKnowledgeFileSchema>
>;

export const updateKnowledgeLogoSchema = (
  t: ReturnType<typeof useTranslations>
) =>
  z.object({
    knowledge: z.object({
      ...createKnowledgeLogoSchema(t).shape.knowledge.shape,
      Id: z.string().nonempty(t('schema.id_required')),
    }),
    file: z.instanceof(File, { message: t('schema.file_optional') }).optional(),
  });

export type UpdateKnowledgeLogoSchema = z.infer<
  ReturnType<typeof updateKnowledgeLogoSchema>
>;

export const updateKnowledgeColorSchema = (
  t: ReturnType<typeof useTranslations>
) =>
  z.object({
    knowledge: z.object({
      ...createKnowledgeColorSchema(t).shape.knowledge.shape,
      Id: z.string().nonempty(t('schema.id_required')),
    }),
  });

export type UpdateKnowledgeColorSchema = z.infer<
  ReturnType<typeof updateKnowledgeColorSchema>
>;

export const knowledgeAudienceUpdatedSchema = (
  t: ReturnType<typeof useTranslations>
) =>
  audienceSchema(t).merge(
    z.object({
      knowledge: z.object({
        ...audienceSchema(t).shape.knowledge.shape,
        Id: z.string().nonempty(t('schema.id_required')),
        Audience: z.object({
          ...audienceSchema(t)
            .shape.knowledge.shape.Audience.omit({
              AgeRange: true,
            })
            .extend({
              AgeMin: z.number().min(18, t('schema.min_age')),
              AgeMax: z.number().max(60, t('schema.max_age')),
            }).shape,
        }),
      }),
    })
  );

export type KnowledgeAudienceUpdatedSchema = z.infer<
  ReturnType<typeof createKnowledgeAudienceSchema>
>;
export const updateKnowledgeAudienceSchema = (
  t: ReturnType<typeof useTranslations>
) =>
  z.object({
    knowledge: z.object({
      ...audienceSchema(t).shape.knowledge.shape,
      Id: z.string().nonempty(t('schema.id_required')),
    }),
  });

export type UpdateKnowledgeAudienceSchema = z.infer<
  ReturnType<typeof updateKnowledgeAudienceSchema>
>;

export const accountsKnowledgeSchema = (
  t: ReturnType<typeof useTranslations>
) =>
  baseKnowledgeSchema(t).omit({
    UserEmail: true,
    Type: true,
    Name: true,
  });

export type AccountsKnowledgeSchema = z.infer<
  ReturnType<typeof accountsKnowledgeSchema>
>;

export const selectFileSharepointSchema = (
  t: ReturnType<typeof useTranslations>
) =>
  accountsKnowledgeSchema(t).extend({
    FolderId: z.string(),
    DriveId: z.string(),
    SiteId: z.string(),
    AccountEmail: z.string().email(t('schema.adresse_email_invalid')),
    Title: z.string(),
    FileId: z.string(),
    FileName: z.string(),
  });

export type SelectFileSharepointSchema = z.infer<
  ReturnType<typeof selectFileSharepointSchema>
>;

export const selectFileGoogleDriveSchema = (
  t: ReturnType<typeof useTranslations>
) =>
  accountsKnowledgeSchema(t).extend({
    AccountEmail: z.string().email(t('schema.adresse_email_invalid')),
    Title: z.string(),
    FileId: z.string(),
    FileName: z.string(),
  });

export type SelectFileGoogleDriveSchema = z.infer<
  ReturnType<typeof selectFileGoogleDriveSchema>
>;

export const selectTableAirtableSchema = (
  t: ReturnType<typeof useTranslations>
) =>
  accountsKnowledgeSchema(t).extend({
    AccountEmail: z.string().email(t('schema.adresse_email_invalid')),
    BaseId: z.string(),
    TableId: z.string(),
    TableName: z.string(),
    Title: z.string(),
  });

export type SelectTableAirtableSchema = z.infer<
  ReturnType<typeof selectTableAirtableSchema>
>;
