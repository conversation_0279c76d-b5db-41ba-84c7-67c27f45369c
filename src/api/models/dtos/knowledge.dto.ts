import { BaseKnowledgeSchema } from '../schemas/knowledge.schema';

export interface TextDto {
  Title: string;
  Description: string;
  Labels: string[] | null;
}

export interface AudienceDto {
  Name: string;
  AgeMin: number;
  AgeMax: number;
  Gender: string;
  InterestAndPref: string[] | null;
  SpendingBehavior: string[] | null;
  Business: boolean;
  BusinessIndustry: string[] | null;
}

export interface ColorItemDto {
  Index: number;
  ColorHex: string;
}

export interface FontDto {
  Name: string;
  Family: string;
  Size: number;
  Bold: boolean;
  GcsLink: string;
  GcsLinkPublic: string;
}

export interface LogoDto {
  Name: string;
  GcsLink: string;
  GcsLinkPublic: string;
}

export interface WebsiteDto {
  Title: string;
  Link: string;
  Description: string;
}

export interface DocDto {
  Title: string;
  Type: string;
  Description: string;
  GcsLink: string;
  GcsLinkPublic: string;
}

export interface KnowledgeResponse {
  Id: string;
  UserEmail: string;
  CompanyId: string;
  BrandId: string;
  Type: BaseKnowledgeSchema['Type'];
  Name: string;
  Text: TextDto;
  Audience: AudienceDto;
  Color: ColorItemDto[] | null;
  Font: FontDto;
  Logo: LogoDto;
  Website: WebsiteDto;
  Doc: DocDto;
  File: string;
  FileName: string;
  LastModifTimestamp: string;
  CreationTimestamp: string;
  Favorite: boolean;
}

export interface PrepareUrlContentKnowledgeResponse {
  HtmlText: string;
}

export interface SharePointResponse {
  id: string;
  name: string;
  url: string;
}

export interface GoogleDriveResponse {
  id: string;
  name: string;
}

export interface AirtableResponse {
  id: string;
  name: string;
}
