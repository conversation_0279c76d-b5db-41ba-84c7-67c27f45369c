import { PostSuggestion } from '@/api/models/dtos/post.dto';
import {
  PlatformType,
  SocialMediaMetrics,
} from '@/api/models/dtos/social-media-insights.dto';

import { GenerateNewPostSuggestionsSchema } from '@/api/models/schemas/creative.schema';

export interface CampaignResponse {
  Id: string;
  CompanyId: string;
  BrandId: string;
  UserEmail: string;
  CampaignType: CampaignType;
  Name: string;
  Description: string;
  Status: CampaignStatus;
  Platforms: PlatformType[];
  PlateformsPageIds: null;
  Objective: string;
  Color?: string;
  StartDate: Date;
  EndDate: Date;
  // TODO: Apply the Knowledge type once it's merge into dev
  Knowledges: string[];
  Audiences: null;
  AICampaignFromId: string;
  Proposal: AICampaignProposal[] | null;
  PostsId: string[];
  Posts: PostSuggestion[] | null;
  Favorite: boolean;
  FbInsights: SocialMediaMetrics;
  InstaInsights: SocialMediaMetrics;
  LinkedinInsights: SocialMediaMetrics;
  XInsights: SocialMediaMetrics;
  ViewsStat: CampaignStats;
  ReachStat: CampaignStats;
  InteractionStat: CampaignStats;
  LastModifTimestamp: Date;
  CreationTimestamp: Date;
}

type CampaignType = 'basic' | 'ai';

type CampaignStatus = 'Pending' | 'In progress' | 'Completed';

interface CampaignStats {
  Title: string;
  Total: number;
  Trend: number;
  TrendStatus: 'positive' | 'negative' | 'neutral';
}

export interface AICampaignProposal {
  Id: string;
  CampaignId: string;
  ResponseId: string;
  Name: string;
  Description: string;
  MarketingStrategy: string;
  ContentCreaForm: GenerateNewPostSuggestionsSchema;
  DateTime: Date;
  TimeZoneRegion: string;
  TimeZoneOffsetString: string;
  // Note: It's so weird that we don't have unified type for social media across the app
  SocialMedia: 'Facebook' | 'Instagram' | 'LinkedIn' | 'X';
  PageId: string;
  PageName: string;
  PostId: string;
}

// Note: I'm Not sure why we do have this here
// Remove this eslint-disable rule once checked
// eslint-disable-next-line @typescript-eslint/no-unused-vars
interface AICampaignForm {
  Id: string;
  CompanyId: string;
  BrandId: string;
  UserEmail: string;
  Name: string;
  StartDate: Date;
  TimeZoneRegion: string;
  Description: string;
  Objective: string;
  Period: string;
  Frequency: string;
  Language: string;
  Audience: string[];
  Platforms: PlatformType[];
  MarketingStrategy: string;
  SocialMedias: PlateformPageId[];
  Knowledges: string[];
  Evergreen: Evergreen;
  Keywords: string[];
  ImageOptions: GenerateNewPostSuggestionsSchema['ImageOptions'];
  LastModifTimestamp: Date;
  CreationTimestamp: Date;
}

interface Evergreen {
  WithEvergreen: boolean;
  Topics: string[];
  RegionOrCountries: string[];
}

interface PlateformPageId {
  SocialMedia: PlatformType;
  PageId: string;
  PageName: string;
}
