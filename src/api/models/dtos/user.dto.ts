import { ApiResponse } from '@/api/models/dtos/api.dto';

export type Role = 'admin' | 'user';
export type ConnectionMode = 'email' | 'google' | 'facebook' | 'microsoft';

export interface User {
  UserID: string;
  CompanyId: string;
  UserName: string;
  UserPassword: string;
  Quota: number;
  UserEmail: string;
  UserGoogleId?: string;
  Role: Role;
  Department: string;
  IsInvited: boolean;
  isActive: boolean;
  connectionMode: ConnectionMode;
  isFirstConnection: boolean;
  createdAt: string;
  updatedAt: string;
}

export type UserResponse = ApiResponse<User>;
export type CreateUserResponse = ApiResponse<{ userId: string }>;
export type UpdateUserResponse = ApiResponse<{ updatedAt: string }>;
