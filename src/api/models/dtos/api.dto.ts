export type ApiSuccessResponse<T> = T;

export interface ApiErrorResponse {
  error: string;
  // Add any additional error fields your API might return
  // code?: string;
  // details?: Record<string, unknown>;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  pages: number;
  // Include other pagination fields if present
  // page?: number;
  // limit?: number;
}

// Generic response type that can handle both success and error
export type ApiResponse<T> = ApiSuccessResponse<T> | ApiErrorResponse;
