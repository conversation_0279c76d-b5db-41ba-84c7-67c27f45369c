import {
  ImageListSchema,
  PrepareTextModelSchema,
} from '../schemas/private-model.schema';

export interface CheckLimitResponse {
  status: boolean;
}

export interface PrepareTextPrivateModelResponse {
  text: string;
  platform: PrepareTextModelSchema['SocialMediaData'][number]['platform'];
}

export interface PrepareImageModelResponse {
  CompanyId: string;
  BrandId: string;
  UserEmail: string;
  ModelId: string;
  ModelName: string;
  Keyword: string;
  Object: string;
  Type: string;
  ImageUrlsUpload: string[] | null;
  ImageList: ImageListSchema[] | null;
  Tags: string[] | null;
  BaseModel: string;
  Description: string;
  DummyRequest: boolean;
}

export interface LLMConfig {
  train_data: string | null;
  validation_data?: string;
  data_list: string[] | null;
  knowledge_list: string[] | null;
}

export interface PrivateModelResponse {
  Id: string;
  CompanyId: string;
  BrandId: string;
  UserEmail: string;
  ModelOwner: string;
  ModelId: string;
  ModelName: string;
  ModelDescription: string;
  ModelType: 'LLM' | 'IMAGE';
  ModelSpec: string;
  ModelStatus: string;
  TrainingId: string;
  TrainingMessage: string;
  Favorite: boolean;
  LLMRawData: PrepareTextPrivateModelResponse[] | null;
  LLMConfig: LLMConfig;
  ImageModelRequest: PrepareImageModelResponse;
  ModelPlatform: string;
  CreationTimestamp: Date;
  LastModifTimestamp: Date;
}
