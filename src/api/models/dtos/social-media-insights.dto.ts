export type PlatformType = 'facebook' | 'instagram' | 'x' | 'linkedin';

export interface SocialMediaMetrics {
  EngagedUsers: number;
  Engagement: number;
  Impression: number;
  Reach: number;
  EngagementPercentage: number;
  CTAPercentage: number;
  Reaction: number;
  Click: number;
  LikeTotal: number;
  Comment: number;
  Share: number;
  Saved: number;
  Like: number;
  Love: number;
  Wow: number;
  Haha: number;
  Sorry: number;
  Anger: number;
  Retweet: number;
  Reply: number;
  Quote: number;
  UpdateTime: string;
  Platform: PlatformType;
}

export interface SocialMediaInsightsResponse {
  CompanyId: string;
  BrandId: string;
  PageId: string;
  DayInsight: SocialMediaMetrics[];
  LifeTime: SocialMediaMetrics[];
  DailyImpressionPercentage: number;
  DailyTotalReachPercentage: number;
  DailyEngagementPercentage: number;
  DailyInteractionPercentage: number;
  UpdateTime: string;
}
