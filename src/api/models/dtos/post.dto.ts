import { GenerateNewPostSuggestionsSchema } from '@/api/models/schemas/creative.schema';

export interface PostsSuggestionsResponse {
  results: PostSuggestion[];
}

export interface PostSuggestion {
  Id: string;
  CreaFormId: string;
  UserEmail: string;
  CompanyId: string;
  BrandId: string;
  Ad: Ad;
  AdType: GenerateNewPostSuggestionsSchema['GenerationType'];
  Status: 'Draft' | 'Ready' | 'Approved' | 'Scheduled' | 'Posted';
  SocialMedia: 'facebook' | 'instagram' | 'linkedin' | 'x';
  Medias: Media[] | null;
  Saved: boolean;
  Favorite: boolean;
  Score: Score;
  LastModifTimestamp: Date;
  CreationTimestamp: Date;
}

export interface Ad {
  title: string;
  message: Message;
  imagesprompts: Imagesprompt[];
  language: string;
  textprompt: string;
}

export interface Imagesprompt {
  index: number;
  prompt: string;
}

export interface Message {
  Text: string;
  URL: string;
}

export interface Media {
  Index: number;
  GcsLink: string;
  GcsLinkPublic: string;
  Width: number;
  Height: number;
  MediaType: string;
}

export interface Score {
  Id: string;
  ContentId: string;
  FormId: string;
  CompanyId: string;
  BrandId: string;
  UserEmail: string;
  PopularityScore: string;
  AwarenessScore: string;
  ConsiderationScore: string;
  ConversionScore: string;
  LoyaltyScore: string;
  Explanation: string;
  LastModifTimestamp: Date;
  CreationTimestamp: Date;
}

export type PromptType = 'IMAGE' | 'TEXT';
