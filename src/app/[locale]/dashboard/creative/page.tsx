'use client';

import { useImageSelector } from '@/stores/creative-image-selector-store';
import { useCreativePostStore } from '@/stores/creative-post-store';

import { ImageChoiceCard } from '@/components/creative/molecules/choice-card';
import { PopularityCard } from '@/components/creative/molecules/popularity-card';
import { PostSubmissionControls } from '@/components/creative/molecules/post-submission-controls';
import { NewCreativeCard } from '@/components/creative/organisms/new-creative-card';
import { PostSuggestionPreviewCard } from '@/components/creative/organisms/post-suggestion-preview-card';
import { Card } from '@/components/ui/card';

export default function Page() {
  const currentPostSuggestion = useCreativePostStore(
    (s) => s.currentPostSuggestion
  );

  const { isSelectorOpen: isTypeSelectorOpen } = useImageSelector('type')();

  const { isSelectorOpen: isStyleSelectorOpen } = useImageSelector('style')();

  const { isSelectorOpen: isEffectsSelectorOpen } =
    useImageSelector('effect')();

  const content = (() => {
    switch (true) {
      case isTypeSelectorOpen:
        return <ImageChoiceCard selectorType='type' />;
      case isStyleSelectorOpen:
        return <ImageChoiceCard selectorType='style' />;
      case isEffectsSelectorOpen:
        return <ImageChoiceCard selectorType='effect' />;
      default:
        return <PostSuggestionPreviewCard />;
    }
  })();

  return (
    <div className='flex h-full'>
      <div className='flex-1'>
        <main className='flex h-full flex-1 flex-col gap-4 overflow-auto px-4'>
          <div className='grid h-full grid-cols-[5fr_10fr_2.5fr] grid-rows-[1fr_auto] gap-4 overflow-hidden py-1'>
            <Card className='row-span-2 flex h-full flex-col overflow-hidden'>
              <NewCreativeCard />
            </Card>

            <Card className='row-span-2 flex h-full w-full flex-col overflow-hidden'>
              {content}
            </Card>

            <Card className='flex flex-col overflow-hidden pt-4'>
              <PopularityCard post={currentPostSuggestion} />
            </Card>

            <Card className='col-start-3 row-start-2'>
              <PostSubmissionControls />
            </Card>
          </div>
        </main>
      </div>
    </div>
  );
}
