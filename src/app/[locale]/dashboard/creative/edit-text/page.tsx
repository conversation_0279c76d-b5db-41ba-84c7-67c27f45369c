'use client';

import { useEffect } from 'react';

import { useRouter } from '@/i18n/routing';
import { useCreativePostStore } from '@/stores/creative-post-store';

import { PopularityCard } from '@/components/creative/molecules/popularity-card';
import { PostEditTextControls } from '@/components/creative/molecules/post-edit-text-controls';
import { EditPostTextPreviewCard } from '@/components/creative/organisms/edit-post-text-preview-card';
import { EditTextCreativeCard } from '@/components/creative/organisms/edit-text-creative-card';
import { Card } from '@/components/ui/card';

export default function Page() {
  const router = useRouter();

  const currentPostSuggestion = useCreativePostStore(
    (s) => s.currentPostSuggestion
  );
  const editPostSuggestion = useCreativePostStore((s) => s.editPostSuggestion);

  useEffect(() => {
    if (!currentPostSuggestion) {
      router.back();
    }
  }, [currentPostSuggestion, router]);

  return (
    <div className='flex h-full'>
      <div className='flex-1'>
        <main className='flex h-full flex-1 flex-col gap-4 overflow-auto px-4'>
          <div className='grid h-full grid-cols-[5fr_10fr_2.5fr] grid-rows-[1fr_auto] gap-4 overflow-hidden py-1'>
            <Card className='row-span-2 flex h-full flex-col overflow-hidden'>
              <EditTextCreativeCard />
            </Card>

            <Card className='row-span-2 flex h-full w-full flex-col overflow-hidden'>
              <EditPostTextPreviewCard />
            </Card>

            <Card className='flex flex-col overflow-hidden pt-4'>
              <PopularityCard
                post={editPostSuggestion ?? currentPostSuggestion}
              />
            </Card>

            <Card className='col-start-3 row-start-2'>
              <PostEditTextControls />
            </Card>
          </div>
        </main>
      </div>
    </div>
  );
}
