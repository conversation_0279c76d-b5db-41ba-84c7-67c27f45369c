import PrivateModelDetail from '@/components/private-model/private-model-detail';
import { ScrollArea } from '@/components/ui/scroll-area';

interface PageProps {
  params: Promise<{ modelId: string }>;
}

export default async function Page({ params }: PageProps) {
  const { modelId } = await params;

  return (
    <div className='flex h-full'>
      <ScrollArea className='flex-1'>
        <PrivateModelDetail modelId={modelId} />
      </ScrollArea>
    </div>
  );
}
