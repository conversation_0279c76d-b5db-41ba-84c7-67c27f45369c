import type { Metadata } from 'next';

import { OverviewHeader } from '@/components/dashboard/overview-header';
import { SidebarRight } from '@/components/dashboard/sidebar-right/sidebar-right';
import SocialMediaChart from '@/components/dashboard/social-media-chart';
import SocialMediaInsights from '@/components/dashboard/social-media-insights';
import { ScrollArea } from '@/components/ui/scroll-area';

export const metadata: Metadata = {
  title: 'Kwore | Dashboard',
  description: 'Welcome to Kwore',
};

async function getImpressionKpi() {
  const res = await fetch(
    'http://localhost:3000/api/social-stats/impressions',
    {
      next: {
        revalidate: 10,
      },
    }
  );
  if (!res.ok) throw new Error('Failed to fetch social stats');
  return res.json();
}

async function getReactionsKpi() {
  const res = await fetch('http://localhost:3000/api/social-stats/reactions', {
    next: {
      revalidate: 10,
    },
  });
  if (!res.ok) throw new Error('Failed to fetch social stats');
  return res.json();
}

async function getReachKpi() {
  const res = await fetch('http://localhost:3000/api/social-stats/reach', {
    next: {
      revalidate: 10,
    },
  });
  if (!res.ok) throw new Error('Failed to fetch social stats');
  return res.json();
}

async function getInteractionsKpi() {
  const res = await fetch(
    'http://localhost:3000/api/social-stats/interactions',
    {
      next: {
        revalidate: 10,
      },
    }
  );
  if (!res.ok) throw new Error('Failed to fetch social stats');
  return res.json();
}

async function getPosts() {
  const res = await fetch('http://localhost:3000/api/posts', {
    next: {
      revalidate: 10,
    },
  });
  if (!res.ok) throw new Error('Failed to fetch posts');
  return res.json();
}

export default async function Page() {
  const [impressionsStats, reactionsStat, reachStat, interactionsStat] =
    await Promise.all([
      getImpressionKpi(),
      getReactionsKpi(),
      getReachKpi(),
      getInteractionsKpi(),
    ]);
  const posts = await getPosts();

  return (
    <main className='flex h-full'>
      <ScrollArea className='flex-1'>
        <main className='flex h-full flex-1 flex-col gap-4 overflow-auto px-4'>
          <OverviewHeader />

          <SocialMediaInsights />

          <div className='grid gap-4 sm:grid-cols-2 sm:gap-6'>
            <SocialMediaChart data={impressionsStats} title='impressions' />
            <SocialMediaChart data={reactionsStat} title='reactions' />
            <SocialMediaChart data={reachStat} title='reach' />
            <SocialMediaChart data={interactionsStat} title='interactions' />
          </div>
        </main>
      </ScrollArea>

      <SidebarRight data={posts} />
    </main>
  );
}
