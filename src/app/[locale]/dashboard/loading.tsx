import React from 'react';

import { Card } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';

export default function Loading() {
  return (
    <div className='flex'>
      <div className='flex flex-1 flex-col gap-4 p-4'>
        <Skeleton className='aspect-video h-12 w-full rounded-lg' />
        <div className='grid auto-rows-min gap-4 md:grid-cols-4'>
          {[...Array(4)].map((_, i) => (
            <Skeleton key={i} className='aspect-video rounded-xl' />
          ))}
        </div>

        <div className='grid auto-rows-min gap-4 md:grid-cols-2'>
          {[...Array(4)].map((_, i) => (
            <Skeleton key={i} className='aspect-video rounded-xl' />
          ))}
        </div>

        <Skeleton className='min-h-[100vh] flex-1 rounded-xl' />
      </div>

      <CalendarPosts />
    </div>
  );
}

export function CalendarPosts() {
  return (
    <div className='border-r-1 hidden w-[16.5rem] space-y-4 bg-background p-4 lg:block'>
      {/* Calendar Skeleton */}
      <div className='grid place-items-center'>
        <Skeleton className='h-6 w-32' /> {/* Month and Year */}
        <div className='grid grid-cols-7 gap-2 py-6'>
          {[...Array(42)].map((_, i) => (
            <Skeleton key={i} className='size-6 rounded-md' />
          ))}
        </div>
      </div>

      {/* Recent Posts Skeleton */}
      <div>
        <Skeleton className='mb-2 h-6 w-40' />
        <div className='space-y-4'>
          {[...Array(3)].map((_, i) => (
            <Card key={i} className='space-y-2 bg-background p-4'>
              <Skeleton className='h-4 w-24' />
              <Skeleton className='h-3 w-32' />
              <Skeleton className='h-4 w-full' />
              <Skeleton className='h-3 w-20' />
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
}
