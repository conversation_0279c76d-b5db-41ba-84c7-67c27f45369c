import { getLocale } from 'next-intl/server';
import { cookies } from 'next/headers';
import { NuqsAdapter } from 'nuqs/adapters/next/app';
import React from 'react';
import { isRtlLang } from 'rtl-detect';

import { DashboardTestIds } from '@/constants/test-ids';

import { Header } from '@/components/shared/layouts/dashboard/header/header';
import { SidebarLeft } from '@/components/shared/layouts/dashboard/sidebar-left/sidebar-left';
import { SidebarInset, SidebarProvider } from '@/components/ui/sidebar';

interface DashboardLayoutProps {
  children: React.ReactNode;
}

export default async function DashboardLayout({
  children,
}: DashboardLayoutProps) {
  const locale = await getLocale();
  const isRtl = isRtlLang(locale);

  const cookieStore = await cookies();
  const defaultOpen = cookieStore.get('sidebar_state')?.value === 'true';

  return (
    <NuqsAdapter>
      <SidebarProvider defaultOpen={defaultOpen} className='overflow-hidden'>
        <SidebarLeft
          side={isRtl ? 'right' : 'left'}
          collapsible='icon'
          data-testid={DashboardTestIds.left_sidebar}
        />
        <SidebarInset className='h-svh overflow-x-auto overflow-y-hidden ps-3'>
          <Header data-testid={DashboardTestIds.header} />
          {/* todo: check for the padding bottom value */}
          <div
            className='h-full overflow-hidden'
            data-testid={DashboardTestIds.children}
          >
            {children}
          </div>
        </SidebarInset>
      </SidebarProvider>
    </NuqsAdapter>
  );
}
