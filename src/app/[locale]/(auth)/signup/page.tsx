import type { Metadata } from 'next';

import { getTranslations } from 'next-intl/server';
import Image from 'next/image';
import React from 'react';

import { Link } from '@/i18n/routing';

import { LoginWithProvider } from '@/components/auth/forms/login-with-provider';
import SignupForm from '@/components/demo/signup-form';
import { Card, CardContent } from '@/components/ui/card';

export const metadata: Metadata = {
  title: 'Kwore | Signup',
  description: 'signup in Kwore',
};
export default async function SignupPage() {
  const t = await getTranslations();

  return (
    <Card className='w-full overflow-hidden rounded-none border-0 bg-transparent shadow-none sm:rounded-[3rem] sm:border sm:bg-background sm:shadow'>
      <CardContent className='grid p-0 md:grid-cols-2'>
        <div className='relative hidden md:block'>
          <Image
            src='/images/signup.png'
            alt='Image'
            className='absolute inset-0 h-full w-full object-fill'
            width={500}
            height={500}
          />

          <div className='absolute inset-0 flex w-4/5 flex-col justify-center gap-3 px-12 text-2xl text-background'>
            <h1 className='text-5xl font-bold'>
              {t('auth_image.signup_title')}
            </h1>
            <h2 className='text-xl font-normal'>
              {t('auth_image.signup_subtitle')}
            </h2>
          </div>
        </div>

        <div className='mx-auto space-y-2 py-6 sm:w-2/3'>
          <SignupForm />

          <div className='flex items-center justify-center gap-2 text-xs text-[#3F3F46]'>
            <span>{t('already_have_an_account')}</span>
            <Link
              href='/login'
              className='text-primary underline underline-offset-4'
            >
              {t('login')}
            </Link>
          </div>

          <div className='relative text-center text-sm after:absolute after:inset-0 after:top-1/2 after:z-0 after:flex after:items-center after:border-t after:border-border'>
            <span className='relative z-10 bg-background px-2 text-xs text-[#3F3F46]'>
              {t('or_continue_with')}
            </span>
          </div>

          <LoginWithProvider />
        </div>
      </CardContent>
    </Card>
  );
}
