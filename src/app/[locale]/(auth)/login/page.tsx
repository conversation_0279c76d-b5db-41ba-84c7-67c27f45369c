import type { Metada<PERSON> } from 'next';

import { getTranslations } from 'next-intl/server';
import Image from 'next/image';
import React from 'react';

import { Link } from '@/i18n/routing';

import LoginForm from '@/components/auth/forms/login-form';
import { Icons } from '@/components/shared/icons';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

export const metadata: Metadata = {
  title: 'Kwore | Login',
  description: 'Login to Kwore',
};

export default async function LoginPage() {
  const t = await getTranslations();

  return (
    <Card className='overflow-hidden rounded-none border-0 bg-transparent shadow-none sm:rounded-[3rem] sm:border sm:bg-background sm:shadow'>
      <CardContent className='grid p-0 md:grid-cols-2'>
        <div className='relative hidden md:block'>
          <Image
            src='/images/login.png'
            alt='Image'
            className='absolute inset-0 h-full w-full object-fill'
            width={500}
            height={500}
          />
        </div>

        <div className='mx-auto space-y-5 p-6 sm:w-3/4 md:px-6 md:py-14'>
          <LoginForm />

          <div className='flex items-center justify-center gap-2 text-sm text-muted-foreground'>
            <span>{t('need_to_create_account')}</span>
            <Link
              href='/signup'
              className='text-primary underline underline-offset-4'
            >
              {t('signup')}
            </Link>
          </div>

          <div className='relative text-center text-sm after:absolute after:inset-0 after:top-1/2 after:z-0 after:flex after:items-center after:border-t after:border-border'>
            <span className='relative z-10 bg-background px-2 text-muted-foreground'>
              {t('or_continue_with')}
            </span>
          </div>

          <div className='grid grid-cols-3 gap-4'>
            <Button variant='outline' className='rounded-lg'>
              <Icons.googleOfficial />
              <span className='text-xs font-light'>{t('google')}</span>
            </Button>
            <Button variant='outline' className='rounded-lg'>
              <Icons.microsoftOfficial />
              <span className='text-xs font-light'>{t('microsoft')}</span>
            </Button>

            <Button variant='outline' className='rounded-lg'>
              <Icons.linkedin />
              <span className='text-xs font-light'>{t('linkedin')}</span>
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
