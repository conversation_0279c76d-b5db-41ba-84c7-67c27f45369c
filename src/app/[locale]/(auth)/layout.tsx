import React from 'react';

import AuthFooter from '@/components/shared/layouts/auth/auth-footer';
import AuthHeader from '@/components/shared/layouts/auth/auth-header';

export default async function AuthLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className='flex min-h-svh flex-col items-center justify-between bg-[#F2F2FD] dark:bg-[#1E1E2E]'>
      <AuthHeader />
      <main className='h-full p-0 sm:w-4/6'>{children}</main>
      <AuthFooter />
    </div>
  );
}
