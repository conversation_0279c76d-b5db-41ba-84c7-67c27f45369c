export interface SocialMediaPost {
  text: string;
  platform: SocialPlatform;
  url?: string;
}

export type SocialPlatform = 'Facebook' | 'Instagram' | 'LinkedIn' | 'X';

export interface ScraperError extends Error {
  statusCode: number;
  details?: unknown;
}

export interface FacebookPost {
  text: string;
  url?: string;
  date?: string;
}

export interface InstagramPost {
  caption: string;
  url?: string;
  timestamp?: string;
}

export interface LinkedInPost {
  text: string;
  url?: string;
  timestamp?: string;
}

export interface XPost {
  text: string;
  url?: string;
  created_at?: string;
}
