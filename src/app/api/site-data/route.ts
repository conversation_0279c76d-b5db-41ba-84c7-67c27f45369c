import { ApifyClient } from 'apify-client';
import fetch from 'node-fetch';
import puppeteer from 'puppeteer';
import sharp from 'sharp';
import { parse } from 'svgson';

// Initialize the ApifyClient with API token
const client = new ApifyClient({
  token: process.env.APIFY_API_TOKEN, // Use environment variable for API token
});

export async function POST(request: Request) {
  const res = await request.json();
  // const htmlText = await scrapeWebsite(res.url)
  const htmlText = await scrapeWebsite(res.url);

  // Get web site
  const responseData = {
    HtmlText: htmlText?.content,
  };

  return Response.json(responseData);
}

async function scrapeWebsite(websiteUrl: string) {
  let browser;
  try {
    // Ensure the URL starts with http:// or https://
    if (!/^https?:\/\//i.test(websiteUrl)) {
      websiteUrl = 'https://' + websiteUrl;
    }

    // Modifiez la section de lancement du navigateur
    browser = await puppeteer.launch({
      executablePath:
        process.env.PUPPETEER_EXECUTABLE_PATH || puppeteer.executablePath(),
      defaultViewport: null,
      headless: true, // Enable headless mode
      args: [
        '--disable-popup-blocking',
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-gpu',
      ],
    });

    const page = await browser.newPage();
    await page.setViewport({ width: 1920, height: 1080 });

    await page.goto(websiteUrl, { waitUntil: 'networkidle0', timeout: 60000 });

    // Scroll to load lazy images
    await autoScroll(page);

    // Wait for images to load
    await new Promise((resolve) => setTimeout(resolve, 5000));

    const result = await page.evaluate(() => {
      // Extract text content
      const content = document.body.innerText.trim().replace(/\s+/g, ' ');

      // Extract image URLs
      const imgElements = Array.from(document.querySelectorAll('img'));

      const images = imgElements
        .map(
          (img) =>
            img.getAttribute('src') ||
            img.getAttribute('data-src') ||
            img.getAttribute('data-lazy') ||
            img.getAttribute('data-original') ||
            img.getAttribute('srcset')
        )
        .filter((src): src is string => src !== null)
        .flatMap((src) => {
          if (src.includes(',')) {
            // Handle srcset with multiple URLs
            return src.split(',').map((s) => s.trim().split(' ')[0]);
          } else {
            return [src];
          }
        })
        .map((src) => {
          try {
            return new URL(src, window.location.href).href;
          } catch {
            console.error(`Invalid URL: ${src}`);
            return null;
          }
        })
        .filter((url): url is string => url !== null);

      // Extract background images
      const bgImages = Array.from(document.querySelectorAll('*'))
        .map((el) => getComputedStyle(el).getPropertyValue('background-image'))
        .filter((url) => url && url !== 'none')
        .map((url) => url.slice(4, -1).replace(/["']/g, '')) // Remove url("...") or url('...')
        .map((src) => {
          try {
            return new URL(src, window.location.href).href;
          } catch {
            console.error(`Invalid URL: ${src}`);
            return null;
          }
        })
        .filter((url): url is string => url !== null);

      const allImages = [...images, ...bgImages];

      return { content, images: allImages };
    });

    // Analyze images
    const imageAnalysis = await analyzeImages(result.images);

    // Include the analysis in your result
    return {
      content: result.content,
      images: imageAnalysis,
    };
  } catch (error) {
    console.error(`Error scraping the website ${websiteUrl}:`, error);
    return null;
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}
function rgbToHex(r: number, g: number, b: number): string {
  return '#' + [r, g, b].map((x) => x.toString(16).padStart(2, '0')).join('');
}

async function analyzeImages(imageUrls: string[]) {
  const analysisPromises = imageUrls.map(async (imageUrl) => {
    try {
      // Validate URL
      new URL(imageUrl);

      // Fetch the image
      const response = await fetch(imageUrl);
      if (!response.ok) {
        console.error(
          `Failed to fetch image ${imageUrl}: ${response.statusText}`
        );
        return null;
      }

      // Determine image type from Content-Type header
      const contentType = response.headers.get('content-type') || '';
      const imageType = contentType.split(';')[0];

      console.log(`Content-Type for ${imageUrl}: ${imageType}`);

      if (!/^image\//.test(imageType)) {
        console.error(`Unsupported image type for ${imageUrl}: ${imageType}`);
        return null;
      }

      if (imageType === 'image/svg+xml') {
        // Handle SVG images
        const svgText = await response.text();

        // Parse the SVG and extract colors
        const svgObject = await parse(svgText);

        const colorsSet = new Set<string>();
        extractColors(svgObject, colorsSet);

        const hexColors = Array.from(colorsSet);

        return {
          imageUrl,
          colors: hexColors,
        };
      }

      const arrayBuffer = await response.arrayBuffer();
      let buffer = Buffer.from(arrayBuffer);

      // Handle transparent PNGs
      if (imageType === 'image/png') {
        buffer = await sharp(buffer)
          .flatten({ background: '#ffffff' })
          .toBuffer();
      }

      // Resize the image to 1x1 pixel to get the average color
      const { data } = await sharp(buffer)
        .resize(1, 1)
        .raw()
        .toBuffer({ resolveWithObject: true });

      // Adjusted code without iteration
      const r = data[0];
      const g = data[1];
      const b = data[2];
      const avgColor = rgbToHex(r, g, b);

      return {
        imageUrl,
        colors: [avgColor],
      };
    } catch (error) {
      console.error(`Error processing image ${imageUrl}:`, error);
      return null;
    }
  });

  const results = await Promise.all(analysisPromises);
  return results.filter(
    (result): result is { imageUrl: string; colors: string[] } =>
      result !== null
  );
}
interface SvgNode {
  attributes?: { [key: string]: string };
  children?: SvgNode[];
}

function extractColors(node: SvgNode, colors: Set<string>) {
  if (node.attributes) {
    for (const attr in node.attributes) {
      const value = node.attributes[attr];
      if (typeof value === 'string') {
        const colorMatches = value.match(/#([0-9a-fA-F]{3,8})\b/g);
        if (colorMatches) {
          colorMatches.forEach((color) => colors.add(color));
        }
      }
    }
  }
  if (node.children) {
    node.children.forEach((child: SvgNode) => extractColors(child, colors));
  }
}
// Function to auto-scroll the page
import { Page } from 'puppeteer';

async function autoScroll(page: Page) {
  await page.evaluate(async () => {
    await new Promise<void>((resolve) => {
      let totalHeight = 0;
      const distance = 100;
      const timer = setInterval(() => {
        const scrollHeight = document.body.scrollHeight;
        window.scrollBy(0, distance);
        totalHeight += distance;
        if (totalHeight >= scrollHeight) {
          clearInterval(timer);
          resolve();
        }
      }, 100);
    });
  });
}
// eslint-disable-next-line @typescript-eslint/no-unused-vars
async function scrapeWebsiteApify(websiteUrl: string) {
  websiteUrl = formatURL(websiteUrl);
  const input = {
    aggressivePrune: false,
    clickElementsCssSelector: '[aria-expanded="false"]',
    clientSideMinChangePercentage: 15,
    debugLog: false,
    debugMode: false,
    dynamicContentWaitSecs: 2,
    expandIframes: true,
    ignoreCanonicalUrl: true,
    keepUrlFragments: false,
    maxCrawlDepth: 1,
    maxCrawlPages: 1,
    proxyConfiguration: {
      useApifyProxy: true,
    },
    readableTextCharThreshold: 100,
    removeCookieWarnings: true,
    removeElementsCssSelector:
      'nav, footer, script, style, noscript, svg,\n[role="alert"],\n[role="banner"],\n[role="dialog"],\n[role="alertdialog"],\n[role="region"][aria-label*="skip" i],\n[aria-modal="true"]',
    renderingTypeDetectionPercentage: 10,
    saveFiles: false,
    saveHtml: false,
    saveHtmlAsFile: false,
    saveMarkdown: true,
    saveScreenshots: false,
    startUrls: [
      {
        url: `${websiteUrl}`,
      },
    ],
    useSitemaps: true,
  };

  const run = await client.actor('aYG0l9s7dbB7j3gbS').call(input);
  const { items } = await client.dataset(run.defaultDatasetId).listItems();

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const captions: string[] = items.map((item: any) => item.text);
  return captions[0];
}

function formatURL(inputURL: string): string {
  let formattedURL = inputURL.trim();

  // If the URL doesn't have a protocol, add https:// by default
  if (!/^https?:\/\//i.test(formattedURL)) {
    formattedURL = 'https://' + formattedURL;
  }

  try {
    const urlObj = new URL(formattedURL);
    let hostname = urlObj.hostname;

    // If hostname doesn't start with 'www.', add it
    if (!hostname.startsWith('www.')) {
      hostname = 'www.' + hostname;
      urlObj.hostname = hostname;
      formattedURL = urlObj.toString();
    } else {
      formattedURL = urlObj.toString();
    }
  } catch {
    // Throw an exception if URL parsing fails
    throw new Error(`Invalid URL: ${inputURL}`);
  }

  return formattedURL;
}
