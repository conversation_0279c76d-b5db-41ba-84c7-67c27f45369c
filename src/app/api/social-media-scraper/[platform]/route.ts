import { NextRequest, NextResponse } from 'next/server';

import { ACTORS, fetchApifyResults } from '../../apify-client';
import {
  XPost,
  ScraperError,
  FacebookPost,
  LinkedInPost,
  InstagramPost,
  SocialMediaPost,
} from '../../types';
import {
  getCookie,
  getMaxPosts,
  validatePageParam,
  createErrorResponse,
  createSuccessResponse,
} from '../../utils';

type PlatformHandler = (
  page: string,
  request: NextRequest
) => Promise<SocialMediaPost[]>;

const platformHandlers: Record<string, PlatformHandler> = {
  facebook: extractFacebookPosts,
  instagram: extractInstagramPosts,
  linkedin: extractLinkedInPosts,
  x: extractXPosts,
};

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ platform: string }> }
) {
  try {
    const { platform } = await params;

    // Validate platform existence
    if (!platform) {
      return NextResponse.json(
        { error: 'Platform parameter is required' },
        { status: 400 }
      );
    }

    const platformName = platform.toLowerCase();
    const page = request.nextUrl.searchParams.get('page');

    if (!platformHandlers[platformName]) {
      return NextResponse.json(
        { error: `Unsupported platform: ${platformName}` },
        { status: 400 }
      );
    }

    const validatedPage = validatePageParam(page);
    const handler = platformHandlers[platformName];
    const posts = await handler(validatedPage, request);

    return createSuccessResponse(posts);
  } catch (error) {
    return createErrorResponse(error);
  }
}

// Platform-specific implementation functions
async function extractFacebookPosts(
  pageName: string
): Promise<SocialMediaPost[]> {
  const maxPosts = getMaxPosts();

  const input = {
    startUrls: [
      {
        url: `https://www.facebook.com/${pageName}/`,
      },
    ],
    resultsLimit: maxPosts,
  };

  const items = await fetchApifyResults<FacebookPost>(ACTORS.FACEBOOK, input);

  return items.map((item) => ({
    text: item.text,
    platform: 'Facebook',
    url: item.url,
    timestamp: item.date,
  }));
}

async function extractInstagramPosts(
  username: string
): Promise<SocialMediaPost[]> {
  const maxPosts = getMaxPosts();

  const input = {
    resultsLimit: maxPosts,
    username: [username],
  };

  const items = await fetchApifyResults<InstagramPost>(ACTORS.INSTAGRAM, input);

  return items.map((item) => ({
    text: item.caption,
    platform: 'Instagram',
    url: item.url,
    timestamp: item.timestamp,
  }));
}

async function extractLinkedInPosts(
  page: string,
  request: NextRequest
): Promise<SocialMediaPost[]> {
  const token = request.nextUrl.searchParams.get('token');

  if (!token) {
    const error: ScraperError = new Error(
      'Token parameter is required for LinkedIn provider'
    ) as ScraperError;
    error.statusCode = 400;
    throw error;
  }

  const cookieData = await getCookie(
    `${process.env.BACKEND_SERVER_URL}utils/linkedin_cookie`,
    token
  );

  const maxPosts = getMaxPosts();

  const input = {
    cookie: JSON.parse(cookieData),
    deepScrape: false,
    endPage: 5,
    'filters.fromCompanies': [`https://linkedin.com/company/${page}`],
    limitPerSource: maxPosts,
    maxDelay: 8,
    minDelay: 2,
    proxy: {
      useApifyProxy: true,
      apifyProxyGroups: ['BUYPROXIES94952'],
    },
    rawData: false,
    urls: [`https://linkedin.com/company/${page}`],
  };

  const items = await fetchApifyResults<LinkedInPost>(ACTORS.LINKEDIN, input);

  return items.map((item) => ({
    text: item.text,
    platform: 'LinkedIn',
    url: item.url,
    timestamp: item.timestamp,
  }));
}

async function extractXPosts(page: string): Promise<SocialMediaPost[]> {
  const maxPosts = getMaxPosts();
  const cleanedHandle = page.replace(/^@/, '');

  const input = {
    maxItems: maxPosts,
    onlyImage: false,
    onlyQuote: false,
    onlyTwitterBlue: false,
    onlyVerifiedUsers: false,
    onlyVideo: false,
    sort: 'Latest',
    start: '2021-07-01',
    startUrls: [`https://twitter.com/${cleanedHandle}`],
  };

  const items = await fetchApifyResults<XPost>(ACTORS.X, input);

  return items.map((item) => ({
    text: item.text,
    platform: 'X',
    url: item.url,
    timestamp: item.created_at,
  }));
}
