import { NextResponse } from 'next/server';

import { ScraperError, SocialMediaPost } from './types';

export const MAX_POSTS_LIMIT = 100;

/**
 * Gets the maximum number of posts to retrieve from configuration
 * with reasonable bounds checking
 */
export function getMaxPosts(): number {
  const configValue = process.env.MAX_SCRAP_POST || '50';
  const parsedValue = parseInt(configValue, 10);

  // Ensure the value is within reasonable bounds
  return Math.min(Math.max(1, parsedValue), MAX_POSTS_LIMIT);
}

/**
 * Validates the page parameter from the request
 * @throws {ScraperError} If page is missing or invalid
 */
export function validatePageParam(page: string | null): string {
  if (!page) {
    const error = new Error('Page parameter is required') as ScraperError;
    error.statusCode = 400;
    throw error;
  }

  // Validate against a reasonably restrictive pattern to prevent injection attacks
  if (!/^[a-zA-Z0-9.\-_]+$/.test(page)) {
    const error = new Error('Invalid page parameter format') as ScraperError;
    error.statusCode = 400;
    throw error;
  }

  return page;
}

/**
 * Creates a standardized error response
 */
export function createErrorResponse(error: unknown): NextResponse {
  console.error('API error:', error);

  if (error instanceof Error) {
    const statusCode = (error as ScraperError).statusCode || 500;
    const message = error.message || 'Internal server error';

    return NextResponse.json({ error: message }, { status: statusCode });
  }

  return NextResponse.json(
    { error: 'An unexpected error occurred' },
    { status: 500 }
  );
}

/**
 * Creates a standardized success response
 */
export function createSuccessResponse(data: SocialMediaPost[]): NextResponse {
  return NextResponse.json(data, { status: 200 });
}

/**
 * Fetches the LinkedIn cookie from the external service
 */
export async function getCookie(
  apiUrl: string,
  authToken: string
): Promise<string> {
  try {
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${authToken}`,
      },
    });

    if (!response.ok) {
      const error = new Error(
        `HTTP error! Status: ${response.status}`
      ) as ScraperError;
      error.statusCode = response.status;
      throw error;
    }

    const jsonResponse = await response.json();
    return jsonResponse.output;
  } catch (error) {
    console.error('Error in getCookie:', error);
    throw error;
  }
}
