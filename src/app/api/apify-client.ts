import { ApifyClient } from 'apify-client';

import { ScraperError } from './types';

export const apifyClient = new ApifyClient({
  token: process.env.APIFY_API_TOKEN,
});

export const ACTORS = {
  FACEBOOK: 'KoJrdxJCTtpon81KY',
  INSTAGRAM: 'nH2AHrwxeTRJoN5hX',
  LINKEDIN: 'kfiWbq3boy3dWKbiL',
  X: '61RPP7dywgiy0JPD0',
};

export async function fetchApifyResults<T>(
  actorId: string,
  input: Record<string, unknown>
): Promise<T[]> {
  try {
    const run = await apifyClient.actor(actorId).call(input);
    const { items } = await apifyClient
      .dataset(run.defaultDatasetId)
      .listItems();
    return items as T[];
  } catch (error) {
    console.error('Apify API error:', error);
    const scraperError = new Error(
      `Apify API error: ${error instanceof Error ? error.message : 'Unknown error'}`
    ) as ScraperError;
    scraperError.statusCode = 500;
    scraperError.details = error;
    throw scraperError;
  }
}
