import { NextResponse } from 'next/server';

export async function GET() {
  const data = [
    {
      month: 'January',
      facebook: Math.floor(Math.random() * 400) + 50,
      instagram: Math.floor(Math.random() * 300) + 30,
      linkedin: Math.floor(Math.random() * 200) + 40,
      twitter: Math.floor(Math.random() * 250) + 75,
    },
    {
      month: 'February',
      facebook: Math.floor(Math.random() * 400) + 50,
      instagram: Math.floor(Math.random() * 300) + 30,
      linkedin: Math.floor(Math.random() * 200) + 40,
      twitter: Math.floor(Math.random() * 250) + 75,
    },
    {
      month: 'March',
      facebook: Math.floor(Math.random() * 400) + 50,
      instagram: Math.floor(Math.random() * 300) + 30,
      linkedin: Math.floor(Math.random() * 200) + 40,
      twitter: Math.floor(Math.random() * 250) + 75,
    },
    {
      month: 'April',
      facebook: Math.floor(Math.random() * 400) + 50,
      instagram: Math.floor(Math.random() * 300) + 30,
      linkedin: Math.floor(Math.random() * 200) + 40,
      twitter: Math.floor(Math.random() * 250) + 75,
    },
    {
      month: 'May',
      facebook: Math.floor(Math.random() * 400) + 50,
      instagram: Math.floor(Math.random() * 300) + 30,
      linkedin: Math.floor(Math.random() * 200) + 40,
      twitter: Math.floor(Math.random() * 250) + 75,
    },
    {
      month: 'June',
      facebook: Math.floor(Math.random() * 400) + 50,
      instagram: Math.floor(Math.random() * 300) + 30,
      linkedin: Math.floor(Math.random() * 200) + 40,
      twitter: Math.floor(Math.random() * 250) + 75,
    },
    {
      month: 'July',
      facebook: Math.floor(Math.random() * 400) + 50,
      instagram: Math.floor(Math.random() * 300) + 30,
      linkedin: Math.floor(Math.random() * 200) + 40,
      twitter: Math.floor(Math.random() * 250) + 75,
    },
    {
      month: 'August',
      facebook: Math.floor(Math.random() * 400) + 50,
      instagram: Math.floor(Math.random() * 300) + 30,
      linkedin: Math.floor(Math.random() * 200) + 40,
      twitter: Math.floor(Math.random() * 250) + 75,
    },
    {
      month: 'September',
      facebook: Math.floor(Math.random() * 400) + 50,
      instagram: Math.floor(Math.random() * 300) + 30,
      linkedin: Math.floor(Math.random() * 200) + 40,
      twitter: Math.floor(Math.random() * 250) + 75,
    },
    {
      month: 'October',
      facebook: Math.floor(Math.random() * 400) + 50,
      instagram: Math.floor(Math.random() * 300) + 30,
      linkedin: Math.floor(Math.random() * 200) + 40,
      twitter: Math.floor(Math.random() * 250) + 75,
    },
    {
      month: 'November',
      facebook: Math.floor(Math.random() * 400) + 50,
      instagram: Math.floor(Math.random() * 300) + 30,
      linkedin: Math.floor(Math.random() * 200) + 40,
      twitter: Math.floor(Math.random() * 250) + 75,
    },
    {
      month: 'December',
      facebook: Math.floor(Math.random() * 400) + 50,
      instagram: Math.floor(Math.random() * 300) + 30,
      linkedin: Math.floor(Math.random() * 200) + 40,
      twitter: Math.floor(Math.random() * 250) + 75,
    },
  ];

  return NextResponse.json(data);
}
