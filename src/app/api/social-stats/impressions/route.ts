import { NextResponse } from 'next/server';

export async function GET() {
  const data = [
    {
      month: 'January',
      facebook: 186,
      instagram: 80,
      linkedin: 60,
      twitter: 120,
    },
    {
      month: 'February',
      facebook: 305,
      instagram: 200,
      linkedin: 110,
      twitter: 220,
    },
    {
      month: 'March',
      facebook: 237,
      instagram: 120,
      linkedin: 150,
      twitter: 170,
    },
    {
      month: 'April',
      facebook: 73,
      instagram: 190,
      linkedin: 90,
      twitter: 130,
    },
    { month: 'May', facebook: 209, instagram: 130, linkedin: 140, twitter: 90 },
    {
      month: 'June',
      facebook: 214,
      instagram: 140,
      linkedin: 130,
      twitter: 100,
    },
    {
      month: 'July',
      facebook: 115,
      instagram: 214,
      linkedin: 160,
      twitter: 210,
    },
    {
      month: 'August',
      facebook: 310,
      instagram: 60,
      linkedin: 80,
      twitter: 140,
    },
    {
      month: 'September',
      facebook: 422,
      instagram: 77,
      linkedin: 120,
      twitter: 230,
    },
    {
      month: 'October',
      facebook: 225,
      instagram: 86,
      linkedin: 90,
      twitter: 160,
    },
    {
      month: 'November',
      facebook: 132,
      instagram: 90,
      linkedin: 130,
      twitter: 125,
    },
    {
      month: 'December',
      facebook: 60,
      instagram: 110,
      linkedin: 100,
      twitter: 95,
    },
  ];

  return NextResponse.json(data);
}
