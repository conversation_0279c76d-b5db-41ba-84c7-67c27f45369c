import { User } from 'next-auth';

type LoginFn = (email: string, password: string) => Promise<User>;

export const login: LoginFn = async (email, password) => {
  const url = process.env.BACKEND_SERVER_URL + 'auth/login';

  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ UserEmail: email, UserPassword: password }),
  });

  const respJson = await response.json();

  if (!response.ok) {
    throw new Error(respJson['error']);
  }

  return respJson;
};
