import { User } from 'next-auth';

type LoginFn = (
  email: string,
  user_name: string,
  profile_id: string,
  platform: string
) => Promise<User>;

export const registerPlatform: LoginFn = async (
  email,
  user_name,
  profile_sub_id,
  platform
) => {
  const url =
    process.env.NEXT_PUBLIC_GOLANG_SERVER_URL + '/api/auth/register/platform';

  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      UserEmail: email,
      ProfileSubId: profile_sub_id,
      UserName: user_name,
      Platform: platform,
    }),
  });

  const respJson = await response.json();

  if (!response.ok) {
    throw new Error(respJson.error || "Erreur d'inscription LinkedIn");
  }

  return respJson;
};
