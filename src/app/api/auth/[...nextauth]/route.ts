import { User, Account, Profile } from 'next-auth';
import { AdapterUser } from 'next-auth/adapters';
import NextAuth from 'next-auth/next';
import AzureAD<PERSON>rovider from 'next-auth/providers/azure-ad';
import CredentialsProvider from 'next-auth/providers/credentials';
import GoogleProvider from 'next-auth/providers/google';
import LinkedInProvider from 'next-auth/providers/linkedin';

import { login } from '../lib/auth';
import { registerPlatform } from '../lib/platform-register';

const handler = NextAuth({
  session: {
    strategy: 'jwt',
  },
  providers: [
    CredentialsProvider({
      name: 'Credentials',
      credentials: {
        email: {
          label: 'Email',
          type: 'text',
        },
        password: { label: 'Password', type: 'password' },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          throw new Error('Email and password are required');
        }

        try {
          const response = await login(credentials.email, credentials.password);

          return {
            id: response.id,
            email: response.email,
            name: response.name,
            companyId: response.companyId,
            brandId: response.brandId,
          };
        } catch (error) {
          throw new Error(
            error instanceof Error ? error.message : 'Invalid credentials'
          );
        }
      },
    }),

    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID as string,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET as string,
      authorization: {
        params: {
          //prompt: "consent",
          prompt: 'select_account',
          access_type: 'offline',
          response_type: 'code',
        },
      },
    }),

    LinkedInProvider({
      clientId: process.env.LINKEDIN_CLIENT_ID as string,
      clientSecret: process.env.LINKEDIN_CLIENT_SECRET as string,
      authorization: {
        params: {
          // Use OAuth 2.0 scopes for LinkedIn
          scope: 'r_basicprofile email r_basicprofile',
        },
      },
    }),

    AzureADProvider({
      id: 'microsoft',
      clientId: process.env.MICROSOFT_CLIENT_ID as string,
      clientSecret: process.env.MICROSOFT_CLIENT_SECRET as string,
      tenantId: process.env.MICROSOFT_TENANT_ID as string,
      authorization: {
        params: {
          scope: 'openid profile email',
        },
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user, trigger, session }) {
      if (user) {
        token.id = user.id;
      }

      if (trigger === 'update') {
        return { ...token, ...session.user };
      }
      return { ...token, ...user };
    },

    async session({ session }) {
      return {
        ...session,
        user: {
          ...session.user,
          id: session.user.id,
        },
      };
    },
    async signIn({
      user,
      account,
      profile,
    }: {
      user: User | AdapterUser;
      account: Account | null;
      profile?: Profile;
      email?: { verificationRequest?: boolean };
      credentials?: Record<string, unknown>;
    }): Promise<string | boolean> {
      if (account?.provider === 'google') {
        const { email } = user;
        try {
          const fetch_user = await registerPlatform(
            String(email),
            String(profile?.name),
            String(profile?.sub),
            'Google'
          );

          Object.assign(user, fetch_user);
          return true;
        } catch (e) {
          console.error(e);
          return false;
        }
      }
      if (account?.provider === 'linkedin') {
        try {
          const linkedinProfile = profile as Record<string, unknown>;
          const fetch_user = await registerPlatform(
            `${String(linkedinProfile['localizedFirstName'])}.${String(linkedinProfile['localizedLastName'])}@linkedin`,
            `${String(linkedinProfile['localizedFirstName'])} ${String(linkedinProfile['localizedLastName'])}`,
            String(linkedinProfile['id']),
            'Linkedin'
          );
          Object.assign(user, fetch_user);
          return true;
        } catch (e) {
          console.error(e);
          return false;
        }
      }
      if (account?.provider === 'microsoft') {
        try {
          const microsoftProfile = profile as Record<string, unknown>;
          if (!microsoftProfile['email'] || !microsoftProfile['sub']) {
            console.error('Profil Microsoft incomplet :', microsoftProfile);
            return false;
          }
          const fetch_user = await registerPlatform(
            String(microsoftProfile['email']),
            String(microsoftProfile['name']),
            String(microsoftProfile['sub']),
            'Microsoft'
          );
          Object.assign(user, fetch_user);
          return true;
        } catch (e) {
          console.error("Erreur lors de l'inscription Microsoft :", e);
          return false;
        }
      }

      return true;
    },
  },
  pages: {
    signIn: '/auth/login',
    error: '/auth/error',
    verifyRequest: '/auth/verify-request',
    newUser: '/auth/welcome',
  },
  secret: process.env.JWT_SECRET,
});

export { handler as GET, handler as POST };
