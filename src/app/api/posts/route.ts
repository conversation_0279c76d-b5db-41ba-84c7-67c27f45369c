import { NextResponse } from 'next/server';

export async function GET() {
  const posts = [
    {
      id: 1,
      text: 'House in the woods',
      description:
        'A serene and tranquil retreat, this house in the woods offers a peaceful escape from the hustle and bustle of city life.',

      thumbnail:
        'https://images.unsplash.com/photo-1476231682828-37e571bc172f?q=80&w=3474&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      createdAt: 1740250832907,
    },
    {
      id: 2,
      text: 'House above the clouds',
      description:
        'Perched high above the world, this house offers breathtaking views and a unique living experience. It&apos;s a place where the sky meets home, and tranquility is a way of life.',

      thumbnail:
        'https://images.unsplash.com/photo-1464457312035-3d7d0e0c058e?q=80&w=3540&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      createdAt: 1740248792907,
    },
    {
      id: 3,
      text: 'House above the clouds',
      description:
        'Perched high above the world, this house offers breathtaking views and a unique living experience. It&apos;s a place where the sky meets home, and tranquility is a way of life.',

      thumbnail:
        'https://images.unsplash.com/photo-1464457312035-3d7d0e0c058e?q=80&w=3540&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      createdAt: 1740219992907,
    },
    {
      id: 4,
      text: 'Greens all over',
      description:
        'A house surrounded by greenery and nature&apos;s beauty. It&apos;s the perfect place to relax, unwind, and enjoy life.',

      thumbnail:
        'https://images.unsplash.com/photo-1588880331179-bc9b93a8cb5e?q=80&w=3540&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      createdAt: 1740122792907,
    },
    {
      id: 5,
      text: 'Rivers are serene',
      description:
        'A house by the river is a place of peace and tranquility. It&apos;s the perfect place to relax, unwind, and enjoy life.',

      thumbnail:
        'https://images.unsplash.com/photo-1475070929565-c985b496cb9f?q=80&w=3540&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      createdAt: 1739615192907,
    },
    {
      id: 6,
      text: 'Rivers are serene',
      description:
        'A house by the river is a place of peace and tranquility. It&apos;s the perfect place to relax, unwind, and enjoy life.',

      thumbnail:
        'https://images.unsplash.com/photo-1475070929565-c985b496cb9f?q=80&w=3540&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      createdAt: 1736936792907,
    },
    {
      id: 7,
      text: 'Modern Mountain Home',
      description:
        "Nestled amidst a stunning mountain range, this contemporary home offers breathtaking views and a luxurious living experience. It's a haven for those seeking tranquility and connection with nature.",
      thumbnail:
        'https://images.unsplash.com/photo-1571896349842-33c89424de2d?q=80&w=3540&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      createdAt: 1731659712907,
    },
    {
      id: 8,
      text: 'Seaside Cottage',
      description:
        "This charming cottage by the sea offers a peaceful retreat with stunning ocean views. It's the perfect place to relax, unwind, and enjoy the sound of the waves.",
      thumbnail:
        'https://images.unsplash.com/photo-1564501049412-61c2a3083791?q=80&w=3546&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      createdAt: 1718887522907,
    },
    {
      id: 9,
      text: 'Desert Oasis',
      description:
        "This unique home in the desert offers a tranquil escape from the hustle and bustle of city life. It's a place where you can connect with nature and find peace.",
      thumbnail:
        'https://images.unsplash.com/photo-1505693372133-e67b28b45d7f?q=80&w=3474&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      createdAt: 1693930232907,
    },
    {
      id: 10,
      text: 'Urban Penthouse',
      description:
        "This luxurious penthouse in the heart of the city offers stunning views and a sophisticated living experience. It's the perfect place for those who enjoy the energy and excitement of urban life.",
      thumbnail:
        'https://images.unsplash.com/photo-1527030280862-64139fba04ca?q=80&w=3540&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      createdAt: 1681296942907,
    },
    {
      id: 11,
      text: 'Tropical Treehouse',
      description:
        "Escape to paradise in this enchanting treehouse nestled amidst a lush tropical rainforest. It's a place where you can reconnect with nature and experience the beauty of the jungle.",
      thumbnail:
        'https://images.unsplash.com/photo-1520250497591-112f2f40a3f4?q=80&w=3540&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      createdAt: 1710771652907,
    },
    {
      id: 12,
      text: 'Historic Brownstone',
      description:
        "This beautifully preserved brownstone in a historic neighborhood offers a blend of classic charm and modern amenities. It's the perfect place for those who appreciate history and architecture.",
      thumbnail:
        'https://images.unsplash.com/photo-1501785888041-af3ef285b470?q=80&w=3540&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      createdAt: 1703788502907,
    },
    {
      id: 13,
      text: 'Cozy Winter Cabin',
      description:
        "This charming cabin nestled in the snowy woods offers a warm and inviting retreat for winter getaways. It's the perfect place to curl up by the fire and enjoy the beauty of the season.",
      thumbnail:
        'https://images.unsplash.com/photo-1487530811176-3780de880c93?q=80&w=3540&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      createdAt: 1736079642907,
    },
    {
      id: 14,
      text: 'Modern Ski Chalet',
      description:
        "This stylish chalet offers a luxurious retreat for ski enthusiasts with breathtaking mountain views and modern amenities. It's the perfect place to relax after a day on the slopes.",
      thumbnail:
        'https://images.unsplash.com/photo-1517973710199-79af48b0f6a6?q=80&w=3540&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      createdAt: 1736079642907,
    },
    {
      id: 15,
      text: 'Historic City Apartment',
      description:
        "This elegant apartment in a historic building offers a blend of classic charm and modern comfort. It's the perfect place for those who appreciate history and culture.",
      thumbnail:
        'https://images.unsplash.com/photo-1583608205776-bfd35f0d9f83?q=80&w=3540&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      createdAt: 1737822952907,
    },
    {
      id: 16,
      text: 'Tropical Beach House',
      description:
        "This stunning beach house offers a luxurious escape with direct access to the beach and breathtaking ocean views. It's the perfect place to relax, unwind, and enjoy the beauty of the tropics.",
      thumbnail:
        'https://images.unsplash.com/photo-1564501049412-61c2a3083791?q=80&w=3540&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      createdAt: 1736679002907,
    },
    {
      id: 17,
      text: 'Tropical Beach Houses',
      description:
        "This stunning beach house offers a luxurious escape with direct access to the beach and breathtaking ocean views. It's the perfect place to relax, unwind, and enjoy the beauty of the tropics.",
      thumbnail:
        'https://images.unsplash.com/photo-1564501049412-61c2a3083791?q=80&w=3540&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      createdAt: 1740427202907,
    },
    {
      id: 18,
      text: 'Tropical Beach Houses',
      description:
        "This stunning beach house offers a luxurious escape with direct access to the beach and breathtaking ocean views. It's the perfect place to relax, unwind, and enjoy the beauty of the tropics.",
      thumbnail:
        'https://images.unsplash.com/photo-1564501049412-61c2a3083791?q=80&w=3540&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      createdAt: Date.now() + 60 * 60 * 1000,
    },
    {
      id: 19,
      text: 'please work',
      description:
        "This stunning beach house offers a luxurious escape with direct access to the beach and breathtaking ocean views. It's the perfect place to relax, unwind, and enjoy the beauty of the tropics.",
      thumbnail:
        'https://images.unsplash.com/photo-1564501049412-61c2a3083791?q=80&w=3540&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      createdAt: 1740519033000,
    },
    {
      id: 20,
      text: 'Historic City Apartment',
      description:
        "This elegant apartment in a historic building offers a blend of classic charm and modern comfort. It's the perfect place for those who appreciate history and culture.",
      thumbnail:
        'https://images.unsplash.com/photo-1583608205776-bfd35f0d9f83?q=80&w=3540&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      createdAt: 1740519033000,
    },
  ];

  return NextResponse.json(posts);
}
