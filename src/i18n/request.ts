import { getRequestConfig } from 'next-intl/server';

import { Locale, routing } from './routing';

export default getRequestConfig(async ({ requestLocale }) => {
  let locale = await requestLocale;

  if (!locale || !routing.locales.includes(locale as Locale)) {
    locale = routing.defaultLocale;
  }

  const messages = {
    ...(await import(`./locales/${locale}/knowledge.json`)).default,
    ...(await import(`./locales/${locale}/private-model.json`)).default,
    ...(await import(`./locales/${locale}/translation.json`)).default,
    ...(await import(`./locales/${locale}/common.json`)).default,
    ...(await import(`./locales/${locale}/auth.json`)).default,
    ...(await import(`./locales/${locale}/routes.json`)).default,
    ...(await import(`./locales/${locale}/api-errors.json`)).default,
    ...(await import(`./locales/${locale}/creative.json`)).default,
    ...(await import(`./locales/${locale}/posts.json`)).default,
  };

  return {
    locale,
    messages: messages,
  };
});
