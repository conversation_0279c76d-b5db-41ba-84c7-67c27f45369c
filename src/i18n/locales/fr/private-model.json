{"name": "Nom", "description": "Description", "creation_Date": "Date de création", "type": "Type", "created_by": "C<PERSON><PERSON> par", "actions": "Actions", "title_private_model_list": "Liste des modèles privés", "subtitle_private_model_list": "Un modèle privé capture le style et les caractéristiques des entrées comme référence pour les publications générées par l'IA", "create_a_private_model": "<PERSON><PERSON><PERSON> un modèle privé", "search": "<PERSON><PERSON><PERSON>", "filter": "<PERSON><PERSON><PERSON>", "model_type": "Type de modèle", "text": "Texte", "image": "Image", "url": "URL", "clear_all": "Tout effacer", "apply": "Appliquer", "sort": "<PERSON><PERSON>", "clear_sorting": "<PERSON>ff<PERSON><PERSON> le tri", "select_a_date": "Sélectionner une date", "clear": "<PERSON><PERSON><PERSON><PERSON>", "show": "<PERSON><PERSON><PERSON><PERSON>", "previous": "Précédent", "next": "Suivant", "cancel": "Annuler", "private_model_form": {"model_name": "Nom du modèle", "model_name_placeholder": "Contenu", "description": "Description", "description_placeholder": "Contenu", "topics": "Sujets", "add": "Ajouter", "social_media": "Réseaux sociaux", "add_social_media": "Ajouter un réseau social", "select_platform": "Sélectionner une plateforme", "page_name": "Nom de la page", "image_category": "Catégorie d'image", "image_category_placeholder": "Sé<PERSON><PERSON>ner une catégorie d'image", "keyword": "Mot-clé", "object_name": "Nom de l'objet", "image_urls": "URLs d'image", "url": "URL", "import_image": "Importer une image", "drag_drop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ou", "link": "choisir des fichiers", "upload": "pour télécharger", "delete": "<PERSON><PERSON><PERSON><PERSON>", "new_model": "Nouveau modèle", "info_general": "Informations générales", "preview": "<PERSON><PERSON><PERSON><PERSON>", "text_model": "<PERSON><PERSON><PERSON><PERSON> de texte", "image_model": "Mo<PERSON><PERSON><PERSON> d'image", "save": "Enregistrer", "cancel": "Annuler"}, "preview_form": {"close": "<PERSON><PERSON><PERSON>"}, "text-schema": {"topic-error": "Veuillez entrer au moins un sujet", "model-name-error": "Veuillez entrer un nom de modèle", "model-description-error": "<PERSON><PERSON><PERSON><PERSON> entrer une description du modèle", "platform-error": "La plateforme doit être 'facebook', 'instagram', 'linkedin' ou 'x'", "pageName-error": "Le nom de la page est requis"}, "toast": {"deleting_model": "Suppression du modèle...", "model_deleted": "<PERSON><PERSON><PERSON><PERSON> supprimé", "deletion_failed": "Échec de la suppression", "model_marked_as_favorite": "<PERSON><PERSON><PERSON><PERSON> marqué comme favori", "model_marked_as_not_favorite": "<PERSON><PERSON><PERSON><PERSON> marqué comme non favori", "error_deleting_model": "<PERSON><PERSON><PERSON> lors de la suppression du modèle", "model_created_successfully": "Votre modèle a été créé avec succès", "model_prapared_successfully": "Votre modèle a été préparé avec succès", "error_preparing_model": "Erreur lors de la préparation du modèle"}, "details": {"error_loading_model": "Erreur lors du chargement du modèle", "private_model_details": "<PERSON>é<PERSON> du modèle privé", "creation_date": "Date de création :", "created_by": "Créé par :", "model_status": "Statut du modèle :", "tags": "Étiquettes :", "no_tags": "Aucune éti<PERSON>", "images": "Images", "texts": "Description"}}