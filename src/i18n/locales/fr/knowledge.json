{"no_data_available": "<PERSON><PERSON><PERSON> donnée disponible", "type_knowledge": {"content": "Contenu", "media": "Média", "audience": "Audience"}, "content": {"title": "Liste des connaissances", "new_content": "Nouveau contenu", "new_media": "Nouveau média", "new_audience": "Nouvelle audience", "subtitle": "Une connaissance est un ensemble structuré d'informations prédéfinies qui guide l'IA dans la génération de publications pertinentes, ciblées et visuellement alignées."}, "content_form": {"title": "Nouveau contenu", "type": "Type", "text": "<PERSON><PERSON><PERSON><PERSON> de texte", "url": "Depuis URL", "file": "<PERSON><PERSON><PERSON>", "external_drive": "Depuis un lecteur externe", "name": "Nom", "title_field": "Titre", "content": "Contenu", "tags": "Mots-clés", "website_name": "Nom du site web", "website_title": "Titre du site web", "website_url": "URL du site web", "data_scraped": "Les données ont été récupérées avec succès", "preview_data": "Aperçu des données", "file_name": "Nom du fichier", "google_drive": "Depuis Google Drive", "sharepoint": "Depuis SharePoint", "aitable": "<PERSON><PERSON><PERSON>", "connect": "Connecter", "accounts": "<PERSON><PERSON><PERSON>", "accounts_placeholder": "Sélectionner un compte", "no_options": "Aucune option disponible", "select_file": "Sélectionner un fichier", "search_file": "Rechercher un fichier ...", "select_site": "Sélectionner un site", "site_placeholder": "www.site.com", "select_drive": "Sé<PERSON>ionner un lecteur", "select_folder": "Sélectionner un dossier", "select_base": "Sélectionner une base", "select_table": "Sélectionner une table"}, "media_form": {"title": "Nouveau contenu média", "type": "Type", "logo": "Ajouter un logo", "logo_name": "Nom du logo", "color": "A<PERSON>ter une couleur", "color_name": "Nom de la couleur", "select_color": "<PERSON><PERSON><PERSON><PERSON>ner une couleur", "name": "Nom"}, "audience_form": {"title": "Nouveau segment d'audience", "name": "Nom de l'audience", "age_range": "Tranche d'âge", "gender": "<PERSON>e", "gender_options": {"male": "<PERSON><PERSON>", "female": "<PERSON>mme", "neutral": "Neutre"}, "spending_behavior": "Comportement de dépense", "spending_behavior_options": {"budget_conscious": "Soucieux du budget", "value_oriented": "<PERSON><PERSON> valeur", "aspirational": "Aspirationnel", "luxury": "Luxe", "experiential": "Expérientiel", "impulsive": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "select_all": "<PERSON><PERSON>", "select_options": "Sélectionner les options"}, "business_industry": "Secteur d'activité", "interests_or_preferences": "Intérêts ou préférences"}, "schema": {"url_required": "L'URL du site web est requise", "name_required": "Le nom est requis", "text_title_required": "Le titre du texte est requis", "text_content_required": "Le contenu du texte est requis", "website_title_required": "Le titre du site web est requis", "website_url_required": "L'URL du site web est requise", "document_title_required": "Le titre du document est requis", "file_required": "Le fichier est requis", "logo_name_required": "Le nom du logo est requis", "minimum_color_length": "Minimum 1 couleur requise", "maximum_color_length": "Maximum 6 couleurs autorisées", "audience_name_required": "Le nom de l'audience est requis", "min_value_age_required": "Minimum 2 valeurs requises", "max_value_age_required": "Maximum 2 valeurs autorisées", "min_age": "L'âge minimum est de 18 ans", "max_age": "L'âge maximum est de 60 ans", "id_required": "L'ID est requis", "file_optional": "Le fichier est facultatif", "adresse_email_invalid": "Adresse e-mail invalide"}, "toast_knowledge": {"knowledge_deleted": "Connaissance supprimée avec succès", "website_scraped": "Site web récupéré avec succès", "website_not_scraped": "Site web non récupéré", "text_created": "Contenu texte créé avec succès", "text_not_created": "Contenu texte non créé", "website_created": "Contenu du site web créé avec succès", "website_not_created": "Contenu du site web non créé", "file_created": "Contenu du fichier créé avec succès", "file_not_created": "Contenu du fichier non créé", "logo_created": "Logo créé avec succès", "logo_not_created": "Logo non créé", "color_created": "<PERSON>uleur créée avec succès", "color_not_created": "Couleur non créée", "audience_created": "Segment d'audience créé avec succès", "audience_not_created": "Segment d'audience non créé", "text_updated": "Contenu texte mis à jour avec succès", "text_not_updated": "Contenu texte non mis à jour", "website_updated": "Contenu du site web mis à jour avec succès", "website_not_updated": "Contenu du site web non mis à jour", "file_updated": "Contenu du fichier mis à jour avec succès", "file_not_updated": "Contenu du fichier non mis à jour", "logo_updated": "Logo mis à jour avec succès", "logo_not_updated": "Logo non mis à jour", "color_updated": "<PERSON><PERSON>ur mise à jour avec succès", "color_not_updated": "Couleur non mise à jour", "audience_updated": "Segment d'audience mis à jour avec succès", "audience_not_updated": "Segment d'audience non mis à jour", "google_drive_revoked": "Compte Google révoqué", "google_drive_not_revoked": "Compte Google non révoqué", "sharepoint_revoked": "Compte SharePoint révoqué", "sharepoint_not_revoked": "Compte SharePoint non révoqué", "aitable_revoked": "Co<PERSON><PERSON>é<PERSON>", "aitable_not_revoked": "Compte Aitable non révoqué", "wait_scraping_data": "V<PERSON> devez attendre les données récupérées"}, "error_details": "Impossible d'obtenir la connaissance, une erreur s'est produite !", "error_title_details": "Problème lors de l'obtention du titre", "error_details_content": "Problème lors de l'obtention du contenu", "knowledge_details": "Détails de la connaissance", "detail_title": {"text": "Connaissance textuelle", "file": "Connaissance de fichier", "website": "Connaissance de site web", "audience": "Connaissance d'audience", "logo": "Connaissance de logo", "color": "Connaissance de couleur"}, "description": "Description", "tags": "Mots-clés", "no_labels": "Aucune étiquette disponible", "url": "URL", "created_by": "C<PERSON><PERSON> par", "text": "Texte", "file": "<PERSON><PERSON><PERSON>", "website": "Site web", "audience": "Audience", "logo": "Logo", "color": "<PERSON><PERSON><PERSON>"}