{"private_model_form": {"model_name": "<PERSON><PERSON><PERSON>", "model_name_placeholder": "Inhalt", "description": "Beschreibung", "description_placeholder": "Inhalt", "topics": "Themen", "add": "Hinzufügen", "social_media": "Soziale Medien", "add_social_media": "Soziales Netzwerk hinzufügen", "select_platform": "Plattform auswählen", "page_name": "<PERSON><PERSON><PERSON><PERSON>", "image_category": "Bildkategorie", "image_category_placeholder": "Bildkategorie auswählen", "keyword": "Schlüsselwort", "object_name": "Objektname", "image_urls": "Bild-URLs", "url": "URL", "import_image": "Bild importieren", "drag_drop": "<PERSON><PERSON><PERSON> und ablegen oder", "link": "<PERSON><PERSON> au<PERSON>wählen", "upload": "zum Hochladen", "delete": "Löschen", "new_model": "Neues Modell", "info_general": "Allgemeine Informationen", "preview": "Vorschau", "text_model": "Textmodell", "image_model": "Bildmodell", "save": "Speichern", "cancel": "Abbrechen"}, "preview_form": {"close": "Schließen"}, "text-schema": {"topic-error": "<PERSON>te geben Si<PERSON> mindestens ein Thema ein", "model-name-error": "<PERSON>te geben Si<PERSON> einen Modellnamen ein", "model-description-error": "Bitte geben Si<PERSON> eine Modellbeschreibung ein", "platform-error": "Die Plattform muss 'facebook', 'instagram', 'linkedin' oder 'x' sein", "pageName-error": "Seitenname ist erforderlich"}, "toast": {"deleting_model": "Modell wird gelöscht...", "model_deleted": "<PERSON><PERSON>", "deletion_failed": "Löschen fehlgeschlagen", "model_marked_as_favorite": "Modell als Favorit mark<PERSON>t", "model_marked_as_not_favorite": "<PERSON>l nicht mehr als Favorit markiert", "error_deleting_model": "Fehler beim Löschen des Modells", "model_created_successfully": "I<PERSON> Modell wurde erfolgreich erstellt", "model_prapared_successfully": "Ihr Modell wurde erfolgreich vorbereitet", "error_preparing_model": "Fehler beim Vorbereiten des Modells"}, "details": {"error_loading_model": "Fehler beim Laden des Modells", "private_model_details": "Details zum privaten Modell", "creation_date": "Erstellungsdatum:", "created_by": "<PERSON><PERSON><PERSON><PERSON> <PERSON>:", "model_status": "Modellstatus:", "tags": "Tags:", "no_tags": "Keine <PERSON>s", "images": "Bilder", "texts": "Beschreibung"}}