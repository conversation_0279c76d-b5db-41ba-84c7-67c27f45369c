{"no_data_available": "<PERSON><PERSON> ve<PERSON>ü<PERSON>", "type_knowledge": {"content": "Inhalt", "media": "Medien", "audience": "Zielgruppe"}, "content": {"title": "Wissensliste", "new_content": "Neuer Inhalt", "new_media": "Neue Medien", "new_audience": "Neue Zielgruppe", "subtitle": "Wissen ist ein strukturierter Satz vordefinierter Informationen, der die KI bei der Generierung relevanter, zielgerichteter und visuell abgestimmter Beiträge leitet."}, "content_form": {"title": "Neuer Inhalt", "type": "<PERSON><PERSON>", "text": "Textmodell", "url": "Von URL", "file": "<PERSON>", "external_drive": "Von externem Laufwerk", "name": "Name", "title_field": "Titel", "content": "Inhalt", "tags": "Tags", "website_name": "Website-Name", "website_title": "Website-Titel", "website_url": "Website-URL", "data_scraped": "Die Daten wurden erfolgreich gescrapt", "preview_data": "Datenvorschau", "file_name": "Dateiname", "google_drive": "Von <PERSON> Drive", "sharepoint": "Von SharePoint", "aitable": "<PERSON>", "connect": "Verbinden", "accounts": "Konten", "accounts_placeholder": "<PERSON><PERSON> auswählen", "no_options": "Keine Optionen verfügbar", "select_file": "<PERSON>i ausw<PERSON>hlen", "search_file": "Datei suchen ...", "select_site": "Website auswählen", "site_placeholder": "www.site.com", "select_drive": "Laufwerk auswählen", "select_folder": "Ordner auswählen", "select_base": "<PERSON><PERSON> aus<PERSON>en", "select_table": "<PERSON><PERSON><PERSON> auswählen"}, "media_form": {"title": "Neuer Medieninhalt", "type": "<PERSON><PERSON>", "logo": "<PERSON><PERSON>", "logo_name": "Logo-Name", "color": "Farbe hinzufügen", "color_name": "<PERSON>b<PERSON>", "select_color": "Farbe auswählen", "name": "Name"}, "audience_form": {"title": "Neues Zielgruppensegment", "name": "Zielgruppenname", "age_range": "Altersbereich", "gender": "Geschlecht", "gender_options": {"male": "<PERSON><PERSON><PERSON><PERSON>", "female": "<PERSON><PERSON><PERSON>", "neutral": "Neutral"}, "spending_behavior": "Ausgabeverhalten", "spending_behavior_options": {"budget_conscious": "Budgetbewusst", "value_oriented": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aspirational": "Ambit<PERSON><PERSON><PERSON>", "luxury": "Luxus", "experiential": "<PERSON><PERSON><PERSON><PERSON><PERSON>entier<PERSON>", "impulsive": "Impulsiv", "select_all": "Alle auswählen", "select_options": "Optionen auswählen"}, "business_industry": "Branche", "interests_or_preferences": "Interessen oder Präferenzen"}, "schema": {"url_required": "Website-URL ist erforderlich", "name_required": "Name ist erforderlich", "text_title_required": "Texttitel ist erforderlich", "text_content_required": "Textinhalt ist erforderlich", "website_title_required": "Website-Titel ist erforderlich", "website_url_required": "Website-URL ist erforderlich", "document_title_required": "Dokumenttitel ist erforderlich", "file_required": "Datei ist erforderlich", "logo_name_required": "Logo-Name ist erforderlich", "minimum_color_length": "Mindestens 1 Farbe erforderlich", "maximum_color_length": "Maximal 6 Farben erlaubt", "audience_name_required": "Zielgruppenname ist erforderlich", "min_value_age_required": "Mindestens 2 Werte erforderlich", "max_value_age_required": "Maximal 2 Werte erlaubt", "min_age": "Mindestalter ist 18", "max_age": "Höchstalter ist 60", "id_required": "ID ist erforderlich", "file_optional": "Datei ist optional", "adresse_email_invalid": "Ungültige E-Mail-Adresse"}, "toast_knowledge": {"knowledge_deleted": "Wissen erfolgreich gel<PERSON>t", "website_scraped": "Website erfolgreich gescrapt", "website_not_scraped": "Website nicht gescrapt", "text_created": "Textinhalt erfolgreich erstellt", "text_not_created": "Textinhalt nicht erstellt", "website_created": "Website-Inhalt erfolgreich erstellt", "website_not_created": "Website-In<PERSON> nicht erstellt", "file_created": "Dateiinhalt erfolgreich erstellt", "file_not_created": "Dateiinhalt nicht erstellt", "logo_created": "Logo erfolgreich erstellt", "logo_not_created": "Logo nicht erstellt", "color_created": "Farbe erfolgreich erstellt", "color_not_created": "Farbe nicht erstellt", "audience_created": "Zielgruppensegment erfolgreich erstellt", "audience_not_created": "Zielgruppensegment nicht erstellt", "text_updated": "Textinhalt erfolgreich aktualisiert", "text_not_updated": "Textinhalt nicht aktualisiert", "website_updated": "Website-Inhalt erfolgreich aktualisiert", "website_not_updated": "Website-Inhalt nicht aktualisiert", "file_updated": "Dateiinhalt erfolgreich aktualisiert", "file_not_updated": "Dateiinhalt nicht aktualisiert", "logo_updated": "Logo erfolgreich aktualisiert", "logo_not_updated": "Logo nicht aktualisiert", "color_updated": "Farbe erfolgreich aktualisiert", "color_not_updated": "Farbe nicht aktualisiert", "audience_updated": "Zielgruppensegment erfolgreich aktualisiert", "audience_not_updated": "Zielgruppensegment nicht aktualisiert", "google_drive_revoked": "Google-<PERSON><PERSON>", "google_drive_not_revoked": "Google-Konto nicht widerrufen", "sharepoint_revoked": "SharePoint-Konto widerrufen", "sharepoint_not_revoked": "SharePoint-Konto nicht widerrufen", "aitable_revoked": "Aitable-<PERSON><PERSON>", "aitable_not_revoked": "Aitable-Konto nicht widerrufen", "wait_scraping_data": "Sie sollten auf die gescrapten Daten warten"}, "error_details": "Wissen kann nicht abgerufen werden, etwas ist schief gelaufen!", "error_title_details": "Problem beim Abrufen des Titels", "error_details_content": "Problem beim Abrufen des Inhalts", "knowledge_details": "Wissensdetails", "detail_title": {"text": "Textwissen", "file": "<PERSON><PERSON><PERSON><PERSON>", "website": "Website-Wissen", "audience": "Zielgruppenwissen", "logo": "Logo-Wissen", "color": "<PERSON><PERSON><PERSON><PERSON>"}, "description": "Beschreibung", "tags": "Tags", "no_labels": "Keine Beschriftungen verfügbar", "url": "URL", "created_by": "<PERSON><PERSON><PERSON><PERSON> von", "text": "Text", "file": "<PERSON><PERSON>", "website": "Webseite", "audience": "Zielgruppe", "logo": "Logo", "color": "Farbe"}