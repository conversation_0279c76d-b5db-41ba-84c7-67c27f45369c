{"creative_card": {"title": "Erstellung einer neuen Kreativvorlage", "tabs": {"visual": {"title": "<PERSON><PERSON>uell", "children": {"image": {"title": "<PERSON><PERSON><PERSON> gene<PERSON>"}, "video": {"title": "Video generieren"}}}, "text": {"title": "Text"}}, "form": {"general_infos": {"title": "Allgemein", "description": "Verfassen Sie eine kurze und präzise Eingabeaufforderung, um die Erstellung Ihrer Inhalte zu steuern", "social_media": "Soziale Medien", "fields": {"title": {"label": "Titel", "placeholder": "Inhalt"}, "description": {"label": "Beschreibung", "placeholder": "Inhalt"}}}, "image_generation": {"title": "Inhalt", "description": "Geben Sie die erforderlichen Details ein, um ein bestimmtes Bild zu generieren", "question": "<PERSON><PERSON>chten Sie die Datei in den Beitrag einfügen?", "fields": {"image_content": {"label": "Bildinhalt"}, "image_sizes": {"custom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "square": "<PERSON>uadratisch", "landscape": "Querformat", "cinemascope": "Kinoformat", "portrait": "Hochformat", "vertical": "Vertikales Kinoformat"}, "width": {"label": "Breite"}, "height": {"label": "<PERSON><PERSON><PERSON>"}, "image_type": {"label": "<PERSON><PERSON>", "placeholder": "Bildtyp auswählen", "button": "Wechseln"}, "image_style": {"label": "Stil", "placeholder": "Bildstil auswählen", "button": "Wechseln"}, "image_effects": {"label": "Effekte", "placeholder": "Bildeffekte auswählen", "button": {"add": "Hinzufügen", "change": "Ändern"}}}}, "cta_content": {"title": "Button Call to Action", "description": "Definieren Sie die Benutzeraktion mit einem Link zur relevanten Seite", "select": {"label": "Call to Action", "placeholder": "Inhalt", "cta_texts": {"save_50_buy_now": "sparen-50-jetzt-kaufen", "get_started": "<PERSON><PERSON><PERSON>", "join_newsletter": "newsletter-abonnieren", "discover_more": "mehr-ent<PERSON><PERSON>n", "try_it_now": "jetzt-ausprobieren", "schedule_free_demo": "kostenlose-demo-vereinbaren", "instant_download": "sofortiger-download", "secure_cart": "sicherer-warenko<PERSON>", "subscribe_savings": "abonnieren-sparen", "follow_for_updates": "folge-für-updates", "unlock_potential": "potenzial-freisetzen", "claim_free_trial": "kostenlose-testversion-anfordern", "watch_demo": "demo-an<PERSON><PERSON>", "contact_us": "kontaktieren-sie-uns"}}, "field": {"label": "Link", "placeholder": "Inhalt"}}, "marketing_infos": {"title": "Marketing", "description": "Definieren Sie die Benutzeraktion mit einem Link zur relevanten Seite", "select": {"placeholder": "Inhalt", "marketing_strategy": {"label": "Marketingstrategie", "marketing_strategies_options": {"aida": "AIDA", "before_and_after": "<PERSON><PERSON><PERSON> und Nachher", "consistency_voice_tone": "Konsistenz in Stimme und Ton", "educational_content": "Bildungsinhalte", "emotive_appeals": "Emotionale Appelle", "exclusivity_scarcity": "Exklusivität und Knappheit", "fomo": "FOMO (<PERSON><PERSON>, et<PERSON> zu verpassen)", "interactive_posts": "Interaktive Beiträge", "personalization": "Personalisierung", "problem_solution": "Problem und Lösung", "product_descriptions": "Produktbeschreibungen", "promotion": "Werbeaktion", "retargeting": "Retargeting", "storytelling": "Storytelling", "testimonial": "Testimonial", "trending_topics": "Trendthemen", "user_centric_messaging": "Nutzerzentrierte Nachrichten", "value_proposition": "Wertversprechen", "visual_storytelling": "<PERSON><PERSON><PERSON><PERSON>"}}, "private_model": {"label": "Privates Modell"}, "knowledge": {"label": "Wissen"}}}, "advanced_options": {"title": "Erweiterte Optionen", "description": "Tools zur effektiven Personalisierung und Verbesserung des Inhalts Ihres Beitrags", "select": {"placeholder": "Inhalt", "language": {"label": "<PERSON><PERSON><PERSON>", "options": {"english": "<PERSON><PERSON><PERSON>", "french": "Franzö<PERSON><PERSON>", "arabic": "Arabisch", "italian": "Italienisch", "spanish": "Spanisch", "korean": "Koreanisch"}}, "tone_of_voice": {"label": "Tonfall", "options": {"formal": "<PERSON>ell", "informal": "Informell", "humorous": "Humorvoll", "serious": "<PERSON>", "optimistic": "Optimistisch", "motivating": "Motivierend", "respectful": "Respektvoll", "assertive": "Bestimmt", "conversational": "Konversationell", "persuasive": "Überzeugend", "inspirational": "Inspirierend", "educational": "<PERSON><PERSON><PERSON><PERSON>", "emotional": "Emotional", "dramatic": "Dramatisch"}}}, "checkbox": {"emoji": "<PERSON><PERSON><PERSON>", "hushtag": "Hashtag"}, "originality": "Originalität"}}, "buttons": {"generate": "<PERSON><PERSON><PERSON>"}}, "post_suggestion_preview": {"title": "Feed-<PERSON><PERSON><PERSON><PERSON>", "buttons": {"reset": "Z<PERSON>ücksetzen", "edit_text": "Text bearbeiten", "edit_image": "Bild bearbeiten"}}, "popularity": {"title": "Popularität", "awareness": "Bekanntheit", "consideration": "Berücksichtigung", "conversion": "Kon<PERSON>", "loyalty": "Loyalität", "explanation": "Erklärung:"}, "post_submission_controls": {"link_with_campaign": {"label": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> mit einer Kampagne verknüpfen?", "placeholder": "Kampagne auswählen"}, "dialog_save": {"title": "Speichern bestätigen", "description": "Sind <PERSON> sicher, dass Sie diesen Beitrag speichern möchten?", "base_select": {"label": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> mit einer Kampagne verknüpfen?", "placeholder": "Kampagnenname"}}, "dialog_schedule": {"title": "Planen", "description": "Planen Sie Ihren Beitrag und speichern Sie ihn", "social_media": {"label": "Soziale Medien auswählen", "select_platform": "Plattform auswählen", "page_name": "<PERSON><PERSON><PERSON><PERSON>"}}, "dialog_publish": {"title": "Veröffentlichen", "description": "Sind <PERSON> sicher, dass Sie diesen Beitrag veröffentlichen möchten?", "social_media": {"label": "Wählen Sie die Facebook-Seite für diesen Beitrag aus", "page_name": "<PERSON><PERSON><PERSON><PERSON>"}}}, "image_choice_card": {"all_categories": "Alle Kategorien", "type": {"title": "Wählen Sie Ihren bevorzugten Bildtyp", "categories": {"abstract_and_experimental": "Abstrakt und Experimentell", "artistic_and_creative": "Künstlerisch und Kreativ", "event_and_documentary": "Veranstaltung und Dokumentation", "fashion_and_commercial": "Mode und Werbung", "nature_and_environment": "Natur und Umwelt", "people_and_lifestyle": "Menschen und Lifestyle", "professional_services": "Professionelle Dienstleistungen", "specialized_photography": "Spezialisierte Fotografie", "urban_and_architecture": "Urban und Architektur"}, "content": {"abstract_photography": "Abstrakte Fotografie", "experimental_photography": "Experimentelle Fotografie", "infrared_photography": "Infrarotfotografie", "360_photography": "360-Grad-Fotografie", "light_painting_photography": "Lichtmalerei-Fotografie", "long_exposure_photography": "Langzeitbelichtungsfotografie", "conceptual_photography": "Konzeptfotografie", "fine_art_photography": "Kunstfotografie", "hdr_photography": "HDR-Fotografie", "surreal_photography": "Surreale Fotografie", "documentary_photography": "Dokumentarfotografie", "event_photography": "Eventfotografie", "photojournalism": "Fotojournalismus", "commercial_photography": "Kommerzielle Fotografie", "fashion_photography": "Modefotografie", "fashion_editorial_photography": "Mode-Editorial-Fotografie", "commercial_product_photography": "Kommerzielle Produktfotografie", "aerial_photography": "Luftbildfotografie", "astro_photography": "Astrofotografie", "landscape_photography": "Landschaftsfotografie", "macro_photography": "Makrofotografie", "underwater_photography": "Unterwasserfotografie", "wildlife_photography": "Tierfotografie", "lifestyle_photography": "Lifestyle-Fotografie", "portrait_photography": "Porträtfotografie", "pet_photography": "Tierfotografie", "wedding_photography": "Hochzeitsfotografie", "professional_branding": "Professionelles Branding", "professional_editorial": "Professionelles Editorial", "food_photography": "Food-Fotografie", "medical_photography": "Medizinische Fotografie", "night_photography": "Nachtfotografie", "panoramic_photography": "Panoramafotografie", "sports_action_photography": "Sport-Action-Fotografie", "sports_photography": "Sportfotografie", "architecture_photography": "Architekturfotografie", "real_estate_photography": "Immobilienfotografie", "street_photography": "Streetfotografie"}}, "style": {"title": "Wählen Sie Ihren bevorzugten Bildstil", "categories": {"basic": "Basic", "advanced": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "content": {"game_like_graphics": "Spielähnliche Grafiken", "luxurious_lighting": "Luxuriöse Beleuchtung", "dark_mysterious": "Dunkel, geheimnisvoll", "vintage_japanese": "Vintage-japanische Werbung", "photo_illustration": "Foto + Illustration", "dark_classical_themes": "Dunkle klassische Themen", "retro_dark_anime": "Retro-Dark-Anime", "fisheye_lens": "Fischaugenobjektiv", "soft_watercolors": "Weiche Wasserfarben", "double_exposure": "Doppelbelichtung", "film_style_grading": "Farbkorrektur im Filmstil", "shiny_surfaces": "Glänzende Oberflächen", "glitch_effects": "Glitch-Effekte", "neon_retro_glow": "Neon-Retro-Glühen", "fine_art_themes": "<PERSON><PERSON><PERSON><PERSON>", "painted_imagery": "Gemalte Bilder", "high_contrast_ink": "Hochkontrasttinte", "dreamlike_themes": "Traumhafte Themen", "ultra_realistic": "Ultrarealistisch", "cute_3d": "Niedlich 3D", "cartoonish": "<PERSON><PERSON><PERSON>", "stippled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "polaroid": "Polaroid", "3D": "3D", "illustration": "Illustration", "black_and_white": "Schwarz & Weiß", "painting": "<PERSON><PERSON><PERSON>", "neon": "Neon", "glow": "Glühen", "funky": "Ausgeflippt", "cartoon_in_reality": "Cartoon in der Realität", "retro": "Retro", "sketchy": "Skizzenhaft", "long_exposure": "Langzeitbelichtung", "multi_angles": "<PERSON><PERSON><PERSON>"}}, "effects": {"title": "Wählen Sie Ihre bevorzugten Bildeffekte (1 Element pro Kategorie)", "categories": {"color": "Farbe", "camera": "<PERSON><PERSON><PERSON>", "lighting": "Beleuchtung"}, "content": {"pastel": "<PERSON><PERSON>", "black_and_white": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gold_glow": "<PERSON><PERSON>", "vibrant": "Lebendig", "cold_neon": "<PERSON><PERSON><PERSON>", "portrait": "Porträt", "low_angle": "Untersicht", "mid_shot": "<PERSON><PERSON><PERSON>", "wide_shot": "<PERSON><PERSON><PERSON><PERSON>", "tilt_shot": "Kippaufnahme", "aerial": "Luftaufnahme", "iridescent": "<PERSON><PERSON><PERSON>", "dramatic": "Dramatisch", "long_exposure": "Langzeitbelichtung", "indoor": "Inn<PERSON><PERSON>nahme", "high_flash": "<PERSON><PERSON>", "neon": "Neon"}}}, "edit_text_creative_card": {"title": "Text bearbeiten", "buttons": {"adjust_options": "Optionen anpassen", "edit_prompt": "Eingabeaufforderung bearbeiten", "regenerate": "<PERSON><PERSON> gene<PERSON>"}, "update_post_form": {"placeholder": "Inhalt", "title": "Titel", "description": "Beschreibung", "call_to_action": "Call to Action", "link": "Link", "marketing_strategy": "Marketingstrategie", "private_model": "Privates Modell", "knowledge": "Wissen", "language": "<PERSON><PERSON><PERSON>", "tone_of_voice": "Tonfall"}}, "messages_schema": {"company_id_required": "CompanyId ist erforderlich", "brand_id_required": "BrandId ist erforderlich", "user_email_required": "UserEmail ist erforderlich", "title_required": "Titel ist erforderlich", "description_required": "Beschreibung ist erforderlich", "up_to_one_files": "Bitte wählen Sie bis zu 1 Datei aus", "file_size": "Die Dateigröße muss weniger als 5 MB betragen", "text_prompt_required": "Text Prompt ist erforderlich"}, "toast": {"success": {"generate_post": "<PERSON><PERSON> Beitrag wurde erfolgreich erstellt", "save_post": "<PERSON>in Beitrag wurde erfolgreich gespeichert", "update_post": "<PERSON>in Beitrag wurde erfolgreich aktualisiert"}}}