{"no_data_available": "No data available", "type_knowledge": {"content": "Content", "media": "Media", "audience": "Audience"}, "content": {"title": "Knowledge List", "new_content": "New Content", "new_media": "New Media", "new_audience": "New Audience", "subtitle": "A knowledge is a structured set of predefined information that guides the AI in generating relevant, targeted, and visually aligned posts."}, "content_form": {"title": "New Content", "type": "Type", "text": "Text Model", "url": "From URL", "file": "From File", "external_drive": "From External Drive", "name": "Name", "title_field": "Title", "content": "Content", "tags": "Tags", "website_name": "Website Name", "website_title": "Website Title", "website_url": "Website URL", "data_scraped": "The data was scraped successfully", "preview_data": "Preview Data", "file_name": "File Name", "google_drive": "From Google Drive", "sharepoint": "From SharePoint", "aitable": "From Aitable", "connect": "Connect", "accounts": "Accounts", "accounts_placeholder": "Select account", "no_options": "No options available", "select_file": "Select a File", "search_file": "Search a File ...", "select_site": "Select a Site", "site_placeholder": "www.site.com", "select_drive": "Select a Drive", "select_folder": "Select a Folder", "select_base": "Select a Base", "select_table": "Select a Table"}, "media_form": {"title": "New Media Content", "type": "Type", "logo": "Add logo", "logo_name": "Logo Name", "color": "Add color", "color_name": "Color name", "select_color": "Select Color", "name": "Name"}, "audience_form": {"title": "New Audience Segment", "name": "Audience Name", "age_range": "Age Range", "gender": "Gender", "gender_options": {"male": "Male", "female": "Female", "neutral": "Neutral"}, "spending_behavior": "Spending Behavior", "spending_behavior_options": {"budget_conscious": "Budget-conscious", "value_oriented": "Value-oriented", "aspirational": "Aspirational", "luxury": "Luxury", "experiential": "Experiential", "impulsive": "Impulsive", "select_all": "Select All", "select_options": "Select Options"}, "business_industry": "Business Industry", "interests_or_preferences": "Interests or Preferences"}, "schema": {"url_required": "Website URL is required", "name_required": "Name is required", "text_title_required": "Text Title is required", "text_content_required": "Text content is required", "website_title_required": "Website title is required", "website_url_required": "Website URL is required", "document_title_required": "Document Title is required", "file_required": "File is required", "logo_name_required": "Logo name is required", "minimum_color_length": "Minimum 1 color required", "maximum_color_length": "Maximum 6 colors allowed", "audience_name_required": "Audience name is required", "min_value_age_required": "Minimum 2 values required", "max_value_age_required": "Maximum 2 values allowed", "min_age": "Minimum age is 18", "max_age": "Maximum age is 60", "id_required": "Id is required", "file_optional": "File is optional", "adresse_email_invalid": "Invalid email address"}, "toast_knowledge": {"knowledge_deleted": "Knowledge deleted successfully", "website_scraped": "Website scraped successfully", "website_not_scraped": "Website not scraped", "text_created": "Text content created successfully", "text_not_created": "Text content not created", "website_created": "Website content created successfully", "website_not_created": "Website content not created", "file_created": "File content created successfully", "file_not_created": "File content not created", "logo_created": "Logo created successfully", "logo_not_created": "Logo not created", "color_created": "Color created successfully", "color_not_created": "Color not created", "audience_created": "Audience segment created successfully", "audience_not_created": "Audience segment not created", "text_updated": "Text content updated successfully", "text_not_updated": "Text content not updated", "website_updated": "Website content updated successfully", "website_not_updated": "Website content not updated", "file_updated": "File content updated successfully", "file_not_updated": "File content not updated", "logo_updated": "Logo updated successfully", "logo_not_updated": "Logo not updated", "color_updated": "Color updated successfully", "color_not_updated": "Color not updated", "audience_updated": "Audience segment updated successfully", "audience_not_updated": "Audience segment not updated", "google_drive_revoked": "Google account revoked", "google_drive_not_revoked": "Google account not revoked", "sharepoint_revoked": "SharePoint account revoked", "sharepoint_not_revoked": "SharePoint account not revoked", "aitable_revoked": "Aitable account revoked", "aitable_not_revoked": "Aitable account not revoked", "wait_scraping_data": "You should wait for the scraped data"}, "error_details": "Cannot get knowledge something went wrong!", "error_title_details": "Problem went getting the title", "error_details_content": "Problem went getting the content", "knowledge_details": "Knowledge Details", "detail_title": {"text": "Text Knowledge", "file": "File Knowledge", "website": "Website Knowledge", "audience": "Audience Knowledge", "logo": "Logo Knowledge", "color": "Color Knowledge"}, "description": "Description", "tags": "Tags", "no_labels": "No labels available", "url": "URL", "created_by": "Created By", "text": "Text", "file": "File", "website": "Website", "audience": "Audience", "logo": "Logo", "color": "Color"}