{"creative_card": {"title": "Creation of a new creative", "tabs": {"visual": {"title": "Visual", "children": {"image": {"title": "Generate Image"}, "video": {"title": "Generate Video"}}}, "text": {"title": "Text"}}, "form": {"general_infos": {"title": "General", "description": "Write a brief and precise prompt to guide the creation of your content", "social_media": "Social Media", "fields": {"title": {"label": "Title", "placeholder": "content"}, "description": {"label": "Description", "placeholder": "content"}}}, "image_generation": {"title": "Content", "description": "Provide the necessary details to generate a specific image", "question": "Do you want to include the file in the post?", "fields": {"image_content": {"label": "Image Content"}, "image_sizes": {"custom": "Custom", "square": "Square", "landscape": "Landscape", "cinemascope": "Cinemascope", "portrait": "Portrait", "vertical": "Vertical Cinemascope"}, "width": {"label": "<PERSON><PERSON><PERSON>"}, "height": {"label": "Height"}, "image_type": {"label": "Type", "placeholder": "Select image type", "button": "Switch"}, "image_style": {"label": "Style", "placeholder": "Select image style", "button": "Switch"}, "image_effects": {"label": "Effects", "placeholder": "Select image effects", "button": {"add": "Add", "change": "Change"}}}}, "cta_content": {"title": "<PERSON>ton Call to action", "description": "Define user action with a link to relevant page", "select": {"label": "Call to action", "placeholder": "content", "cta_texts": {"save_50_buy_now": "Save 50% - buy now!", "get_started": "Get started", "join_newsletter": "Join our exclusive newsletter", "discover_more": "Discover more", "try_it_now": "Try it now", "schedule_free_demo": "Schedule your free demo now", "instant_download": "Instant download available", "secure_cart": "Secure your cart", "subscribe_savings": "Subscribe for exclusive savings", "follow_for_updates": "Follow us and never miss an update", "unlock_potential": "Unlock your full potential", "claim_free_trial": "Claim your free trial", "watch_demo": "Watch the demo", "contact_us": "Contact us today"}}, "field": {"label": "Link", "placeholder": "content"}}, "marketing_infos": {"title": "Marketing", "description": "Define user action with a link to relevant page", "select": {"placeholder": "content", "marketing_strategy": {"label": "Marketing Strategy", "marketing_strategies_options": {"aida": "AIDA", "before_and_after": "Before and After", "consistency_voice_tone": "Consistency in voice and tone", "educational_content": "Educational content", "emotive_appeals": "Emotive appeals", "exclusivity_scarcity": "Exclusivity and scarcity", "fomo": "FOMO (Fear Of Missing Out)", "interactive_posts": "Interactive posts", "personalization": "Personalization", "problem_solution": "Problem and solution", "product_descriptions": "Product descriptions", "promotion": "Promotion", "retargeting": "Retargeting", "storytelling": "Storytelling", "testimonial": "Testimonial", "trending_topics": "Trending topics", "user_centric_messaging": "User-centric messaging", "value_proposition": "Value proposition", "visual_storytelling": "Visual storytelling"}}, "private_model": {"label": "Private Model"}, "knowledge": {"label": "Knowledge"}}}, "advanced_options": {"title": "Advanced Options", "description": "Tools to personalize and enhance your post's content effectively", "select": {"placeholder": "content", "language": {"label": "Language", "options": {"english": "English", "french": "French", "arabic": "Arabic", "italian": "Italian", "spanish": "Spanish", "korean": "Korean"}}, "tone_of_voice": {"label": "Tone of voice", "options": {"formal": "Formal", "informal": "Informal", "humorous": "Humorous", "serious": "Serious", "optimistic": "Optimistic", "motivating": "Motivating", "respectful": "Respectful", "assertive": "Assertive", "conversational": "Conversational", "persuasive": "Persuasive", "inspirational": "Inspirational", "educational": "Educational", "emotional": "Emotional", "dramatic": "Dramatic"}}}, "checkbox": {"emoji": "<PERSON><PERSON><PERSON>", "hushtag": "Hush tag"}, "originality": "Originality"}}, "buttons": {"generate": "Generate"}}, "post_suggestion_preview": {"title": "Feed Preview", "buttons": {"reset": "Reset", "edit_text": "Edit Text", "edit_image": "Edit Image"}}, "popularity": {"title": "Popularity", "awareness": "Awareness", "consideration": "Consideration", "conversion": "Conversion", "loyalty": "Loyalty", "explanation": "Explanation:"}, "post_submission_controls": {"link_with_campaign": {"label": "Do you want link with campaign?", "placeholder": "Select campaign"}, "dialog_save": {"title": "Confirm Save", "description": "Are you certain you want to proceed with saving this post?", "base_select": {"label": "Do you want link with campaign?", "placeholder": "Campaign Name"}}, "dialog_schedule": {"title": "Schedule", "description": "Schedule your post and save", "social_media": {"label": "Choose social media", "select_platform": "Select Platform", "page_name": "Page Name"}}, "dialog_publish": {"title": "Publish", "description": "Are you certain you want to proceed with publishing this post?", "social_media": {"label": "Select the Facebook Page for this post", "page_name": "Page Name"}}}, "image_choice_card": {"all_categories": "All Categories", "type": {"title": "Choose your preferred image type", "categories": {"abstract_and_experimental": "Abstract And Experimental", "artistic_and_creative": "Artistic And Creative", "event_and_documentary": "Event And Documentary", "fashion_and_commercial": "Fashion And Commercial", "nature_and_environment": "Nature And Environment", "people_and_lifestyle": "People And Lifestyle", "professional_services": "Professional Services", "specialized_photography": "Specialized Photography", "urban_and_architecture": "Urban And Architecture"}, "content": {"abstract_photography": "Abstract Photography", "experimental_photography": "Experimental Photography", "infrared_photography": "Infrared Photography", "360_photography": "360 Photography", "light_painting_photography": "Light Painting Photography", "long_exposure_photography": "Long Exposure Photography", "conceptual_photography": "Conceptual Photography", "fine_art_photography": "Fine Art Photography", "hdr_photography": "HDR Photography", "surreal_photography": "Surreal Photography", "documentary_photography": "Documentary Photography", "event_photography": "Event Photography", "photojournalism": "Photojournalism", "commercial_photography": "Commercial Photography", "fashion_photography": "Fashion Photography", "fashion_editorial_photography": "Fashion Editorial Photography", "commercial_product_photography": "Commercial Product Photography", "aerial_photography": "Aerial Photography", "astro_photography": "Astro Photography", "landscape_photography": "Landscape Photography", "macro_photography": "Macro Photography", "underwater_photography": "Underwater Photography", "wildlife_photography": "Wildlife Photography", "lifestyle_photography": "Lifestyle Photography", "portrait_photography": "Portrait Photography", "pet_photography": "Pet Photography", "wedding_photography": "Wedding Photography", "professional_branding": "Professional Branding", "professional_editorial": "Professional Editorial", "food_photography": "Food Photography", "medical_photography": "Medical Photography", "night_photography": "Night Photography", "panoramic_photography": "Panoramic Photography", "sports_action_photography": "Sports Action Photography", "sports_photography": "Sports Photography", "architecture_photography": "Architecture Photography", "real_estate_photography": "Real Estate Photography", "street_photography": "Street Photography"}}, "style": {"title": "Choose your preferred image style", "categories": {"basic": "Basic", "advanced": "Advanced"}, "content": {"game_like_graphics": "Game-like graphics", "luxurious_lighting": "Luxurious lighting", "dark_mysterious": "Dark, mysterious", "vintage_japanese": "Vintage Japanese ad", "photo_illustration": "Photo + Illustration", "dark_classical_themes": "Dark classical themes", "retro_dark_anime": "Retro dark anime", "fisheye_lens": "Fisheye lens", "soft_watercolors": "Soft watercolors", "double_exposure": "Double exposure", "film_style_grading": "Film-style grading", "shiny_surfaces": "Shiny surfaces", "glitch_effects": "Glitch effects", "neon_retro_glow": "Neon retro glow", "fine_art_themes": "Fine art themes", "painted_imagery": "Painted imagery", "high_contrast_ink": "High contrast ink", "dreamlike_themes": "Dreamlike themes", "ultra_realistic": "Ultra realistic", "cute_3d": "Cute 3D", "cartoonish": "Cartoonish", "stippled": "Stippled", "polaroid": "Polaroid", "3D": "3D", "illustration": "Illustration", "black_and_white": "Black & White", "painting": "Painting", "neon": "Neon", "glow": "Glow", "funky": "Funky", "cartoon_in_reality": "Cartoon in reality", "retro": "Retro", "sketchy": "Sketchy", "long_exposure": "Long exposure", "multi_angles": "Multi angles"}}, "effects": {"title": "Choose your preferred image effects (1 item per category)", "categories": {"color": "Color", "camera": "Camera", "lighting": "Lighting"}, "content": {"pastel": "Pastel", "black_and_white": "Black and White", "gold_glow": "Gold Glow", "vibrant": "Vibrant", "cold_neon": "Cold Neon", "portrait": "Portrait", "low_angle": "Low Angle", "mid_shot": "Mid Shot", "wide_shot": "Wide Shot", "tilt_shot": "Tilt Shot", "aerial": "Aerial", "iridescent": "Iridescent", "dramatic": "Dramatic", "long_exposure": "Long Exposure", "indoor": "Indoor", "high_flash": "High Flash", "neon": "Neon"}}}, "edit_text_creative_card": {"title": "Edit Text", "buttons": {"adjust_options": "Adjust Options", "edit_prompt": "Edit Prompt", "regenerate": "Regenerate"}, "update_post_form": {"placeholder": "content", "title": "Title", "description": "Description", "call_to_action": "Call to action", "link": "Link", "marketing_strategy": "Marketing Strategy", "private_model": "Private Model", "knowledge": "Knowledge", "language": "Language", "tone_of_voice": "Tone of voice"}}, "messages_schema": {"company_id_required": "CompanyId is required", "brand_id_required": "BrandId is required", "user_email_required": "UserEmail is required", "title_required": "Title is required", "description_required": "Description is required", "up_to_one_files": "Please select up to 1 files", "file_size": "File size must be less than 5MB", "text_prompt_required": "Text Prompt is required"}, "toast": {"success": {"generate_post": "Your post was generated successfully", "save_post": "Your post was saved successfully", "update_post": "Your post was updated successfully"}}}