{"no_data_available": "<PERSON><PERSON><PERSON> dato disponibile", "type_knowledge": {"content": "<PERSON><PERSON><PERSON>", "media": "Media", "audience": "Pubblico"}, "content": {"title": "Elenco delle conoscenze", "new_content": "Nuovo contenuto", "new_media": "Nuovi media", "new_audience": "Nuovo pubblico", "subtitle": "Una conoscenza è un insieme strutturato di informazioni predefinite che guida l'IA nella generazione di post pertinenti, mirati e visivamente allineati."}, "content_form": {"title": "Nuovo contenuto", "type": "Tipo", "text": "<PERSON><PERSON> di testo", "url": "Da URL", "file": "Da file", "external_drive": "Da unità esterna", "name": "Nome", "title_field": "<PERSON><PERSON>", "content": "<PERSON><PERSON><PERSON>", "tags": "Tag", "website_name": "Nome del sito web", "website_title": "<PERSON>lo del sito web", "website_url": "URL del sito web", "data_scraped": "I dati sono stati acquisiti con successo", "preview_data": "An<PERSON><PERSON> dati", "file_name": "Nome del file", "google_drive": "Da Google Drive", "sharepoint": "Da SharePoint", "aitable": "Da Aitable", "connect": "<PERSON><PERSON><PERSON>", "accounts": "Account", "accounts_placeholder": "Seleziona account", "no_options": "Nessuna opzione disponibile", "select_file": "Seleziona un file", "search_file": "Cerca un file ...", "select_site": "Seleziona un sito", "site_placeholder": "www.sito.com", "select_drive": "Seleziona un'unità", "select_folder": "Seleziona una cartella", "select_base": "Seleziona una base", "select_table": "Seleziona una tabella"}, "media_form": {"title": "Nuovo contenuto multimediale", "type": "Tipo", "logo": "Aggiungi logo", "logo_name": "Nome logo", "color": "Aggiungi colore", "color_name": "Nome colore", "select_color": "Seleziona colore", "name": "Nome"}, "audience_form": {"title": "Nuovo segmento di pubblico", "name": "Nome del pubblico", "age_range": "Fascia d'età", "gender": "<PERSON><PERSON>", "gender_options": {"male": "<PERSON><PERSON><PERSON>", "female": "<PERSON><PERSON><PERSON>", "neutral": "Neutro"}, "spending_behavior": "Comportamento di spesa", "spending_behavior_options": {"budget_conscious": "Attento al budget", "value_oriented": "Orientato al valore", "aspirational": "Aspirazionale", "luxury": "Lusso", "experiential": "Esperienziale", "impulsive": "Impulsivo", "select_all": "Se<PERSON><PERSON>na tutto", "select_options": "Seleziona opzioni"}, "business_industry": "Settore aziendale", "interests_or_preferences": "Interessi o <PERSON>enze"}, "schema": {"url_required": "L'URL del sito web è obbligatorio", "name_required": "Il nome è obbligatorio", "text_title_required": "Il titolo del testo è obbligatorio", "text_content_required": "Il contenuto del testo è obbligatorio", "website_title_required": "Il titolo del sito web è obbligatorio", "website_url_required": "L'URL del sito web è obbligatorio", "document_title_required": "Il titolo del documento è obbligatorio", "file_required": "Il file è obbligatorio", "logo_name_required": "Il nome del logo è obbligatorio", "minimum_color_length": "Minimo 1 colore richiesto", "maximum_color_length": "Massimo 6 colori consentiti", "audience_name_required": "Il nome del pubblico è obbligatorio", "min_value_age_required": "Minimo 2 valori richiesti", "max_value_age_required": "Massimo 2 valori consentiti", "min_age": "L'età minima è 18", "max_age": "L'età massima è 60", "id_required": "L'ID è obbligatorio", "file_optional": "Il file è facoltativo", "adresse_email_invalid": "Indirizzo email non valido"}, "toast_knowledge": {"knowledge_deleted": "Conoscenza eliminata con successo", "website_scraped": "Sito web acquisito con successo", "website_not_scraped": "Sito web non acquisito", "text_created": "Contenuto testuale creato con successo", "text_not_created": "Contenuto testuale non creato", "website_created": "Contenuto del sito web creato con successo", "website_not_created": "Contenuto del sito web non creato", "file_created": "Contenuto del file creato con successo", "file_not_created": "Contenuto del file non creato", "logo_created": "Logo creato con successo", "logo_not_created": "Logo non creato", "color_created": "Colore creato con successo", "color_not_created": "Colore non creato", "audience_created": "Segmento di pubblico creato con successo", "audience_not_created": "Segmento di pubblico non creato", "text_updated": "Contenuto testuale aggiornato con successo", "text_not_updated": "Contenuto testuale non aggiornato", "website_updated": "Contenuto del sito web aggiornato con successo", "website_not_updated": "Contenuto del sito web non aggiornato", "file_updated": "Contenuto del file aggiornato con successo", "file_not_updated": "Contenuto del file non aggiornato", "logo_updated": "Logo aggiornato con successo", "logo_not_updated": "Logo non aggiornato", "color_updated": "Colore aggiornato con successo", "color_not_updated": "Colore non aggiornato", "audience_updated": "Segmento di pubblico aggiornato con successo", "audience_not_updated": "Segmento di pubblico non aggiornato", "google_drive_revoked": "Account Google revocato", "google_drive_not_revoked": "Account Google non revocato", "sharepoint_revoked": "Account SharePoint revocato", "sharepoint_not_revoked": "Account SharePoint non revocato", "aitable_revoked": "Account <PERSON><PERSON> revocato", "aitable_not_revoked": "Account Aitable non revocato", "wait_scraping_data": "Dovresti attendere i dati acquisiti"}, "error_details": "Impossibile ottenere la conoscenza, qualcosa è andato storto!", "error_title_details": "Problema durante l'ottenimento del titolo", "error_details_content": "Problema durante l'ottenimento del contenuto", "knowledge_details": "Dettagli della conoscenza", "detail_title": {"text": "Conoscenza testuale", "file": "Conoscenza file", "website": "Conoscenza sito web", "audience": "Conoscenza pubblico", "logo": "Conoscenza logo", "color": "Conoscenza colore"}, "description": "Descrizione", "tags": "Tag", "no_labels": "Nessuna etichetta disponibile", "url": "URL", "created_by": "<PERSON><PERSON><PERSON> <PERSON>", "text": "<PERSON><PERSON>", "file": "File", "website": "Sito web", "audience": "Pubblico", "logo": "Logo", "color": "Colore"}