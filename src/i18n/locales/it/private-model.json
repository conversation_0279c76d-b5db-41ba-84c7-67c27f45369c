{"private_model_form": {"model_name": "Nome del modello", "model_name_placeholder": "<PERSON><PERSON><PERSON>", "description": "Descrizione", "description_placeholder": "<PERSON><PERSON><PERSON>", "topics": "Argomenti", "add": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "social_media": "Social media", "add_social_media": "Aggiungi social media", "select_platform": "Seleziona piattaforma", "page_name": "Nome della pagina", "image_category": "Categoria immagine", "image_category_placeholder": "Seleziona categoria immagine", "keyword": "<PERSON><PERSON><PERSON> chia<PERSON>", "object_name": "<PERSON><PERSON> og<PERSON>to", "image_urls": "URL delle immagini", "url": "URL", "import_image": "Importa immagine", "drag_drop": "Trascina e rilascia oppure", "link": "scegli i file", "upload": "per caricare", "delete": "Elimina", "new_model": "Nuovo modello", "info_general": "Informazioni generali", "preview": "Anteprima", "text_model": "<PERSON><PERSON> di testo", "image_model": "<PERSON><PERSON> immagine", "save": "<PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>"}, "preview_form": {"close": "<PERSON><PERSON>"}, "text-schema": {"topic-error": "Inserisci almeno un argomento", "model-name-error": "Inserisci un nome per il modello", "model-description-error": "Inserisci una descrizione del modello", "platform-error": "La piattaforma deve essere 'facebook', 'instagram', 'linkedin' o 'x'", "pageName-error": "Il nome della pagina è obbligatorio"}, "toast": {"deleting_model": "Eliminazione del modello in corso...", "model_deleted": "<PERSON><PERSON> eliminato", "deletion_failed": "Eliminazione fallita", "model_marked_as_favorite": "<PERSON><PERSON> contras<PERSON>gnato come preferito", "model_marked_as_not_favorite": "<PERSON><PERSON> rim<PERSON>o dai preferiti", "error_deleting_model": "Errore durante l'eliminazione del modello", "model_created_successfully": "Il tuo modello è stato creato con successo", "model_prapared_successfully": "Il tuo modello è stato preparato con successo", "error_preparing_model": "Errore nella preparazione del modello"}, "details": {"error_loading_model": "Errore durante il caricamento del modello", "private_model_details": "Dettagli del modello privato", "creation_date": "Data di creazione:", "created_by": "<PERSON><PERSON><PERSON> <PERSON>:", "model_status": "Stato del modello:", "tags": "Tag:", "no_tags": "<PERSON><PERSON><PERSON> tag", "images": "<PERSON><PERSON><PERSON><PERSON>", "texts": "Descrizione"}}