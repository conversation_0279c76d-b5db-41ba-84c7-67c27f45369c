'use client';

import { useEffect } from 'react';

import { KnowledgeUpdateFormsProvider } from '@/providers/knowledge-update-forms-provider';
import { useKnowledgeStore } from '@/stores/use-knowledge-store';

import { KnowledgeSheetDetails } from '@/components/knowledge/atoms/knowledge-sheet-details';
import { KnowledgeSheetHeader } from '@/components/knowledge/atoms/knowledge-sheet-header';
import { KnowledgeSheetTitle } from '@/components/knowledge/atoms/knowledge-sheet-title';
import { Sheet, SheetContent } from '@/components/ui/sheet';

export function KnowledgeDetail() {
  const { isDetailOpen, setDetailOpen, knowledge, setIsUpdating } =
    useKnowledgeStore();

  useEffect(() => {
    if (!isDetailOpen) {
      setIsUpdating(false);
    }
  }, [isDetailOpen, setIsUpdating]);

  return (
    <KnowledgeUpdateFormsProvider>
      <Sheet
        open={isDetailOpen}
        onOpenChange={(open) => {
          setDetailOpen(open);
        }}
      >
        <SheetContent className='flex flex-col gap-0 rounded-s-xl p-0'>
          <KnowledgeSheetHeader />

          <div className='flex flex-1 flex-col gap-5 overflow-hidden rounded-s-xl bg-[#FAFAFA] p-3 px-3.5'>
            <KnowledgeSheetTitle type={knowledge?.Type} />

            <KnowledgeSheetDetails type={knowledge?.Type} />
          </div>
        </SheetContent>
      </Sheet>
    </KnowledgeUpdateFormsProvider>
  );
}
