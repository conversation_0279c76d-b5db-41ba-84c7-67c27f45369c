import { useTranslations } from 'next-intl';

import { KnowledgeResponse } from '@/api/models/dtos/knowledge.dto';

import { cn } from '@/lib/utils';

import {
  useDeleteKnowledge,
  useMarkKnowledgeAsFavorite,
} from '@/api/hooks/knowledge/mutations';

import { useKnowledgeOptionsConfig } from '@/config/knowledge/use-knowledge-options-config';

import { Icons } from '../../shared/icons';
import { Button } from '../../ui/button';
import { Card, CardHeader, CardContent } from '../../ui/card';

interface GridViewModeProps {
  knowledge: KnowledgeResponse;
}

export function GridViewMode({ knowledge }: GridViewModeProps) {
  const t = useTranslations();

  const { mutateAsync: handleStarClick } = useMarkKnowledgeAsFavorite();
  const { mutateAsync: handleDeleteClick } = useDeleteKnowledge();

  const { tagType } = useKnowledgeOptionsConfig();

  const { colorType } = useKnowledgeOptionsConfig();

  const isFavorite = knowledge.Favorite;

  const getDescription = () => {
    switch (knowledge.Type) {
      case 'text_plain':
        return knowledge.Text?.Description;
      case 'text_doc':
        return knowledge.Doc.Description;
      case 'text_website':
        return knowledge.Website?.Description;
      default:
        return '';
    }
  };

  const colorBar = colorType[knowledge.Type] || 'bg-gray-500';
  const typeTag = tagType[knowledge.Type] || {
    label: knowledge.Type,
    color: 'bg-gray-500',
    textClass: 'text-white',
  };

  return (
    <Card className='flex h-full w-full flex-col rounded-xl border border-gray-300 bg-white'>
      <CardHeader className='relative flex-1 px-4 py-3'>
        <div className='flex items-center'>
          {knowledge.Type && (
            <div className={cn('mr-2 h-6 w-2 rounded-full', colorBar)} />
          )}
          <p className='line-clamp-1 text-sm font-bold text-black'>
            {knowledge.Name}
          </p>
        </div>
        <div className='flex flex-col gap-1'>
          <p className='text-xs font-bold text-[#A1A1AA]'>
            {knowledge.CreationTimestamp}
          </p>

          <div className='flex items-center'>
            <p className='text-xs font-bold text-[#A1A1AA]'>
              {t('created_by')}:
            </p>
            <p className='ml-1 text-xs text-[#A1A1AA]'>{knowledge.UserEmail}</p>
          </div>
        </div>
      </CardHeader>

      <CardContent className='gap-2 px-4 pb-2'>
        {getDescription() && (
          <p className='line-clamp-2 text-sm text-gray-800'>
            {getDescription()}
          </p>
        )}

        <div className='flex items-end justify-between pt-1'>
          <div
            className={cn(
              'rounded-3xl px-3 py-1 text-xs text-white',
              typeTag.color
            )}
          >
            {typeTag.label}
          </div>

          <div className='flex space-x-1'>
            <Button
              variant='ghost'
              size='icon'
              className={cn(
                'size-8 text-muted-foreground hover:text-[#F5A524]',
                {
                  'text-[#F5A524] hover:text-muted-foreground': isFavorite,
                }
              )}
              onClick={() => handleStarClick(knowledge.Id)}
            >
              <Icons.star fill={isFavorite ? 'currentColor' : 'none'} />
            </Button>

            <Button
              variant='ghost'
              size='icon'
              className='size-8 text-muted-foreground hover:text-destructive'
              onClick={() => handleDeleteClick(knowledge.Id)}
            >
              <Icons.trash />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
