'use client';

import { useTranslations } from 'next-intl';
import React from 'react';

import { colorKnowledgeFormUpdateOpts } from '@/config/knowledge/forms-config';

import ColorPalette from '@/components/shared/color-palette';
import { withForm } from '@/components/shared/form';

export const UpdateMediaColorForm = withForm({
  ...colorKnowledgeFormUpdateOpts,
  render: function Render({ form }) {
    const t = useTranslations();
    return (
      <form
        className='grid max-w-full gap-2'
        onSubmit={async (e) => {
          e.preventDefault();
          e.stopPropagation();
          await form.handleSubmit();
        }}
      >
        <form.AppField name='knowledge.Name'>
          {(field) => (
            <field.TextField
              label={t('media_form.color_name')}
              placeholder={t('media_form.color_name')}
            />
          )}
        </form.AppField>
        <form.AppField name='knowledge.Color'>
          {(field) => {
            const colorValues = field.getValue?.() || [];
            const hexValues = colorValues.map(
              (color: { ColorHex?: string }) => color.ColorHex || ''
            );

            return (
              <div className='w-full overflow-y-hidden'>
                <ColorPalette
                  value={hexValues}
                  onChange={(newHexValues) => {
                    const newValue = newHexValues.map((hex, index) => ({
                      Index: index,
                      ColorHex: hex,
                    }));
                    field.setValue(newValue);
                  }}
                />
              </div>
            );
          }}
        </form.AppField>
      </form>
    );
  },
});
