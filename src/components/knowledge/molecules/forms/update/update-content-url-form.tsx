'use client';

import { useTranslations } from 'next-intl';
import React from 'react';

import { useKnowledgeScrapedWebsiteStore } from '@/stores/knowledge-scraped-website-store';

import { websiteKnowledgeFormUpdateOpts } from '@/config/knowledge/forms-config';

import { withForm } from '@/components/shared/form';
import { Spinner } from '@/components/shared/spinner';

export const UpdateContentUrlForm = withForm({
  ...websiteKnowledgeFormUpdateOpts,
  render: function Render({ form }) {
    const t = useTranslations();
    const websiteScrapedResults = useKnowledgeScrapedWebsiteStore(
      (state) => state.websiteScrapedResults
    );

    const showResults = useKnowledgeScrapedWebsiteStore(
      (state) => state.showResults
    );

    return (
      <form
        className='grid gap-3'
        onSubmit={async (e) => {
          e.preventDefault();
          e.stopPropagation();
          await form.handleSubmit();
        }}
      >
        <form.AppField name='knowledge.Name'>
          {(field) => (
            <field.TextField
              label={t('content_form.website_title')}
              placeholder={t('content_form.website_title')}
            />
          )}
        </form.AppField>
        <form.AppField name='knowledge.Website.Link'>
          {(field) => (
            <field.TextField
              label={t('content_form.website_url')}
              placeholder={t('content_form.website_url')}
            />
          )}
        </form.AppField>

        <div className='flex items-center justify-center gap-2'>
          {websiteScrapedResults && websiteScrapedResults.isLoading && (
            <div className='flex w-full flex-col items-center justify-center text-primary'>
              <Spinner variant='circle' className='size-10' />
              <p>{t('searching')}</p>
            </div>
          )}

          {websiteScrapedResults &&
            !websiteScrapedResults.isLoading &&
            websiteScrapedResults.HtmlText &&
            !showResults && <p>{t('content_form.data_scraped')}</p>}

          {websiteScrapedResults &&
            !websiteScrapedResults.isLoading &&
            websiteScrapedResults.HtmlText &&
            showResults && <p>{websiteScrapedResults.HtmlText}</p>}
        </div>
      </form>
    );
  },
});
