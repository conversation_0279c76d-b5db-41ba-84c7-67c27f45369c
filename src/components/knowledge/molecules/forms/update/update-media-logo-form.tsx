'use client';

import { useTranslations } from 'next-intl';
import React from 'react';

import { logoKnowledgeFormUpdateOpts } from '@/config/knowledge/forms-config';

import { withForm } from '@/components/shared/form';
import { ImageUpload } from '@/components/shared/form/image-upload';

export const UpdateMediaLogoForm = withForm({
  ...logoKnowledgeFormUpdateOpts,
  render: function Render({ form }) {
    const t = useTranslations();
    return (
      <form
        className='grid gap-3'
        onSubmit={async (e) => {
          e.preventDefault();
          e.stopPropagation();
          await form.handleSubmit();
        }}
      >
        <form.AppField
          name='knowledge.Logo.Name'
          listeners={{
            onChange: ({ value }) => {
              form.setFieldValue('knowledge.Name', value);
            },
          }}
        >
          {(field) => (
            <field.TextField
              label={t('media_form.logo_name')}
              placeholder={t('media_form.logo_name')}
            />
          )}
        </form.AppField>
        <form.AppField name='file'>
          {() => (
            <ImageUpload
              onFileChange={(file) => {
                if (file) {
                  form.setFieldValue('file', file);
                }
              }}
            />
          )}
        </form.AppField>
      </form>
    );
  },
});
