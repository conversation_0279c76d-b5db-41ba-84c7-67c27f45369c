'use client';

import { useTranslations } from 'next-intl';
import React from 'react';

import { audienceKnowledgeFormUpdateOpts } from '@/config/knowledge/forms-config';
import { useKnowledgeOptionsConfig } from '@/config/knowledge/use-knowledge-options-config';

import { withForm } from '@/components/shared/form';
import { MultiSelect } from '@/components/shared/form/multi-select';
import { Icons } from '@/components/shared/icons';
import { Label } from '@/components/ui/label';

export const UpdateAudienceForm = withForm({
  ...audienceKnowledgeFormUpdateOpts,
  render: function Render({ form }) {
    const t = useTranslations();
    const { genderOptions, spendingBehaviorOptions } =
      useKnowledgeOptionsConfig();

    return (
      <form
        className='grid h-full gap-2 overflow-y-auto'
        onSubmit={(e) => {
          e.preventDefault();
          form.handleSubmit();
        }}
      >
        <form.AppField
          name='knowledge.Audience.Name'
          listeners={{
            onChange: ({ value }) => {
              form.setFieldValue('knowledge.Name', value);
            },
          }}
        >
          {(field) => (
            <field.TextField
              label={t('audience_form.name')}
              placeholder={t('audience_form.name')}
            />
          )}
        </form.AppField>
        <form.AppField name='knowledge.Audience.AgeRange'>
          {(field) => (
            <field.DualSliderField
              label={t('audience_form.age_range')}
              showRangeOnLabel
            />
          )}
        </form.AppField>

        <form.AppField name='knowledge.Audience.Gender'>
          {(field) => (
            <field.RadioGroupField
              label={t('audience_form.gender')}
              options={genderOptions}
            />
          )}
        </form.AppField>
        <form.AppField name='knowledge.Audience.SpendingBehavior'>
          {(field) => (
            <div className='space-y-2'>
              <div className='flex flex-wrap gap-2'>
                <Label>{t('audience_form.spending_behavior')}</Label>
                <Icons.infoCircle />
              </div>
              <MultiSelect
                options={spendingBehaviorOptions}
                onValueChange={(selectedValues) => {
                  field.handleChange(selectedValues);
                }}
                placeholder={t(
                  'audience_form.spending_behavior_options.select_options'
                )}
                animation={0.5}
                maxCount={3}
                className='my-custom-class'
              />
            </div>
          )}
        </form.AppField>

        <form.AppField name='knowledge.Audience.BusinessIndustry'>
          {(field) => (
            <field.TextFieldWithInnerTags
              showSeparateAddButton
              separateAddButtonText={t('add')}
              label={t('audience_form.business_industry')}
              required={false}
            />
          )}
        </form.AppField>

        <form.AppField name='knowledge.Audience.InterestAndPref'>
          {(field) => (
            <field.TextFieldWithInnerTags
              showSeparateAddButton
              separateAddButtonText={t('add')}
              label={t('audience_form.interests_or_preferences')}
              required={false}
            />
          )}
        </form.AppField>
      </form>
    );
  },
});
