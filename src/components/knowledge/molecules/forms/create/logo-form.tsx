'use client';

import { useTranslations } from 'next-intl';
import React from 'react';

import { logoKnowledgeFormOpts } from '@/config/knowledge/forms-config';

import { withForm } from '@/components/shared/form';
import { ImageUpload } from '@/components/shared/form/image-upload';

export const MediaLogoForm = withForm({
  ...logoKnowledgeFormOpts,
  render: function Render({ form }) {
    const t = useTranslations();

    return (
      <form
        className='flex flex-col gap-4'
        onSubmit={async (e) => {
          e.preventDefault();
          e.stopPropagation();
          await form.handleSubmit();
        }}
      >
        <div className='grid gap-3'>
          <form.AppField
            name='knowledge.Logo.Name'
            listeners={{
              onChange: ({ value }) => {
                form.setFieldValue('knowledge.Name', value);
              },
            }}
          >
            {(field) => (
              <field.TextField
                label={t('media_form.name')}
                placeholder={t('media_form.name')}
              />
            )}
          </form.AppField>
          <form.AppField name='file'>
            {() => (
              <ImageUpload
                uploadFileText={t('upload_file.upload_file')}
                addImageText={t('upload_file.add_image')}
                dropFilesText={t('upload_file.drop_file_text')}
                onFileChange={(file) => {
                  if (file) {
                    form.setFieldValue('file', file);
                  }
                }}
              />
            )}
          </form.AppField>
        </div>
      </form>
    );
  },
});
