'use client';
import { useTranslations } from 'next-intl';
import React from 'react';

import { useCreateAudienceKnowledge } from '@/api/hooks/knowledge/mutations';
import { audienceSchema } from '@/api/models/schemas/knowledge.schema';
import { useCurrentUserStore } from '@/stores/current-user-store';

import { audienceKnowledgeFormOpts } from '@/config/knowledge/forms-config';

import { useAppForm } from '@/components/shared/form';
import { Button } from '@/components/ui/button';
import {
  DialogTitle,
  DialogClose,
  DialogFooter,
  DialogHeader,
  DialogContent,
} from '@/components/ui/dialog';

import { AudienceForm } from './audience-form';

export function AudienceCreationForm() {
  const t = useTranslations();

  const { getUser } = useCurrentUserStore();
  const user = getUser();

  const { mutateAsync: createAudience } = useCreateAudienceKnowledge();

  const form = useAppForm({
    defaultValues: {
      knowledge: {
        ...audienceKnowledgeFormOpts.defaultValues.knowledge,
        CompanyId: user?.companyId ?? '',
        UserEmail: user?.email ?? '',
        BrandId: user?.brandId ?? '',
      },
    },
    validators: {
      onChange: audienceSchema(t),
    },
    onSubmit: async ({ value }) => {
      await createAudience(value);
    },
  });

  return (
    <DialogContent
      className='flex h-fit max-h-[90%] w-1/2 max-w-none flex-col overflow-hidden px-0'
      onOpenAutoFocus={(e) => e.preventDefault()}
      onCloseAutoFocus={() => form.reset()}
    >
      <DialogHeader className='px-6'>
        <DialogTitle className='text-base'>
          {t('audience_form.title')}
        </DialogTitle>
      </DialogHeader>

      <AudienceForm form={form} />

      <DialogFooter className='gap-2 px-6'>
        <DialogClose asChild>
          <Button
            type='button'
            variant='outline'
            className='border border-[#f312612e] bg-[#f312612e] text-[#F31260] hover:bg-[#F312604D] hover:text-[#F31260]'
            onAbort={() => form.reset()}
          >
            {t('cancel')}
          </Button>
        </DialogClose>
        <form.AppForm>
          <form.SubmitButton
            onClick={async () => {
              await form.handleSubmit();
            }}
          >
            {t('save')}
          </form.SubmitButton>
        </form.AppForm>
      </DialogFooter>
    </DialogContent>
  );
}
