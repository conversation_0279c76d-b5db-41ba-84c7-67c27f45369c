'use client';

import { useTranslations } from 'next-intl';
import { useState } from 'react';

import { useRevokeAccountSharepoint } from '@/api/hooks/knowledge/mutations';
import {
  useGetSitesSharepoint,
  useGetFilesSharepoint,
  useGetDrivesSharepoint,
  useGetFoldersSharepoint,
} from '@/api/hooks/knowledge/queries';
import { useGetSharepointAccounts } from '@/api/hooks/knowledge/queries';
import { knowledgeService } from '@/api/services/knowledge.service';
import { useCurrentUserStore } from '@/stores/current-user-store';

import { sharepointKnowledgeFormOpts } from '@/config/knowledge/forms-config';

import { withForm } from '@/components/shared/form';
import { Icons } from '@/components/shared/icons';
import { Button } from '@/components/ui/button';

export const SharePointForm = withForm({
  ...sharepointKnowledgeFormOpts,
  render: function Render({ form }) {
    const t = useTranslations();

    const currentUser = useCurrentUserStore().getUser();

    const [selectedAccount, setSelectedAccount] = useState('');
    const [selectedSite, setSelectedSite] = useState('');
    const [selectedDrive, setSelectedDrive] = useState('');
    const [selectedFolder, setSelectedFolder] = useState('');

    const {
      data: accounts,
      isLoading: isAccountLoading,
      refetch: refetchAccounts,
    } = useGetSharepointAccounts(currentUser?.companyId, currentUser?.brandId);

    const { data: sites } = useGetSitesSharepoint(
      currentUser?.companyId,
      currentUser?.brandId,
      selectedAccount
    );

    const { data: drives } = useGetDrivesSharepoint(
      currentUser?.companyId,
      currentUser?.brandId,
      selectedAccount,
      selectedSite
    );

    const { data: folders } = useGetFoldersSharepoint(
      currentUser?.companyId,
      currentUser?.brandId,
      selectedAccount,
      selectedSite,
      selectedDrive
    );

    const { data: files } = useGetFilesSharepoint(
      currentUser?.companyId,
      currentUser?.brandId,
      selectedAccount,
      selectedSite,
      selectedDrive,
      selectedFolder
    );

    const { mutateAsync: revokeAccountSharepoint } = useRevokeAccountSharepoint(
      currentUser?.companyId,
      currentUser?.brandId
    );

    const handleRevokeAccount = async () => {
      await revokeAccountSharepoint(selectedAccount);
      form.reset();
      await refetchAccounts();
    };

    const handleConnect = () => {
      try {
        const url = knowledgeService.buildExternalDriveUrl(
          'SHAREPOINT',
          currentUser?.companyId,
          currentUser?.brandId
        );

        knowledgeService.openPopupAndMonitor(url, () => {
          refetchAccounts();
        });
      } catch (error) {
        console.error('Error during authentication:', error);
      }
    };

    return (
      <form
        className='mt-2 grid items-start gap-3'
        onSubmit={async (e) => {
          e.preventDefault();
          e.stopPropagation();
          await form.handleSubmit();
        }}
      >
        <div className='flex items-center justify-between'>
          <div className='text-sm font-medium'>
            {t('content_form.accounts')}

            <span className='ms-2 text-red-500'>*</span>
          </div>
          <Button onClick={handleConnect} className='rounded-2xl' type='button'>
            {t('content_form.connect')}
          </Button>
        </div>
        <form.AppField
          name='AccountEmail'
          listeners={{
            onChange: ({ value }) => {
              setSelectedAccount(value);
            },
          }}
        >
          {(field) => (
            <field.SelectField
              isLoading={isAccountLoading}
              placeholder={t('content_form.accounts_placeholder')}
              options={
                accounts?.map((account) => ({
                  label: account,
                  value: account,
                })) ?? []
              }
              showOptionDelete={true}
              onDeleteOption={handleRevokeAccount}
            />
          )}
        </form.AppField>
        {selectedAccount && (
          <>
            <form.AppField name='Title'>
              {(field) => (
                <field.TextField
                  label={t('content_form.title_field')}
                  placeholder={t('content_form.title_field')}
                />
              )}
            </form.AppField>
            <div className='relative flex items-center gap-4'>
              <div className='border-blue-500text-sm relative z-10 flex h-7 w-7 flex-shrink-0 items-center justify-center rounded-full border-2 border-blue-500 font-medium text-blue-500'>
                1
              </div>
              <div className='w-full'>
                <form.AppField
                  name='SiteId'
                  listeners={{
                    onChange: ({ value }) => {
                      setSelectedSite(value);
                    },
                  }}
                >
                  {(field) => (
                    <field.SelectField
                      required={false}
                      showOptionIcon
                      label={t('content_form.select_site')}
                      placeholder={t('content_form.site_placeholder')}
                      options={
                        sites?.map((site) => ({
                          label: site.name,
                          value: site.id,
                          icon: <Icons.site className='text-primary' />,
                        })) ?? []
                      }
                    />
                  )}
                </form.AppField>
              </div>
            </div>
            {selectedSite ? (
              <div className='relative flex items-center gap-4'>
                <div className='relative z-10 flex h-7 w-7 flex-shrink-0 items-center justify-center rounded-full border-2 border-blue-500 text-sm font-medium text-blue-500'>
                  2
                </div>
                <div className='w-full'>
                  <form.AppField
                    name='DriveId'
                    listeners={{
                      onChange: ({ value }) => {
                        setSelectedDrive(value);
                      },
                    }}
                  >
                    {(field) => (
                      <field.SelectField
                        required={false}
                        label={t('content_form.select_drive')}
                        placeholder={t('content_form.select_drive')}
                        showOptionIcon
                        options={
                          drives?.map((drive) => ({
                            label: drive.name,
                            value: drive.id,
                            icon: <Icons.drive className='text-primary' />,
                          })) ?? []
                        }
                      />
                    )}
                  </form.AppField>
                </div>
              </div>
            ) : (
              <div className='relative flex items-center gap-4'>
                <div className='relative z-10 flex h-7 w-7 flex-shrink-0 items-center justify-center rounded-full border-2 border-gray-500 text-sm font-medium text-gray-500'>
                  2
                </div>
                <div className='w-full text-gray-600'>
                  {t('content_form.select_drive')}
                </div>
              </div>
            )}
            {selectedDrive ? (
              <div className='relative flex items-center gap-4'>
                <div className='relative z-10 flex h-7 w-7 flex-shrink-0 items-center justify-center rounded-full border-2 border-blue-500 text-sm font-medium text-blue-500'>
                  3
                </div>
                <div className='w-full'>
                  <form.AppField
                    name='FolderId'
                    listeners={{
                      onChange: ({ value }) => {
                        setSelectedFolder(value);
                      },
                    }}
                  >
                    {(field) => (
                      <field.SelectField
                        required={false}
                        showOptionIcon
                        label={t('content_form.select_folder')}
                        placeholder={t('content_form.select_folder')}
                        options={
                          folders?.map((folder) => ({
                            label: folder.name,
                            value: folder.id,
                          })) ?? []
                        }
                      />
                    )}
                  </form.AppField>
                </div>
              </div>
            ) : (
              <div className='relative flex items-center gap-4'>
                <div className='relative z-10 flex h-7 w-7 flex-shrink-0 items-center justify-center rounded-full border-2 border-gray-500 text-sm font-medium text-gray-500'>
                  3
                </div>
                <div className='w-full text-gray-600'>
                  {t('content_form.select_folder')}
                </div>
              </div>
            )}
            {selectedFolder ? (
              <div className='relative flex items-center gap-4'>
                <div className='relative z-10 flex h-7 w-7 flex-shrink-0 items-center justify-center rounded-full border-2 border-blue-500 text-sm font-medium text-blue-500'>
                  4
                </div>
                <div className='w-full'>
                  <form.AppField
                    name='FileId'
                    listeners={{
                      onChange: ({ value }) => {
                        form.setFieldValue('FileName', value);
                      },
                    }}
                  >
                    {(field) => (
                      <field.RadioGroupField
                        label={t('content_form.search_file')}
                        itemOptionOrientation='vertical'
                        radioGroupClassName='grid-cols-6 gap-2 grid'
                        labelItemClassName='truncate items-end justify-center text-xs text-foreground'
                        radioItemClassName='relative h-28 flex-1 rounded-lg border border-slate-200'
                        showIndicatorIcon={false}
                        options={
                          files?.map((file) => ({
                            label: file.name,
                            value: file.id,
                          })) ?? []
                        }
                      />
                    )}
                  </form.AppField>
                </div>
              </div>
            ) : (
              <div className='relative flex items-center gap-4'>
                <div className='relative z-10 flex h-7 w-7 flex-shrink-0 items-center justify-center rounded-full border-2 border-gray-500 text-sm font-medium text-gray-500'>
                  4
                </div>
                <div className='w-full text-gray-600'>
                  {t('content_form.search_file')}
                </div>
              </div>
            )}
          </>
        )}
      </form>
    );
  },
});
