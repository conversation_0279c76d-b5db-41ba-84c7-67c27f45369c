'use client';

import { useTranslations } from 'next-intl';
import React from 'react';

import { useKnowledgeScrapedWebsiteStore } from '@/stores/knowledge-scraped-website-store';

import { prepareUrlContentFormOpts } from '@/config/knowledge/forms-config';

import { withForm } from '@/components/shared/form';
import { Icons } from '@/components/shared/icons';
import { Spinner } from '@/components/shared/spinner';

export const ContentUrlForm = withForm({
  ...prepareUrlContentFormOpts,
  render: function Render({ form }) {
    const t = useTranslations();

    const websiteScrapedResults = useKnowledgeScrapedWebsiteStore(
      (state) => state.websiteScrapedResults
    );

    const showResults = useKnowledgeScrapedWebsiteStore(
      (state) => state.showResults
    );

    return (
      <form
        className='grid gap-3'
        onSubmit={async (e) => {
          e.preventDefault();
          e.stopPropagation();
          await form.handleSubmit();
        }}
      >
        <form.AppField name='title'>
          {(field) => (
            <field.TextField
              label={t('content_form.website_name')}
              placeholder={t('content_form.website_name')}
            />
          )}
        </form.AppField>
        <form.AppField name='url'>
          {(field) => (
            <field.TextField
              label={t('content_form.website_url')}
              placeholder={t('content_form.website_url')}
            />
          )}
        </form.AppField>

        <div className='flex items-center justify-center gap-2'>
          {websiteScrapedResults && websiteScrapedResults.isLoading && (
            <div className='flex w-full flex-col items-center justify-center p-12 text-primary'>
              <Spinner variant='circle' className='size-10' />
              <p>{t('searching')}</p>
            </div>
          )}

          {websiteScrapedResults &&
            !websiteScrapedResults.isLoading &&
            websiteScrapedResults.HtmlText &&
            !showResults && (
              <div className='flex flex-col items-center justify-center gap-2 p-12'>
                <Icons.cloudCheck className='text-primary' />
                <div className='text-primary'>
                  {t('content_form.data_scraped')}
                </div>
              </div>
            )}

          {websiteScrapedResults &&
            !websiteScrapedResults.isLoading &&
            websiteScrapedResults.HtmlText &&
            showResults && (
              <div className='flex flex-col justify-center gap-2'>
                <div className='font-semibold'>
                  {t('content_form.preview_data')}
                </div>
                <p>{websiteScrapedResults.HtmlText}</p>
              </div>
            )}
        </div>
      </form>
    );
  },
});
