'use client';

import { useTranslations } from 'next-intl';
import { useState, useEffect } from 'react';

import { useRevokeAccountAirtable } from '@/api/hooks/knowledge/mutations';
import {
  useGetBasesAirtable,
  useGetTablesAirtable,
  useGetAccountsAirtable,
} from '@/api/hooks/knowledge/queries';
import { knowledgeService } from '@/api/services/knowledge.service';
import { useCurrentUserStore } from '@/stores/current-user-store';

import { airtableKnowledgeFormOpts } from '@/config/knowledge/forms-config';

import { withForm } from '@/components/shared/form';
import { Icons } from '@/components/shared/icons';
import { Button } from '@/components/ui/button';

export const AirtableForm = withForm({
  ...airtableKnowledgeFormOpts,
  render: function Render({ form }) {
    const t = useTranslations();

    const currentUser = useCurrentUserStore().getUser();

    const [selectedAccount, setSelectedAccount] = useState('');
    const [selectedBase, setSelectedBase] = useState('');
    const [selectedTable, setSelectedTable] = useState('');

    const {
      data: accounts,
      isLoading: isAccountLoading,
      refetch: refetchAccounts,
    } = useGetAccountsAirtable(currentUser?.companyId, currentUser?.brandId);

    const { data: bases } = useGetBasesAirtable(
      currentUser?.companyId,
      currentUser?.brandId,
      selectedAccount
    );

    const { data: tables } = useGetTablesAirtable(
      currentUser?.companyId,
      currentUser?.brandId,
      selectedAccount,
      selectedBase
    );

    const { mutateAsync: revokeAccountAirtable } = useRevokeAccountAirtable(
      currentUser?.companyId,
      currentUser?.brandId
    );

    useEffect(() => {
      if (selectedTable) {
        form.setFieldValue('TableName', selectedTable);
      }
    }, [selectedTable, form]);

    const handleRevokeAccount = async (accountEmail: string) => {
      try {
        await revokeAccountAirtable(accountEmail);
        if (selectedAccount === accountEmail) {
          setSelectedAccount('');
          setSelectedBase('');
          setSelectedTable('');

          form.setFieldValue('AccountEmail', '');
          form.setFieldValue('BaseId', '');
          form.setFieldValue('TableId', '');
        }
        refetchAccounts();
      } catch (error) {
        console.error('Error revoking account:', error);
      }
    };

    const handleConnect = () => {
      try {
        const url = knowledgeService.buildExternalDriveUrl(
          'AIRTABLE',
          currentUser?.companyId,
          currentUser?.brandId
        );

        knowledgeService.openPopupAndMonitor(url, () => {
          refetchAccounts();
        });
      } catch (error) {
        console.error('Error during authentication:', error);
      }
    };

    return (
      <form
        className='mt-2 grid items-start gap-3'
        onSubmit={async (e) => {
          e.preventDefault();
          e.stopPropagation();
          await form.handleSubmit();
        }}
      >
        <div className='flex items-center justify-between'>
          <div className='text-sm font-medium'>
            {t('content_form.accounts')}
            <span className='ms-2 text-red-500'>*</span>
          </div>
          <Button onClick={handleConnect} className='rounded-2xl' type='button'>
            {t('content_form.connect')}
          </Button>
        </div>
        <form.AppField
          name='AccountEmail'
          listeners={{
            onChange: ({ value }) => {
              setSelectedAccount(value);
            },
          }}
        >
          {(field) => (
            <field.SelectField
              isLoading={isAccountLoading}
              placeholder={t('content_form.accounts_placeholder')}
              emptyDataMessage={t('content_form.no_accounts')}
              options={
                accounts?.map((account) => ({
                  label: account,
                  value: account,
                })) ?? []
              }
              showOptionDelete={true}
              onDeleteOption={handleRevokeAccount}
            />
          )}
        </form.AppField>
        {selectedAccount && (
          <>
            <form.AppField name='Title'>
              {(field) => (
                <field.TextField
                  label={t('content_form.title_field')}
                  placeholder={t('content_form.title_field')}
                />
              )}
            </form.AppField>
            <div className='relative flex items-center gap-4'>
              <div className='border-blue-500text-sm relative z-10 flex h-7 w-7 flex-shrink-0 items-center justify-center rounded-full border-2 border-blue-500 font-medium text-blue-500'>
                1
              </div>
              <div className='w-full'>
                <form.AppField
                  name='BaseId'
                  listeners={{
                    onChange: ({ value }) => {
                      setSelectedBase(value);
                    },
                  }}
                >
                  {(field) => (
                    <field.SelectField
                      required={false}
                      label={t('content_form.select_base')}
                      placeholder={t('content_form.select_base')}
                      showOptionIcon
                      options={
                        bases?.map((base) => ({
                          label: base.name,
                          value: base.id,
                          icon: <Icons.base className='text-primary' />,
                        })) ?? []
                      }
                    />
                  )}
                </form.AppField>
              </div>
            </div>
            {selectedBase ? (
              <div className='relative flex items-center gap-4'>
                <div className='relative z-10 flex h-7 w-7 flex-shrink-0 items-center justify-center rounded-full border-2 border-blue-500 text-sm font-medium text-blue-500'>
                  2
                </div>
                <div className='w-full'>
                  <form.AppField
                    name='TableId'
                    listeners={{
                      onChange: ({ value }) => {
                        const file = tables?.find((t) => t.id === value);
                        if (file) {
                          setSelectedTable(file.name);
                        }
                      },
                    }}
                  >
                    {(field) => (
                      <field.SelectField
                        label={t('content_form.select_table')}
                        placeholder={t('content_form.select_table')}
                        required={false}
                        showOptionIcon
                        options={
                          tables?.map((table) => ({
                            label: table.name,
                            value: table.id,
                            icon: <Icons.table className='text-primary' />,
                          })) ?? []
                        }
                      />
                    )}
                  </form.AppField>
                </div>
              </div>
            ) : (
              <div className='relative flex items-center gap-4'>
                <div className='relative z-10 flex h-7 w-7 flex-shrink-0 items-center justify-center rounded-full border-2 border-gray-500 text-sm font-medium text-gray-500'>
                  2
                </div>
                <div className='w-full text-gray-600'>
                  {t('content_form.select_table')}
                </div>
              </div>
            )}
          </>
        )}
      </form>
    );
  },
});
