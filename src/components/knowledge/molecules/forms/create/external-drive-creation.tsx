'use client';

import { useTranslations } from 'next-intl';

import {
  useSelectFileKnowledge,
  useSelectTableAirtable,
  useSelectFileGoogleDrive,
} from '@/api/hooks/knowledge/mutations';
import {
  selectTableAirtableSchema,
  selectFileSharepointSchema,
  selectFileGoogleDriveSchema,
} from '@/api/models/schemas/knowledge.schema';
import { useCurrentUserStore } from '@/stores/current-user-store';

import {
  airtableKnowledgeFormOpts,
  sharepointKnowledgeFormOpts,
  googleDriveKnowledgeFormOpts,
} from '@/config/knowledge/forms-config';
import { TExternalDriveTab } from '@/config/knowledge/options-config';

import { useAppForm } from '@/components/shared/form';
import { Icons } from '@/components/shared/icons';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';

import { AirtableForm } from './airtable-form';
import { GoogleDriveForm } from './google-drive-form';
import { SharePointForm } from './sharepoint-form';

interface ContentFormProps {
  tabName: TExternalDriveTab;
  setTabName: (value: TExternalDriveTab) => Promise<URLSearchParams>;
}
export function FormExternalDrive({ tabName, setTabName }: ContentFormProps) {
  const t = useTranslations();

  const { mutateAsync: selectFileSharepoint } = useSelectFileKnowledge();
  const { mutateAsync: selectFileAirtable } = useSelectTableAirtable();
  const { mutateAsync: selectFileGoogleDrive } = useSelectFileGoogleDrive();

  const { getUser } = useCurrentUserStore();
  const user = getUser();

  const googleDriveForm = useAppForm({
    defaultValues: {
      ...googleDriveKnowledgeFormOpts.defaultValues,
      CompanyId: user?.companyId ?? '',
      BrandId: user?.brandId ?? '',
    },
    validators: {
      onChange: selectFileGoogleDriveSchema(t),
    },
    onSubmit: async ({ value }) => {
      await selectFileGoogleDrive(value);
    },
  });

  const sharePointForm = useAppForm({
    defaultValues: {
      ...sharepointKnowledgeFormOpts.defaultValues,
      CompanyId: user?.companyId ?? '',
      BrandId: user?.brandId ?? '',
    },
    validators: {
      onChange: selectFileSharepointSchema(t),
    },
    onSubmit: async ({ value }) => {
      await selectFileSharepoint(value);
    },
  });

  const airtableForm = useAppForm({
    defaultValues: {
      ...airtableKnowledgeFormOpts.defaultValues,
      CompanyId: user?.companyId ?? '',
      BrandId: user?.brandId ?? '',
    },
    validators: {
      onChange: selectTableAirtableSchema(t),
    },
    onSubmit: async ({ value }) => {
      await selectFileAirtable(value);
    },
  });

  return (
    <>
      <div className='space-y-2 overflow-y-auto'>
        <Tabs
          value={tabName ?? 'google-drive'}
          onValueChange={async (value) => {
            await setTabName(value as typeof tabName);
          }}
        >
          <TabsList className='flex h-auto rounded-none bg-transparent p-0'>
            <TabsTrigger
              value='google-drive'
              className='relative flex-1 gap-3 rounded-none py-2 after:absolute after:inset-x-0 after:bottom-0 after:h-0.5 data-[state=active]:bg-transparent data-[state=active]:text-primary data-[state=active]:shadow-none data-[state=active]:after:bg-primary'
            >
              <Icons.googleDrive />
              {t('content_form.google_drive')}
            </TabsTrigger>
            <TabsTrigger
              value='share-point'
              className='relative flex-1 gap-3 rounded-none py-2 after:absolute after:inset-x-0 after:bottom-0 after:h-0.5 data-[state=active]:bg-transparent data-[state=active]:text-primary data-[state=active]:shadow-none data-[state=active]:after:bg-primary'
            >
              <Icons.sharePoint />
              {t('content_form.sharepoint')}
            </TabsTrigger>
            <TabsTrigger
              value='airtable'
              id='airtable'
              className='relative flex-1 gap-3 rounded-none py-2 after:absolute after:inset-x-0 after:bottom-0 after:h-0.5 data-[state=active]:bg-transparent data-[state=active]:text-primary data-[state=active]:shadow-none data-[state=active]:after:bg-primary'
            >
              <Icons.airtable />
              {t('content_form.aitable')}
            </TabsTrigger>
          </TabsList>

          {tabName === 'google-drive' && (
            <GoogleDriveForm form={googleDriveForm} />
          )}
          {tabName === 'share-point' && (
            <SharePointForm form={sharePointForm} />
          )}
          {tabName === 'airtable' && <AirtableForm form={airtableForm} />}
        </Tabs>
      </div>
      {tabName === 'share-point' && (
        <sharePointForm.AppForm>
          <sharePointForm.SubmitButton
            onClick={async () => {
              await sharePointForm.handleSubmit();
            }}
          >
            {t('save')}
          </sharePointForm.SubmitButton>
        </sharePointForm.AppForm>
      )}
      {tabName === 'google-drive' && (
        <googleDriveForm.AppForm>
          <googleDriveForm.SubmitButton
            onClick={async () => {
              await googleDriveForm.handleSubmit();
            }}
          >
            {t('save')}
          </googleDriveForm.SubmitButton>
        </googleDriveForm.AppForm>
      )}
      {tabName === 'airtable' && (
        <airtableForm.AppForm>
          <airtableForm.SubmitButton
            onClick={async () => {
              await airtableForm.handleSubmit();
            }}
          >
            {t('save')}
          </airtableForm.SubmitButton>
        </airtableForm.AppForm>
      )}
    </>
  );
}
