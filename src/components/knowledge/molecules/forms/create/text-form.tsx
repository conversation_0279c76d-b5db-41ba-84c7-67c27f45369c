'use client';

import { useTranslations } from 'next-intl';
import React from 'react';

import { textKnowledgeFormOpts } from '@/config/knowledge/forms-config';

import { withForm } from '@/components/shared/form';

export const ContentTextForm = withForm({
  ...textKnowledgeFormOpts,
  render: function Render({ form }) {
    const t = useTranslations();

    return (
      <form
        className='grid gap-3'
        onSubmit={async (e) => {
          e.preventDefault();
          e.stopPropagation();
          await form.handleSubmit();
        }}
      >
        <form.AppField name='knowledge.Name'>
          {(field) => (
            <field.TextField
              label={t('content_form.name')}
              placeholder={t('content_form.name')}
            />
          )}
        </form.AppField>
        <form.AppField name='knowledge.Text.Title'>
          {(field) => (
            <field.TextField
              label={t('content_form.title_field')}
              placeholder={t('content_form.title_field')}
            />
          )}
        </form.AppField>
        <form.AppField name='knowledge.Text.Description'>
          {(field) => (
            <field.TextareaField
              label={t('content_form.content')}
              className='md:h-[193px]'
            />
          )}
        </form.AppField>
        <form.AppField name='knowledge.Text.Labels'>
          {(field) => (
            <field.TextFieldWithInnerTags
              separateAddButtonText={t('add')}
              showSeparateAddButton
              label={t('content_form.tags')}
              showInfoIcon
              required={false}
            />
          )}
        </form.AppField>
      </form>
    );
  },
});
