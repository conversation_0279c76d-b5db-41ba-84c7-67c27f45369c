'use client';

import { useTranslations } from 'next-intl';
import React from 'react';

import { fileKnowledgeFormOpts } from '@/config/knowledge/forms-config';

import { withForm } from '@/components/shared/form';
import { FileUpload } from '@/components/shared/form/file-upload';

export const ContentFileForm = withForm({
  ...fileKnowledgeFormOpts,
  render: function Render({ form }) {
    const t = useTranslations();

    return (
      <form
        className='mt-2 flex flex-col gap-4'
        onSubmit={async (e) => {
          e.preventDefault();
          e.stopPropagation();
          await form.handleSubmit();
        }}
      >
        <div className='grid gap-3'>
          <form.AppField
            name='knowledge.Doc.Title'
            listeners={{
              onChange: ({ value }) => {
                form.setFieldValue('knowledge.Name', value);
              },
            }}
          >
            {(field) => (
              <field.TextField
                label={t('content_form.file_name')}
                placeholder={t('content_form.file_name')}
              />
            )}
          </form.AppField>
          <form.AppField name='file'>
            {() => (
              <FileUpload
                uploadFileText={t('upload_file.upload_file')}
                addPdfText={t('upload_file.add_pdf')}
                dropFilesText={t('upload_file.drop_file_text')}
                onFileChange={(file) => {
                  if (file) {
                    form.setFieldValue('file', file);
                  }
                }}
              />
            )}
          </form.AppField>
        </div>
      </form>
    );
  },
});
