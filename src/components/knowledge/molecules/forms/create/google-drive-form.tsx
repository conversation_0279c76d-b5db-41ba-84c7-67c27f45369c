'use client';

import { useTranslations } from 'next-intl';
import { useState, useEffect } from 'react';

import { useRevokeAccountGoogle } from '@/api/hooks/knowledge/mutations';
import {
  useGetGoogleDriveFiles,
  useGetGoogleDriveAccounts,
} from '@/api/hooks/knowledge/queries';
import { knowledgeService } from '@/api/services/knowledge.service';
import { useCurrentUserStore } from '@/stores/current-user-store';

import { googleDriveKnowledgeFormOpts } from '@/config/knowledge/forms-config';

import { withForm } from '@/components/shared/form';
import { Icons } from '@/components/shared/icons';
import { Button } from '@/components/ui/button';

export const GoogleDriveForm = withForm({
  ...googleDriveKnowledgeFormOpts,
  render: function Render({ form }) {
    const t = useTranslations();

    const { getUser } = useCurrentUserStore();
    const user = getUser();

    const [selectedAccount, setSelectedAccount] = useState('');
    const [selectedFile, setSelectedFile] = useState('');
    const [searchTerm] = useState('');

    useEffect(() => {
      if (selectedFile) {
        form.setFieldValue('FileName', selectedFile);
      }
    }, [selectedFile, form]);

    const {
      data: accounts,
      isLoading: isAccountLoading,
      refetch: refetchAccounts,
    } = useGetGoogleDriveAccounts(user?.companyId, user?.brandId);

    const { data: files } = useGetGoogleDriveFiles(
      user?.companyId,
      user?.brandId,
      selectedAccount
    );
    const { mutateAsync: revokeAccountGoogle } = useRevokeAccountGoogle(
      user?.companyId,
      user?.brandId
    );

    const filteredFiles =
      files?.filter((file) =>
        file.name.toLowerCase().includes(searchTerm.toLowerCase())
      ) || [];

    const handleRevokeAccount = async (accountEmail: string) => {
      try {
        console.log('Revoking account:', accountEmail);
        await revokeAccountGoogle(accountEmail);
        if (selectedAccount === accountEmail) {
          setSelectedAccount('');
          setSelectedFile('');

          form.setFieldValue('AccountEmail', '');
          form.setFieldValue('FileId', '');
          form.setFieldValue('FileName', '');
        }
        await refetchAccounts();
      } catch (error) {
        console.error('Error revoking account:', error);
      }
    };

    const handleConnect = () => {
      try {
        const url = knowledgeService.buildExternalDriveUrl(
          'GOOGLE_DRIVE',
          user?.companyId,
          user?.brandId
        );

        knowledgeService.openPopupAndMonitor(url, () => {
          refetchAccounts();
        });
      } catch (error) {
        console.error('Error during authentication:', error);
      }
    };

    return (
      <form
        className='mt-2 grid items-start gap-3'
        onSubmit={async (e) => {
          e.preventDefault();
          e.stopPropagation();
          await form.handleSubmit();
        }}
      >
        <div className='flex items-center justify-between'>
          <div className='text-sm font-medium'>
            {t('content_form.accounts')}
            <span className='ms-2 text-red-500'>*</span>
          </div>
          <Button onClick={handleConnect} className='rounded-2xl' type='button'>
            {t('content_form.connect')}
          </Button>
        </div>
        <form.AppField
          name='AccountEmail'
          listeners={{
            onChange: ({ value }) => {
              setSelectedAccount(value);
            },
          }}
        >
          {(field) => (
            <field.SelectField
              isLoading={isAccountLoading}
              placeholder={t('content_form.accounts_placeholder')}
              emptyDataMessage={t('content_form.no_accounts')}
              options={
                accounts?.map((account) => ({
                  label: account,
                  value: account,
                })) ?? []
              }
              showOptionDelete={true}
              onDeleteOption={handleRevokeAccount}
            />
          )}
        </form.AppField>
        {selectedAccount && (
          <>
            <form.AppField name='Title'>
              {(field) => (
                <field.TextField
                  label={t('content_form.title_field')}
                  placeholder={t('content_form.title_field')}
                />
              )}
            </form.AppField>

            <div className='w-full'>
              <form.AppField
                name='FileId'
                listeners={{
                  onChange: ({ value }) => {
                    form.setFieldValue('FileName', value);
                  },
                }}
              >
                {(field) => (
                  <field.RadioGroupField
                    label={t('content_form.select_file')}
                    itemOptionOrientation='vertical'
                    radioGroupClassName='grid-cols-6 gap-2 grid'
                    labelItemClassName='truncate items-end justify-center text-xs text-foreground'
                    radioItemClassName='relative h-28 flex-1 rounded-lg border border-slate-200'
                    showIndicatorIcon={false}
                    options={
                      filteredFiles.map((file) => ({
                        label: file.name,
                        value: file.id,
                        icon: <Icons.pdf className='text-primary' />,
                      })) || []
                    }
                  />
                )}
              </form.AppField>
            </div>
          </>
        )}
      </form>
    );
  },
});
