'use client';

import { useTranslations } from 'next-intl';
import React from 'react';

import {
  useCreateLogoKnowledge,
  useCreateColorKnowledge,
} from '@/api/hooks/knowledge/mutations';
import {
  createKnowledgeLogoSchema,
  createKnowledgeColorSchema,
} from '@/api/models/schemas/knowledge.schema';
import { useCurrentUserStore } from '@/stores/current-user-store';

import {
  logoKnowledgeFormOpts,
  colorKnowledgeFormOpts,
} from '@/config/knowledge/forms-config';
import { TMediaTab } from '@/config/knowledge/options-config';

import { useAppForm } from '@/components/shared/form';
import { Button } from '@/components/ui/button';
import {
  DialogTitle,
  DialogClose,
  DialogHeader,
  DialogFooter,
  DialogContent,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';

import { MediaColorForm } from './color-form';
import { MediaLogoForm } from './logo-form';

interface MediaFormProps {
  tabName: TMediaTab;
  setTabName: (value: TMediaTab) => Promise<URLSearchParams>;
}

export function MediaForm({ tabName, setTabName }: MediaFormProps) {
  const t = useTranslations();

  const { getUser } = useCurrentUserStore();
  const user = getUser();

  const { mutateAsync: createMediaLogo } = useCreateLogoKnowledge();
  const { mutateAsync: createMediaColor } = useCreateColorKnowledge();

  const logoForm = useAppForm({
    defaultValues: {
      ...logoKnowledgeFormOpts.defaultValues,
      knowledge: {
        ...logoKnowledgeFormOpts.defaultValues.knowledge,
        CompanyId: user?.companyId ?? '',
        UserEmail: user?.email ?? '',
        BrandId: user?.brandId ?? '',
      },
    },
    validators: {
      onChange: createKnowledgeLogoSchema(t),
    },
    onSubmit: async ({ value }) => {
      await createMediaLogo({
        ...value,
        knowledge: {
          ...value.knowledge,
          Logo: {
            Name: value.knowledge.Name,
          },
        },
      });
    },
  });

  const colorForm = useAppForm({
    defaultValues: {
      knowledge: {
        ...colorKnowledgeFormOpts.defaultValues.knowledge,
        CompanyId: user?.companyId ?? '',
        UserEmail: user?.email ?? '',
        BrandId: user?.brandId ?? '',
      },
    },
    validators: {
      onChange: createKnowledgeColorSchema(t),
    },
    onSubmit: async ({ value }) => {
      await createMediaColor(value);
    },
  });

  return (
    <DialogContent
      className='flex h-fit max-h-[90%] w-1/2 max-w-none flex-col overflow-hidden px-0'
      onOpenAutoFocus={(e) => e.preventDefault()}
      onCloseAutoFocus={async () => {
        await setTabName(null);
        logoForm.reset();
        colorForm.reset();
      }}
    >
      <DialogHeader className='px-6'>
        <DialogTitle className='text-base'>{t('media_form.title')}</DialogTitle>
      </DialogHeader>
      <div className='h-full space-y-4 overflow-y-auto px-6'>
        <Label className='text-md mb-2 block font-medium'>
          {t('media_form.type')}
        </Label>
        <RadioGroup
          className='grid grid-cols-2 gap-2'
          value={tabName ?? 'logo'}
          onValueChange={async (value) => {
            await setTabName(value as typeof tabName);
          }}
        >
          <div className='flex items-center justify-start gap-2 rounded-lg border border-primary px-2 py-4 rtl:flex-row-reverse'>
            <RadioGroupItem value='logo' id='logo' className='size-4' />
            <Label htmlFor='logo' className='text-xs text-foreground'>
              {t('media_form.logo')}
            </Label>
          </div>

          <div className='flex items-center justify-start gap-2 rounded-lg border px-1 py-1 rtl:flex-row-reverse'>
            <RadioGroupItem value='color' id='color' className='size-4' />
            <div className='flex flex-1 flex-col items-start rtl:items-end'>
              <Label htmlFor='color' className='text-xs text-foreground'>
                {t('media_form.color')}
              </Label>
            </div>
          </div>
        </RadioGroup>
        {tabName === 'logo' && <MediaLogoForm form={logoForm} />}
        {tabName === 'color' && <MediaColorForm form={colorForm} />}
      </div>
      <DialogFooter className='px-6'>
        <DialogClose asChild>
          <Button
            type='button'
            variant='outline'
            className='border border-[#f312612e] bg-[#f312612e] text-[#F31260] hover:bg-[#F312604D] hover:text-[#F31260]'
            onClick={async () => {
              await setTabName(null);
              logoForm.reset();
              colorForm.reset();
            }}
          >
            {t('cancel')}
          </Button>
        </DialogClose>
        {tabName === 'logo' ? (
          <logoForm.AppForm>
            <logoForm.SubmitButton
              onClick={async () => {
                await logoForm.handleSubmit();
              }}
            >
              {t('save')}
            </logoForm.SubmitButton>
          </logoForm.AppForm>
        ) : (
          <colorForm.AppForm>
            <colorForm.SubmitButton
              onClick={async () => {
                await colorForm.handleSubmit();
              }}
            >
              {t('save')}
            </colorForm.SubmitButton>
          </colorForm.AppForm>
        )}
      </DialogFooter>
    </DialogContent>
  );
}
