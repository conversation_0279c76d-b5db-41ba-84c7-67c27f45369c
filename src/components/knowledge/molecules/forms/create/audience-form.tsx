'use client';

import { useTranslations } from 'next-intl';
import React from 'react';

import { audienceKnowledgeFormOpts } from '@/config/knowledge/forms-config';
import { useKnowledgeOptionsConfig } from '@/config/knowledge/use-knowledge-options-config';

import { withForm } from '@/components/shared/form';
import { MultiSelect } from '@/components/shared/form/multi-select';
import { Icons } from '@/components/shared/icons';
import { Label } from '@/components/ui/label';

export const AudienceForm = withForm({
  ...audienceKnowledgeFormOpts,
  render: function Render({ form }) {
    const t = useTranslations();

    const { genderOptions, spendingBehaviorOptions } =
      useKnowledgeOptionsConfig();

    return (
      <form
        className='grid h-full gap-2 overflow-y-auto px-6'
        onSubmit={(e) => {
          e.preventDefault();
          form.handleSubmit();
        }}
      >
        <form.AppField
          name='knowledge.Audience.Name'
          listeners={{
            onChange: ({ value }) => {
              form.setFieldValue('knowledge.Name', value);
            },
          }}
        >
          {(field) => (
            <field.TextField
              label={t('audience_form.name')}
              placeholder={t('audience_form.name')}
            />
          )}
        </form.AppField>
        <form.AppField name='knowledge.Audience.AgeRange'>
          {(field) => (
            <field.DualSliderField
              label={t('audience_form.age_range')}
              showRangeOnLabel
            />
          )}
        </form.AppField>

        <form.AppField name='knowledge.Audience.Gender'>
          {(field) => (
            <field.RadioGroupField
              label={t('audience_form.gender')}
              radioGroupClassName='grid grid-cols-3 gap-2'
              radioItemClassName='relative flex cursor-pointer items-center justify-center rounded-lg bg-[#F4F4F5] px-2 py-5'
              options={genderOptions}
            />
          )}
        </form.AppField>
        <form.AppField name='knowledge.Audience.SpendingBehavior'>
          {(field) => (
            <div className='space-y-2'>
              <div className='flex flex-wrap gap-2'>
                <Label>{t('audience_form.spending_behavior')}</Label>
                <Icons.infoCircle />
              </div>
              <MultiSelect
                options={spendingBehaviorOptions}
                onValueChange={(selectedValues) => {
                  field.handleChange(selectedValues);
                }}
                searchText={t('search')}
                selectAllText={t(
                  'audience_form.spending_behavior_options.select_all'
                )}
                closeText={t('close')}
                animation={0.5}
                maxCount={3}
                className='my-custom-class'
              />
            </div>
          )}
        </form.AppField>

        <form.AppField name='knowledge.Audience.BusinessIndustry'>
          {(field) => (
            <field.TextFieldWithInnerTags
              label={t('audience_form.business_industry')}
              required={false}
              showSeparateAddButton
              separateAddButtonText={t('add')}
            />
          )}
        </form.AppField>

        <form.AppField name='knowledge.Audience.InterestAndPref'>
          {(field) => (
            <field.TextFieldWithInnerTags
              label={t('audience_form.interests_or_preferences')}
              required={false}
              showSeparateAddButton
              separateAddButtonText={t('add')}
            />
          )}
        </form.AppField>
      </form>
    );
  },
});
