'use client';

import { useTranslations } from 'next-intl';
import React from 'react';

import { colorKnowledgeFormOpts } from '@/config/knowledge/forms-config';

import ColorPalette from '@/components/shared/color-palette';
import { withForm } from '@/components/shared/form';

export const MediaColorForm = withForm({
  ...colorKnowledgeFormOpts,
  render: function Render({ form }) {
    const t = useTranslations();

    return (
      <form
        className='grid gap-3'
        onSubmit={async (e) => {
          e.preventDefault();
          e.stopPropagation();
          await form.handleSubmit();
        }}
      >
        <form.AppField name='knowledge.Name'>
          {(field) => (
            <field.TextField
              label={t('media_form.color_name')}
              placeholder={t('media_form.color_name')}
            />
          )}
        </form.AppField>
        <form.AppField name='knowledge.Color'>
          {(field) => {
            const colorValues = field.getValue?.() || [];
            const hexValues = colorValues.map(
              (color: { ColorHex?: string }) => color.ColorHex || ''
            );

            return (
              <ColorPalette
                value={hexValues}
                selectColorText={t('media_form.select_color')}
                onChange={(newHexValues) => {
                  const newValue = newHexValues.map((hex, index) => ({
                    Index: index,
                    ColorHex: hex,
                  }));
                  field.setValue(newValue);
                }}
              />
            );
          }}
        </form.AppField>
      </form>
    );
  },
});
