'use client';

import { useTranslations } from 'next-intl';
import { useQueryState, parseAsStringLiteral } from 'nuqs';
import { useEffect } from 'react';
import { toast } from 'sonner';

import { cn } from '@/lib/utils';

import {
  useCreateFileKnowledge,
  useCreateTextKnowledge,
  useCreateWebsiteKnowledge,
  usePrepareUrlContentKnowledge,
} from '@/api/hooks/knowledge/mutations';
import {
  CreateKnowledgeFileSchema,
  CreateKnowledgeTextSchema,
  createKnowledgeTextSchema,
  createKnowledgeFileSchema,
  CreateKnowledgeWebsiteSchema,
  createKnowledgeWebsiteSchema,
  knowledgeScrapingWebsiteSchema,
} from '@/api/models/schemas/knowledge.schema';
import { useCurrentUserStore } from '@/stores/current-user-store';
import { useKnowledgeScrapedWebsiteStore } from '@/stores/knowledge-scraped-website-store';
import { DialogClose } from '@radix-ui/react-dialog';

import {
  textKnowledgeFormOpts,
  fileKnowledgeFormOpts,
  websiteKnowledgeFormOpts,
  prepareUrlContentFormOpts,
} from '@/config/knowledge/forms-config';
import { TContentTab } from '@/config/knowledge/options-config';
import { useKnowledgeOptionsConfig } from '@/config/knowledge/use-knowledge-options-config';

import { useAppForm } from '@/components/shared/form';
import { Button } from '@/components/ui/button';
import {
  DialogTitle,
  DialogHeader,
  DialogFooter,
  DialogContent,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';

import { FormExternalDrive } from './external-drive-creation';
import { ContentFileForm } from './file-form';
import { ContentTextForm } from './text-form';
import { ContentUrlForm } from './url-form';

interface ContentFormProps {
  tabName: TContentTab;
  setTabName: (value: TContentTab) => Promise<URLSearchParams>;
}
export function ContentForm({ tabName, setTabName }: ContentFormProps) {
  const t = useTranslations();

  const { externalDriveTabs } = useKnowledgeOptionsConfig();

  const { getUser } = useCurrentUserStore();
  const user = getUser();

  const [activeExternalDriveTab, setActiveExternalDriveTab] = useQueryState(
    'tabdrive',
    parseAsStringLiteral(externalDriveTabs)
  );

  const toggleShowResults = useKnowledgeScrapedWebsiteStore(
    (state) => state.toggleShowResults
  );
  const websiteScrapedResults = useKnowledgeScrapedWebsiteStore(
    (state) => state.websiteScrapedResults
  );
  useEffect(() => {
    if (tabName === 'drive' && !activeExternalDriveTab) {
      setActiveExternalDriveTab('google-drive');
    }
  }, [tabName, activeExternalDriveTab, setActiveExternalDriveTab]);

  const { mutateAsync: createTextKnowledge } = useCreateTextKnowledge();
  const { mutateAsync: prepareUrlContentKnowledge } =
    usePrepareUrlContentKnowledge();
  const { mutateAsync: createWebsiteKnowledge } = useCreateWebsiteKnowledge();
  const { mutateAsync: createFileKnowledge } = useCreateFileKnowledge();

  const textForm = useAppForm({
    defaultValues: {
      knowledge: {
        ...textKnowledgeFormOpts.defaultValues.knowledge,
        CompanyId: user?.companyId ?? '',
        BrandId: user?.brandId ?? '',
        UserEmail: user?.email ?? '',
      },
    } as CreateKnowledgeTextSchema,
    validators: {
      onChange: createKnowledgeTextSchema(t),
    },
    onSubmit: async ({ value }) => {
      await createTextKnowledge(value);
    },
  });

  const prepareUrlContentForm = useAppForm({
    ...prepareUrlContentFormOpts,
    validators: {
      onChange: knowledgeScrapingWebsiteSchema(t),
    },
    onSubmit: async ({ value }) => {
      await prepareUrlContentKnowledge(value);
    },
  });

  const urlForm = useAppForm({
    defaultValues: {
      knowledge: {
        ...websiteKnowledgeFormOpts.defaultValues.knowledge,
        CompanyId: user?.companyId ?? '',
        BrandId: user?.brandId ?? '',
        UserEmail: user?.email ?? '',
        Name: websiteScrapedResults?.title,
        Website: {
          Title: websiteScrapedResults?.title,
          Link: websiteScrapedResults?.url,
          Description: websiteScrapedResults?.HtmlText,
        },
      },
    } as CreateKnowledgeWebsiteSchema,
    validators: {
      onChange: createKnowledgeWebsiteSchema(t),
    },
    onSubmit: async ({ value }) => {
      if (!websiteScrapedResults) {
        toast.error(t('toast.wait_scraping_data'));
        return;
      }

      await createWebsiteKnowledge({
        knowledge: {
          ...value.knowledge,
          Name: websiteScrapedResults.title,
          Website: {
            Title: websiteScrapedResults.title,
            Link: websiteScrapedResults.url,
            Description: websiteScrapedResults.HtmlText,
          },
        },
      });
    },
  });

  const fileForm = useAppForm({
    defaultValues: {
      knowledge: {
        ...fileKnowledgeFormOpts.defaultValues.knowledge,
        CompanyId: user?.companyId ?? '',
        BrandId: user?.brandId ?? '',
        UserEmail: user?.email ?? '',
      },
    } as CreateKnowledgeFileSchema,
    validators: {
      onChange: createKnowledgeFileSchema(t),
    },
    onSubmit: async ({ value }) => {
      await createFileKnowledge(value);
    },
  });

  return (
    <DialogContent
      className='flex h-fit max-h-[90%] w-1/2 max-w-none flex-col overflow-hidden px-0'
      onOpenAutoFocus={(e) => e.preventDefault()}
      onCloseAutoFocus={async () => {
        await setTabName(null);
        await setActiveExternalDriveTab(null);
        textForm.reset();
      }}
    >
      <DialogHeader className='space-y-0 px-6 py-0'>
        <DialogTitle className='text-base'>
          {t('content_form.title')}
        </DialogTitle>
      </DialogHeader>
      <div className='space-y-2 overflow-y-auto px-6'>
        <Label className='text-md font-medium'>{t('content_form.type')}</Label>
        <RadioGroup
          className='grid grid-cols-4 gap-2'
          value={tabName ?? 'text'}
          onValueChange={async (value) => {
            await setTabName(value as typeof tabName);
          }}
        >
          <div className='flex items-center justify-start gap-2 rounded-lg border border-primary px-2 py-4 rtl:flex-row-reverse'>
            <RadioGroupItem value='text' id='text' className='size-4' />
            <Label htmlFor='text' className='text-xs text-foreground'>
              {t('content_form.text')}
            </Label>
          </div>

          <div className='flex items-center justify-start gap-2 rounded-lg border border-primary px-2 py-4 rtl:flex-row-reverse'>
            <RadioGroupItem value='url' id='url' className='size-4' />
            <Label htmlFor='url' className='text-xs text-foreground'>
              {t('content_form.url')}
            </Label>
          </div>

          <div className='flex items-center justify-start gap-2 rounded-lg border border-primary px-2 py-4 rtl:flex-row-reverse'>
            <RadioGroupItem value='file' id='file' className='size-4' />
            <Label htmlFor='file' className='text-xs text-foreground'>
              {t('content_form.file')}
            </Label>
          </div>

          <div className='flex items-center justify-start gap-2 rounded-lg border border-primary px-2 py-4 rtl:flex-row-reverse'>
            <RadioGroupItem value='drive' id='drive' className='size-4' />
            <Label htmlFor='drive' className='text-xs text-foreground'>
              {t('content_form.external_drive')}
            </Label>
          </div>
        </RadioGroup>

        {tabName === 'text' && <ContentTextForm form={textForm} />}
        {tabName === 'url' && <ContentUrlForm form={prepareUrlContentForm} />}
        {tabName === 'file' && <ContentFileForm form={fileForm} />}
        {tabName === 'drive' && (
          <FormExternalDrive
            tabName={activeExternalDriveTab}
            setTabName={setActiveExternalDriveTab}
          />
        )}
      </div>

      <DialogFooter
        className={cn('flex w-full items-center px-6', {
          'sm:justify-between': tabName === 'url',
        })}
      >
        {tabName === 'url' && (
          <div className='flex items-center gap-2'>
            <prepareUrlContentForm.AppForm>
              <prepareUrlContentForm.SubmitButton
                onClick={async () => {
                  await prepareUrlContentForm.handleSubmit();
                }}
              >
                {t('search_data')}
              </prepareUrlContentForm.SubmitButton>
            </prepareUrlContentForm.AppForm>
            <Button
              type='button'
              variant='outline'
              disabled={!websiteScrapedResults?.HtmlText}
              onClick={toggleShowResults}
            >
              {t('preview_data')}
            </Button>
          </div>
        )}
        <div className='flex items-center gap-2'>
          <DialogClose asChild>
            <Button
              type='button'
              variant='outline'
              className='border border-[#f312612e] bg-[#f312612e] text-[#F31260] hover:bg-[#F312604D] hover:text-[#F31260]'
              onClick={async () => {
                await setTabName(null);
                await setActiveExternalDriveTab(null);
              }}
            >
              {t('cancel')}
            </Button>
          </DialogClose>

          {tabName === 'text' && (
            <textForm.AppForm>
              <textForm.SubmitButton
                onClick={async () => {
                  await textForm.handleSubmit();
                }}
              >
                {t('save')}
              </textForm.SubmitButton>
            </textForm.AppForm>
          )}
          {tabName === 'url' && (
            <urlForm.AppForm>
              <urlForm.SubmitButton
                disabled={!websiteScrapedResults?.HtmlText}
                onClick={async () => {
                  await urlForm.handleSubmit();
                }}
              >
                {t('save')}
              </urlForm.SubmitButton>
            </urlForm.AppForm>
          )}
          {tabName === 'file' && (
            <fileForm.AppForm>
              <fileForm.SubmitButton
                onClick={async () => {
                  await fileForm.handleSubmit();
                }}
              >
                {t('save')}
              </fileForm.SubmitButton>
            </fileForm.AppForm>
          )}
        </div>
      </DialogFooter>
    </DialogContent>
  );
}
