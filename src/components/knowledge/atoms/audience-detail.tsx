'use client';

import { useTranslations } from 'next-intl';

import { KnowledgeResponse } from '@/api/models/dtos/knowledge.dto';

import { useFormatDate } from '@/hooks/use-date-fns-locale';

import { Badge } from '@/components/ui/badge';

interface KnowledgeDetailProps {
  knowledge: KnowledgeResponse | null;
}

export function AudienceDetail({ knowledge }: KnowledgeDetailProps) {
  const { format } = useFormatDate();

  const t = useTranslations();
  if (!knowledge) {
    return <h1>{t('error_details')}</h1>;
  }

  return (
    <div>
      <div className='font-medium'>{knowledge.Audience.Name}</div>
      <div className='text-xs text-muted-foreground'>
        {format(new Date(knowledge.CreationTimestamp), 'PP')}
      </div>
      <div className='mt-6 font-medium'>{t('audience_form.age_range')}</div>
      <div className='mt-1 text-xs text-muted-foreground'>
        {knowledge.Audience.AgeMin} - {knowledge.Audience.AgeMax}
      </div>
      <div className='mt-6 font-medium'>{t('audience_form.gender')}</div>
      <div className='col-span-3 mt-1 gap-2'>
        <Badge className='rounded-xl bg-[#D7E4F8] text-[#000000]'>
          {knowledge.Audience.Gender}
        </Badge>
      </div>
      {(knowledge?.Audience?.BusinessIndustry?.length ?? 0) > 0 && (
        <>
          <div className='mt-6 font-medium'>
            {t('audience_form.business_industry')}
          </div>
          <div className='col-span-3 mt-1'>
            {knowledge.Audience?.BusinessIndustry?.map((item) => (
              <Badge
                key={item}
                className='me-2 rounded-xl bg-[#D7E4F8] text-[#000000]'
              >
                {item}
              </Badge>
            ))}
          </div>
        </>
      )}
      {(knowledge?.Audience?.InterestAndPref?.length ?? 0) > 0 && (
        <>
          <div className='mt-6 font-medium'>
            {t('audience_form.interests_or_preferences')}
          </div>
          <div className='col-span-3 mt-1'>
            {knowledge.Audience?.InterestAndPref?.map((item) => (
              <Badge
                key={item}
                className='me-2 rounded-xl bg-[#D7E4F8] text-[#000000]'
              >
                {item}
              </Badge>
            ))}
          </div>
        </>
      )}
      {(knowledge?.Audience?.SpendingBehavior?.length ?? 0) > 0 && (
        <>
          <div className='mt-6 font-medium'>
            {t('audience_form.spending_behavior')}
          </div>
          <div className='col-span-3 mt-1'>
            {knowledge.Audience?.SpendingBehavior?.map((item) => (
              <Badge
                key={item}
                className='me-2 mt-2 rounded-xl bg-[#D7E4F8] text-[#000000]'
              >
                {item}
              </Badge>
            ))}
          </div>
        </>
      )}
    </div>
  );
}
