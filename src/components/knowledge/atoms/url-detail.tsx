'use client';

import { useTranslations } from 'next-intl';

import { KnowledgeResponse } from '@/api/models/dtos/knowledge.dto';

import { useFormatDate } from '@/hooks/use-date-fns-locale';

interface KnowledgeDetailProps {
  knowledge: KnowledgeResponse | null;
}
export function UrlDetail({ knowledge }: KnowledgeDetailProps) {
  const { format } = useFormatDate();

  const t = useTranslations();
  if (!knowledge) {
    return <h1>{t('error_details')}</h1>;
  }
  return (
    <div className='flex flex-1 flex-col overflow-hidden'>
      <div className='font-medium'>{knowledge.Website.Title}</div>

      <div className='grid grid-cols-2'>
        <div className='text-sm text-muted-foreground'>
          {format(new Date(knowledge.CreationTimestamp), 'PP')}
        </div>

        <div className='flex items-center'>
          <div className='font-medium'>{t('url')}:</div>
          <div className='text-sm text-muted-foreground'>
            {knowledge.Website.Link}
          </div>
        </div>
      </div>

      <div className='font-medium'>{t('type_knowledge.content')}</div>

      <div className='flex-1 overflow-y-auto text-sm text-muted-foreground'>
        {knowledge.Website.Description}
      </div>
    </div>
  );
}
