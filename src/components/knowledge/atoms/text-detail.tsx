'use client';

import { useTranslations } from 'next-intl';

import { KnowledgeResponse } from '@/api/models/dtos/knowledge.dto';

import { useFormatDate } from '@/hooks/use-date-fns-locale';

import { Badge } from '@/components/ui/badge';

interface KnowledgeDetailProps {
  knowledge: KnowledgeResponse | null;
}

export function TextDetail({ knowledge }: KnowledgeDetailProps) {
  const { format } = useFormatDate();

  const t = useTranslations();
  if (!knowledge) {
    return <h1>{t('error_details')}</h1>;
  }

  return (
    <div>
      <div className='font-medium'>{knowledge.Text.Title}</div>
      <div className='text-sm text-muted-foreground'>
        {format(new Date(knowledge.CreationTimestamp), 'PP')}
      </div>

      <div className='mt-6 font-medium'>{t('description')}</div>
      <div className='mt-1 text-sm text-muted-foreground'>
        {knowledge.Text.Description}
      </div>

      {knowledge.Text?.Labels?.length ? (
        <>
          <div className='mt-6 font-medium'>{t('tags')}</div>
          <div className='mt-1 flex flex-wrap gap-2'>
            {knowledge.Text.Labels.map((item) => (
              <Badge
                key={item}
                className='rounded-xl bg-primary text-sm font-light text-white'
              >
                {item}
              </Badge>
            ))}
          </div>
        </>
      ) : (
        <div className='mt-6 text-sm text-red-500'>{t('no_labels')}</div>
      )}
    </div>
  );
}
