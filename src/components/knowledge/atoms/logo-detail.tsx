'use client';
import { useTranslations } from 'next-intl';
import Image from 'next/image';

import { KnowledgeResponse } from '@/api/models/dtos/knowledge.dto';

import { useFormatDate } from '@/hooks/use-date-fns-locale';

interface KnowledgeDetailProps {
  knowledge: KnowledgeResponse | null;
}
export function LogoDetail({ knowledge }: KnowledgeDetailProps) {
  const { format } = useFormatDate();

  const t = useTranslations();
  if (!knowledge) {
    return <h1>{t('error_details')}</h1>;
  }

  return (
    <div>
      <div className='font-medium'>{knowledge.Logo.Name}</div>
      <div className='text-sm text-muted-foreground'>
        {format(new Date(knowledge.CreationTimestamp), 'PP')}
      </div>
      <Image
        className='mt-4 rounded-xl'
        src={knowledge.Logo.GcsLinkPublic}
        alt='Image'
        width={200}
        height={200}
      />
    </div>
  );
}
