import { useTranslations } from 'next-intl';

import { KnowledgeResponse } from '@/api/models/dtos/knowledge.dto';

import { useKnowledgeUpdateForms } from '@/providers/knowledge-update-forms-provider';
import { useKnowledgeStore } from '@/stores/use-knowledge-store';

import { AudienceDetail } from '@/components/knowledge/atoms/audience-detail';
import { ColorDetail } from '@/components/knowledge/atoms/color-detail';
import { FileDetail } from '@/components/knowledge/atoms/file-detail';
import { LogoDetail } from '@/components/knowledge/atoms/logo-detail';
import { TextDetail } from '@/components/knowledge/atoms/text-detail';
import { UrlDetail } from '@/components/knowledge/atoms/url-detail';

interface KnowledgeSheetDetailsProps {
  type?: KnowledgeResponse['Type'];
}

export function KnowledgeSheetDetails({ type }: KnowledgeSheetDetailsProps) {
  const t = useTranslations();

  const { knowledge, isUpdating } = useKnowledgeStore();
  const { renderRelatedUpdateForm } = useKnowledgeUpdateForms();

  switch (isUpdating) {
    case false:
      switch (type) {
        case 'text_plain':
          return <TextDetail knowledge={knowledge} />;
        case 'text_doc':
          return <FileDetail knowledge={knowledge} />;
        case 'text_website':
          return <UrlDetail knowledge={knowledge} />;
        case 'audience':
          return <AudienceDetail knowledge={knowledge} />;
        case 'media_logo':
          return <LogoDetail knowledge={knowledge} />;
        case 'media_color':
          return <ColorDetail knowledge={knowledge} />;
        default:
          return <h1>{t('error_details_content')}</h1>;
      }

    case true:
      return renderRelatedUpdateForm();
  }
}
