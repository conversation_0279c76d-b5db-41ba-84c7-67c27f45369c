'use client';

import { useTranslations } from 'next-intl';

import { KnowledgeResponse } from '@/api/models/dtos/knowledge.dto';

import { useFormatDate } from '@/hooks/use-date-fns-locale';

import { Badge } from '@/components/ui/badge';
import {
  Tooltip,
  TooltipTrigger,
  TooltipProvider,
} from '@/components/ui/tooltip';

interface KnowledgeDetailProps {
  knowledge: KnowledgeResponse | null;
}
export function ColorDetail({ knowledge }: KnowledgeDetailProps) {
  const { format } = useFormatDate();
  const t = useTranslations();

  if (!knowledge) {
    return <h1>{t('error_details')}</h1>;
  }

  return (
    <>
      <div className='mt-6 font-medium'>{knowledge.Name}</div>
      <div className='mt-1 text-sm text-muted-foreground'>
        {format(new Date(knowledge.CreationTimestamp), 'PP')}
      </div>
      <div className='mt-6 grid grid-cols-2 gap-4'>
        {knowledge.Color?.map((color) => (
          <ColorSwatch key={color.Index} color={color.ColorHex} />
        ))}
      </div>
    </>
  );
}

interface ColorSwatchProps {
  color: string;
}

export function ColorSwatch({ color }: ColorSwatchProps) {
  const copyToClipboard = () => {
    navigator.clipboard.writeText(color);
  };

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Badge
            variant='secondary'
            className='flex cursor-pointer items-center gap-2 rounded-2xl bg-[#F4F4F5] px-3 py-1.5 hover:bg-secondary/80'
            onClick={copyToClipboard}
          >
            <div
              className='h-7 w-7 rounded-full border border-border'
              style={{ backgroundColor: color }}
            />
            <span>{color}</span>
          </Badge>
        </TooltipTrigger>
      </Tooltip>
    </TooltipProvider>
  );
}
