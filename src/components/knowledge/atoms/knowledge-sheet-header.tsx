import { useTranslations } from 'next-intl';
import React from 'react';

import { ChevronLeft, ChevronRight } from 'lucide-react';

import {
  useDeleteKnowledge,
  useMarkKnowledgeAsFavorite,
} from '@/api/hooks/knowledge/mutations';
import { useKnowledgeUpdateForms } from '@/providers/knowledge-update-forms-provider';
import { useKnowledgeStore } from '@/stores/use-knowledge-store';

import { Icons } from '@/components/shared/icons';
import { Button } from '@/components/ui/button';
import {
  SheetTitle,
  SheetHeader,
  SheetDescription,
} from '@/components/ui/sheet';

export function KnowledgeSheetHeader() {
  const t = useTranslations();

  const { isUpdating, setIsUpdating, setDetailOpen, knowledge } =
    useKnowledgeStore();
  const { handleFormUpdateAction } = useKnowledgeUpdateForms();

  const { mutateAsync: handleStarClick } = useMarkKnowledgeAsFavorite();
  const { mutateAsync: handleDeleteClick } = useDeleteKnowledge();

  switch (isUpdating) {
    case false:
      return (
        <SheetHeader className='flex-row items-center justify-between space-y-0 border-b border-b-gray-200 px-4 py-2'>
          <SheetTitle className='flex items-center justify-center gap-2 ps-3'>
            <Button
              variant='outline'
              size='sm'
              className='flex items-center gap-1 border-blue-600 bg-white font-normal text-blue-600'
              iconStart={<Icons.penEdit />}
              onClick={() => {
                setIsUpdating(true);
              }}
            >
              {t('update')}
            </Button>

            <Button
              size='setting'
              className='bg-[#FEFCE8] px-5 text-[#F5A524] hover:bg-[#F5A524] hover:text-white'
              onClick={async () => await handleStarClick(knowledge?.Id)}
            >
              <Icons.star />
            </Button>
            <Button
              size='icon'
              className='size-8 bg-[#********] text-[#F31260] hover:bg-[#F31260]/70 hover:text-background'
              onClick={async () => await handleDeleteClick(knowledge?.Id)}
            >
              <Icons.trash />
            </Button>
          </SheetTitle>
          <SheetDescription className='sr-only'>
            {t('knowledge_details')}
          </SheetDescription>

          <div className='flex items-center gap-1'>
            <Button
              variant='outline'
              size='setting'
              className='border-[1.5px] border-[#71717A] bg-[#F4F4F5] text-[#71717A]'
            >
              <ChevronLeft />
            </Button>
            <Button
              variant='outline'
              size='setting'
              className='border-[1.5px] border-[#71717A] bg-[#F4F4F5] text-[#71717A]'
            >
              <ChevronRight />
            </Button>
          </div>
        </SheetHeader>
      );

    case true:
      return (
        <SheetHeader className='space-y-0 border-b border-b-gray-200 px-4 py-2'>
          <SheetTitle className='flex items-center justify-end gap-2'>
            <Button
              variant='outline'
              className='bg-[#********] text-[#F31260] hover:bg-[#F31260]/70 hover:text-background'
              onClick={() => setIsUpdating(false)}
            >
              {t('cancel')}
            </Button>

            <Button
              onClick={async () => {
                await handleFormUpdateAction();
                setDetailOpen(false);
              }}
            >
              {t('save')}
            </Button>
          </SheetTitle>
          <SheetDescription className='sr-only'>
            {t('knowledge_details')}
          </SheetDescription>
        </SheetHeader>
      );
  }
}
