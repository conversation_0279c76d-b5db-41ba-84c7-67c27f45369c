'use client';

import { useTranslations } from 'next-intl';

import { KnowledgeResponse } from '@/api/models/dtos/knowledge.dto';

import { useFormatDate } from '@/hooks/use-date-fns-locale';

interface KnowledgeDetailProps {
  knowledge: KnowledgeResponse | null;
}

export function FileDetail({ knowledge }: KnowledgeDetailProps) {
  const { format } = useFormatDate();

  const t = useTranslations();
  if (!knowledge) {
    return <h1>{t('error_details')}</h1>;
  }

  return (
    <div>
      <div className='font-medium'>{knowledge.Doc.Title}</div>
      <div className='text-sm text-muted-foreground'>
        {format(new Date(knowledge.CreationTimestamp), 'PP')}
      </div>

      <div className='mt-6 flex items-center'>
        <div className='font-medium'>{t('content_form.type')}:</div>
        <div className='ms-2 text-sm text-muted-foreground'>
          {knowledge.Doc.Type}
        </div>
      </div>
      <div className='mt-6 font-medium'>{t('type_knowledge.content')}</div>
      <div className='mt-1 text-sm text-muted-foreground'>
        {knowledge.Doc.Description}
      </div>
    </div>
  );
}
