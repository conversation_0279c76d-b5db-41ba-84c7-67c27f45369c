import { useTranslations } from 'next-intl';

import { KnowledgeResponse } from '@/api/models/dtos/knowledge.dto';

interface KnowledgeTitleDetailsPorps {
  type?: KnowledgeResponse['Type'];
}

export function KnowledgeSheetTitle({ type }: KnowledgeTitleDetailsPorps) {
  const t = useTranslations();

  switch (type) {
    case 'text_plain':
      return (
        <h1 className='text-[25px] font-semibold leading-normal'>
          {t('detail_title.text')}
        </h1>
      );
    case 'text_doc':
      return (
        <h1 className='text-[25px] font-semibold leading-normal'>
          {t('detail_title.file')}
        </h1>
      );
    case 'text_website':
      return (
        <h1 className='text-[25px] font-semibold leading-normal'>
          {t('detail_title.website')}
        </h1>
      );
    case 'audience':
      return (
        <h1 className='text-[25px] font-semibold leading-normal'>
          {t('detail_title.audience')}
        </h1>
      );
    case 'media_logo':
      return (
        <h1 className='text-[25px] font-semibold leading-normal'>
          {t('detail_title.logo')}
        </h1>
      );
    case 'media_color':
      return (
        <h1 className='text-[25px] font-semibold leading-normal'>
          {t('detail_title.color')}
        </h1>
      );

    default:
      return <h1>{t('error_title_details')}</h1>;
  }
}
