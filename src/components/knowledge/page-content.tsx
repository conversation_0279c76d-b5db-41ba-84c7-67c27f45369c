'use client';
import { useTranslations } from 'next-intl';
import { useQueryState, parseAsStringLiteral } from 'nuqs';

import { useAllKnowledge } from '@/api/hooks/knowledge/queries';
import { useCurrentUserStore } from '@/stores/current-user-store';

import { useKnowledgeOptionsConfig } from '@/config/knowledge/use-knowledge-options-config';
import { useKnowledgeColumns } from '@/config/knowledge/use-knowledge-table-columns';
import { useKnowledgeTableOptionsConfig } from '@/config/knowledge/use-knowledge-table-options-config';

import { AudienceCreationForm } from '@/components/knowledge/molecules/forms/create/audience-creation';
import { ContentForm } from '@/components/knowledge/molecules/forms/create/content-creation';
import { MediaForm } from '@/components/knowledge/molecules/forms/create/media-creation';
import { GridViewMode } from '@/components/knowledge/molecules/grid-view-mode';
import { KnowledgeDetail } from '@/components/knowledge/molecules/knowledge-detail';
import { DataTable } from '@/components/shared/table/data-table';
import { Button } from '@/components/ui/button';
import { Dialog, DialogTrigger } from '@/components/ui/dialog';

import { Icons } from '../shared/icons';

export default function PageContent() {
  const t = useTranslations();
  const { contentTabs, mediaTabs } = useKnowledgeOptionsConfig();
  const knowledgeColumnsDef = useKnowledgeColumns();
  const { getUser } = useCurrentUserStore();
  const currentUser = getUser();

  const { searchConfig, filterConfig } = useKnowledgeTableOptionsConfig();

  const [activeContentTab, setActiveContentTab] = useQueryState(
    'tab',
    parseAsStringLiteral(contentTabs)
  );

  const [activeMediaTab, setActiveMediaTab] = useQueryState(
    'tab',
    parseAsStringLiteral(mediaTabs)
  );

  const {
    data: allKnowledge,
    isFetching,
    isError,
    error,
    refetch,
  } = useAllKnowledge(currentUser?.companyId, currentUser?.brandId);

  return (
    <div className='flex h-full flex-col'>
      <div className='flex items-center justify-between'>
        <h1 className='text-2xl font-bold'>{t('content.title')}</h1>

        <div className='flex gap-2'>
          <Dialog>
            <DialogTrigger asChild>
              <Button
                onClick={() => {
                  setActiveContentTab('text');
                }}
                className='bg-blue-900 text-white hover:bg-blue-900'
              >
                {t('content.new_content')}
                <Icons.plus />
              </Button>
            </DialogTrigger>
            <ContentForm
              tabName={activeContentTab}
              setTabName={setActiveContentTab}
            />
          </Dialog>

          <Dialog>
            <DialogTrigger asChild>
              <Button
                onClick={() => {
                  setActiveMediaTab('logo');
                }}
                className='bg-[#E7D2CF] text-blue-950 hover:bg-[#E7D2CF]'
              >
                {t('content.new_media')}
                <Icons.plus />
              </Button>
            </DialogTrigger>
            <MediaForm
              tabName={activeMediaTab}
              setTabName={setActiveMediaTab}
            />
          </Dialog>
          <Dialog>
            <DialogTrigger asChild>
              <Button className='bg-[#C48E86] text-blue-950 hover:bg-[#C48E86]'>
                {t('content.new_audience')}
                <Icons.plus />
              </Button>
            </DialogTrigger>
            <AudienceCreationForm />
          </Dialog>
        </div>
      </div>

      <p className='text-gray-0 my-2'>{t('content.subtitle')}</p>

      <DataTable
        columns={knowledgeColumnsDef}
        data={allKnowledge || []}
        isLoading={isFetching}
        isError={isError}
        error={error}
        refetch={refetch}
        gridViewPageSize={12}
        listViewPageSize={10}
        gridView={(knowledge) => <GridViewMode knowledge={knowledge} />}
        initialViewMode='list'
        searchConfig={searchConfig}
        filterConfig={filterConfig}
      />

      <KnowledgeDetail />
    </div>
  );
}
