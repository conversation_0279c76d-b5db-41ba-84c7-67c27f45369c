'use client';

import { useTranslations } from 'next-intl';
import { toast } from 'sonner';

import { cn } from '@/lib/utils';

import {
  useCreateCampaign,
  useGenerateAICampaignProposals,
} from '@/api/hooks/campaign/mutation';
import { createCampaignSchema } from '@/api/models/schemas/campaign.schema';
import { useRouter } from '@/i18n/routing';
import { useCampaignStore } from '@/stores/campaign-store';
import { useCurrentUserStore } from '@/stores/current-user-store';

import { simpleCreateCampaignFormOpts } from '@/config/campaigns/form.config';

import { AICampaignSuggestions } from '@/components/campaigns/molecules/ai-campaign-suggestions';
import { CampaignProposalPlaceholderCard } from '@/components/campaigns/molecules/campaign-proposal-placeholder-card';
import { NewAiCampaignCard } from '@/components/campaigns/molecules/new-ai-campaign-card';
import { NewCampaignCard } from '@/components/campaigns/molecules/new-campaign-card';
import { NewCampaignProposalsCard } from '@/components/campaigns/molecules/new-campaign-proposals-card';
import { useAppForm } from '@/components/shared/form';
import { Card, CardContent } from '@/components/ui/card';

export default function CreatePageContent() {
  const t = useTranslations();
  const router = useRouter();

  const user = useCurrentUserStore().getUser();
  const {
    isAICampaignCardOpen,
    isAICampaignProposalsSuccess,
    aiCampaignResponseData,
    setAiCampaignResponseData,
    setIsAICampaignProposalsSuccess,
  } = useCampaignStore();

  const { mutateAsync: createCustomCampaign } = useCreateCampaign();
  const { mutateAsync: generateAICampaignProposals } =
    useGenerateAICampaignProposals();

  const createCampaignForm = useAppForm({
    ...simpleCreateCampaignFormOpts,
    defaultValues: {
      ...simpleCreateCampaignFormOpts.defaultValues,
      CompanyId: user?.companyId ?? '',
      BrandId: user?.brandId ?? '',
      UserEmail: user?.email ?? '',
    },
    validators: {
      onChange: createCampaignSchema(),
    },
    onSubmit: async ({ value }) => {
      if (aiCampaignResponseData && aiCampaignResponseData.length > 0) {
        await createCustomCampaign({
          ...value,
          Proposal: aiCampaignResponseData,
        });
        createCampaignForm.reset();
        router.back();
        setAiCampaignResponseData(undefined);
        setIsAICampaignProposalsSuccess(false);
        return;
      }

      if (isAICampaignCardOpen) {
        await generateAICampaignProposals(value);
        return;
      }

      await createCustomCampaign(value);
      createCampaignForm.reset();
      router.back();
    },
    onSubmitInvalid: () => {
      toast.error(t('form.invalid_form'));
    },
  });

  return (
    <div className='flex h-full'>
      <div className='flex-1'>
        <main className='flex h-full flex-1 flex-col gap-4 overflow-auto px-4'>
          <div
            className={cn(
              'grid h-full grid-cols-[400px_380px_1fr] gap-4 overflow-hidden py-1',
              {
                'grid-cols-[50px_50px_1fr_1fr_1fr_1fr]':
                  isAICampaignProposalsSuccess,
              }
            )}
          >
            <Card className='flex h-full flex-col overflow-hidden'>
              {!isAICampaignProposalsSuccess ? (
                <NewCampaignCard form={createCampaignForm} />
              ) : (
                <div
                  className='grid h-full rotate-180 place-items-center font-semibold text-[#162F58]'
                  style={{ writingMode: 'vertical-lr' }}
                >
                  Create New Campaign
                </div>
              )}
            </Card>

            <Card
              className={cn('col-span-1 flex h-full flex-col overflow-hidden', {
                hidden: !isAICampaignCardOpen && !isAICampaignProposalsSuccess,
              })}
            >
              {!isAICampaignProposalsSuccess ? (
                <NewAiCampaignCard form={createCampaignForm} />
              ) : (
                <div
                  className='grid h-full rotate-180 place-items-center font-semibold text-[#3776DC]'
                  style={{ writingMode: 'vertical-lr' }}
                >
                  AI Assisted
                </div>
              )}
            </Card>

            <Card
              className={cn('flex h-full flex-col overflow-hidden', {
                hidden: !isAICampaignProposalsSuccess,
              })}
            >
              <NewCampaignProposalsCard form={createCampaignForm} />
            </Card>

            <Card
              className={cn('relative flex h-full flex-col overflow-hidden', {
                'col-span-1':
                  isAICampaignCardOpen && !isAICampaignProposalsSuccess,
                'col-span-2':
                  !isAICampaignCardOpen && !isAICampaignProposalsSuccess,
                'col-span-3': isAICampaignProposalsSuccess,
              })}
            >
              {!isAICampaignProposalsSuccess ? (
                <CampaignProposalPlaceholderCard />
              ) : (
                <CardContent className='grid h-full grid-cols-1 gap-2 overflow-hidden bg-[#F5F5F5] p-4 lg:grid-cols-3'>
                  <AICampaignSuggestions
                    campaignsSuggestions={aiCampaignResponseData ?? []}
                  />
                </CardContent>
              )}
            </Card>
          </div>
        </main>
      </div>
    </div>
  );
}
