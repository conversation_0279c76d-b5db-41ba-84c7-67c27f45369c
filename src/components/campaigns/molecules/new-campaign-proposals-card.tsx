import { useCampaignStore } from '@/stores/campaign-store';

import { simpleCreateCampaignFormOpts } from '@/config/campaigns/form.config';

import { SuggestionCard } from '@/components/campaigns/molecules/ai-campaign-suggestions';
import { withForm } from '@/components/shared/form';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>ooter,
  CardHeader,
  CardContent,
} from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';

export const NewCampaignProposalsCard = withForm({
  ...simpleCreateCampaignFormOpts,

  render: function Render({ form }) {
    const { aiCampaignProposals } = useCampaignStore();

    // Hide the card if no proposals are selected
    if (!aiCampaignProposals || aiCampaignProposals.length === 0) {
      return null;
    }

    return (
      <>
        <CardHeader className='px-2 py-3'>
          <CardTitle className='text-xs text-[#0C2144]'>
            Customize your own campaign by choosing from the ones generated
          </CardTitle>
        </CardHeader>

        <ScrollArea className='h-full'>
          <CardContent className='h-full flex-1 overflow-hidden px-2'>
            <div className='space-y-2'>
              {aiCampaignProposals.map((proposal, index) => (
                <SuggestionCard
                  key={index}
                  suggestion={proposal}
                  hideChooseButton
                />
              ))}
            </div>
          </CardContent>
        </ScrollArea>

        <CardFooter className='grid gap-4 border-t border-border bg-muted/70 px-5 py-4'>
          <form.AppForm>
            <form.SubmitButton onClick={form.handleSubmit}>
              Create custom campaign
            </form.SubmitButton>
          </form.AppForm>
        </CardFooter>
      </>
    );
  },
});
