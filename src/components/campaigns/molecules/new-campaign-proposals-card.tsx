import { useCampaignStore } from '@/stores/campaign-store';

import { simpleCreateCampaignFormOpts } from '@/config/campaigns/form.config';

import { ProposalSuggestionCard } from '@/components/campaigns/molecules/proposal-suggestion-card';
import { withForm } from '@/components/shared/form';
import {
  Card<PERSON>itle,
  Card<PERSON>ooter,
  CardHeader,
  CardContent,
} from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';

export const NewCampaignProposalsCard = withForm({
  ...simpleCreateCampaignFormOpts,

  render: function Render({ form }) {
    const { aiCampaignProposals } = useCampaignStore();

    return (
      <>
        <CardHeader className='px-2 py-3'>
          <CardTitle className='text-xs text-[#0C2144]'>
            Customize your own campaign by choosing from the ones generated
          </CardTitle>
        </CardHeader>

        <ScrollArea className='h-full'>
          <CardContent className='h-full flex-1 overflow-hidden px-2'>
            {aiCampaignProposals && aiCampaignProposals.length > 0 && (
              <div className='space-y-2'>
                {aiCampaignProposals.map((proposal, index) => (
                  <ProposalSuggestionCard
                    key={index}
                    suggestion={proposal}
                    hideChooseButton
                  />
                ))}
              </div>
            )}
          </CardContent>
        </ScrollArea>

        <CardFooter className='grid gap-4 border-t border-border bg-muted/70 px-5 py-4'>
          <form.AppForm>
            <form.SubmitButton onClick={form.handleSubmit}>
              Create custom campaign
            </form.SubmitButton>
          </form.AppForm>
        </CardFooter>
      </>
    );
  },
});
