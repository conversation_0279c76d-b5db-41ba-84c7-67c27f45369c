'use client';

import React from 'react';

import { CampaignResponse } from '@/api/models/dtos/compaign.dto';

import { cn } from '@/lib/utils';

import { useFormatDate } from '@/hooks/use-date-fns-locale';

import { StatusBadge } from '@/components/campaigns/atoms/status-badge';
import { Icons } from '@/components/shared/icons';
import {
  Card,
  CardTitle,
  CardHeader,
  CardFooter,
  CardContent,
} from '@/components/ui/card';

interface GridViewModeProps {
  campaign: CampaignResponse;
}

export function GridViewMode({ campaign }: GridViewModeProps) {
  const { format } = useFormatDate();

  return (
    <Card className='flex h-full flex-col'>
      <CardHeader className='flex-row items-center gap-2 space-y-0 px-3 py-2'>
        <div
          className={cn('h-8 w-2 rounded-full', {
            border: !campaign.Color,
          })}
          style={{ backgroundColor: campaign.Color ?? 'transparent' }}
        />
        <CardTitle className='text-sm font-semibold capitalize text-gray-900'>
          <p>{campaign.Name}</p>
        </CardTitle>
      </CardHeader>

      <CardContent className='flex h-full flex-1 flex-col gap-2 px-3 py-0'>
        <div>
          <StatusBadge status={campaign.Status} />
        </div>

        <div className='flex items-center gap-1.5 text-[0.875rem] text-[#71717A] opacity-50'>
          <div>{format(campaign.StartDate, 'PPP')}</div>
          <div>-</div>
          <div>{format(campaign.EndDate, 'PPP')}</div>
        </div>

        <div className='flex items-center gap-1.5 text-[0.75rem]'>
          <div className='font-bold text-[#11181C] opacity-60'>Objective :</div>
          <div className='text-[#979797]'>{campaign.Objective}</div>
        </div>

        <div className='flex items-center gap-1.5 text-[0.75rem]'>
          <div className='font-bold text-[#11181C] opacity-60'>
            Created by :
          </div>
          <div className='text-[#979797]'>{campaign.UserEmail}</div>
        </div>

        <p className='line-clamp-3 flex-1 text-[0.75rem] text-[#27272A]'>
          {campaign.Description}
        </p>

        <div className='grid grid-cols-3 gap-1'>
          <div className='flex flex-col justify-center gap-1 rounded-xl border border-gray-100 px-2 py-1.5'>
            <div className='flex items-center gap-1'>
              <Icons.eye className='size-5 rounded-full border border-[#FDEDD3] bg-[#FEFCE8] p-1 text-[#F5A524]' />
              <span className='text-[10px] font-medium text-[#71717A]'>
                Views
              </span>
            </div>

            <div className='flex items-center justify-between'>
              <div className='font-semibold text-[#3F3F46]'>
                {campaign.ViewsStat.Total}
              </div>
              <div className='flex items-center gap-1 rounded-md bg-[#F5A52433] px-1.5 py-0.5 text-xs text-[#F5A524]'>
                <Icons.arrowRight className='size-3 text-[#F9C97C]' />
                {campaign.ViewsStat.Trend.toFixed(1)}%
              </div>
            </div>
          </div>

          <div className='flex flex-col justify-center gap-1 rounded-xl border border-gray-100 px-2 py-1.5'>
            <div className='flex items-center gap-1'>
              <Icons.globalScale className='size-5 rounded-full border border-[#E9D5EF] bg-[#F4EAF7] p-0.5 text-[#A87DB6]' />
              <span className='text-[10px] font-medium text-[#71717A]'>
                Reach
              </span>
            </div>

            <div className='flex items-center justify-between'>
              <div className='font-semibold text-[#3F3F46]'>
                {campaign.ReachStat.Total}
              </div>
              <div className='flex items-center gap-1 rounded-md bg-[#E9D5EF] px-1.5 py-0.5 text-xs text-[#A87DB6]'>
                <Icons.arrowRight className='size-3 text-[#A87DB6]' />
                {campaign.ReachStat.Trend.toFixed(1)}%
              </div>
            </div>
          </div>

          <div className='flex flex-col justify-center gap-1 rounded-xl border border-gray-100 px-2 py-1.5'>
            <div className='flex items-center gap-1'>
              <Icons.heart className='size-5 rounded-full border border-[#FAA0BF] bg-[#FEE7EF] p-1 text-[#F31260]' />
              <span className='text-[10px] font-medium text-[#71717A]'>
                Interactions
              </span>
            </div>

            <div className='flex items-center justify-between'>
              <div className='font-semibold text-[#3F3F46]'>
                {campaign.InteractionStat.Total}
              </div>
              <div className='flex items-center gap-1 rounded-md bg-[#FEE7EF] px-1.5 py-0.5 text-xs text-[#F31260]'>
                <Icons.arrowRight className='size-3 text-[#F871A0]' />
                {campaign.InteractionStat.Trend.toFixed(1)}%
              </div>
            </div>
          </div>
        </div>
      </CardContent>

      <CardFooter className='justify-between px-3 py-2'>
        <div className='flex items-center'>
          <Icons.facebookOfficial className='size-4' />
          <Icons.instagramOfficial className='size-4' />
          <Icons.linkedinOfficial className='size-4' />
          <Icons.x className='size-4' />
        </div>

        <Icons.trash className='size-6 cursor-pointer rounded-full p-1 text-rose transition-all hover:bg-rose hover:text-background' />
      </CardFooter>
    </Card>
  );
}
