import { AICampaignProposal } from '@/api/models/dtos/compaign.dto';

import { cn } from '@/lib/utils';

import { useFormatDate } from '@/hooks/use-date-fns-locale';

import { useCampaignStore } from '@/stores/campaign-store';

import { Icons } from '@/components/shared/icons';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

interface ProposalSuggestionCardProps {
  suggestion: AICampaignProposal;
  hideChooseButton?: boolean;
}

export function ProposalSuggestionCard({
  suggestion,
  hideChooseButton = false,
}: ProposalSuggestionCardProps) {
  const { format } = useFormatDate();
  const { selectProposal, unselectProposal, removeProposalFromSuggestions } =
    useCampaignStore();

  return (
    <Card>
      <CardContent className='flex flex-col gap-2 p-2'>
        <div className='flex items-center gap-2'>
          <h3 className='text-xs font-bold text-[#52525B]'>Context :</h3>
          <h4 className='text-[0.688rem] font-semibold text-[#27272A]'>
            {suggestion.Name}
          </h4>
        </div>

        <p className='text-xs text-[#3F3F46]'>
          {format(suggestion.DateTime, 'dd/MM/yyyy')}
        </p>

        <div className='grid gap-0.5 text-xs'>
          <p className='font-bold text-[#11181C]'>Project :</p>
          <p className='text-[#27272A]'>{suggestion.Description}</p>
        </div>

        <div className='flex items-center justify-between'>
          <Icons.facebookOfficial className='size-4' />

          <div className='flex items-center space-x-2'>
            {!hideChooseButton && (
              <Icons.trash
                className='size-6 cursor-pointer rounded bg-rose/30 p-1 text-rose transition-all hover:bg-rose hover:text-background'
                onClick={() => removeProposalFromSuggestions(suggestion)}
              />
            )}
            {hideChooseButton && (
              <Icons.trash
                className='size-6 cursor-pointer rounded bg-rose/30 p-1 text-rose transition-all hover:bg-rose hover:text-background'
                onClick={() => unselectProposal(suggestion)}
              />
            )}
            <Button
              size='sm'
              className={cn(
                'rounded-xl border border-[#3779E2] bg-[#006FEE33] text-[#3779E2]',
                {
                  hidden: hideChooseButton,
                }
              )}
              onClick={() => selectProposal(suggestion)}
            >
              Choose
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
