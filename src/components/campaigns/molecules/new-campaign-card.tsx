'use client';

import { cn } from '@/lib/utils';

import { useCampaignStore } from '@/stores/campaign-store';

import { simpleCreateCampaignFormOpts } from '@/config/campaigns/form.config';

import { NewCampaignForm } from '@/components/campaigns/molecules/forms/create/new-campaign-form';
import { withForm } from '@/components/shared/form';
import { Icons } from '@/components/shared/icons';
import { Button } from '@/components/ui/button';
import {
  CardTitle,
  CardFooter,
  CardHeader,
  CardContent,
  CardDescription,
} from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';

export const NewCampaignCard = withForm({
  ...simpleCreateCampaignFormOpts,
  defaultValues: {
    ...simpleCreateCampaignFormOpts.defaultValues,
  },
  render: function Render({ form }) {
    const { setIsAICampaignCardOpen, isAICampaignCardOpen } =
      useCampaignStore();

    return (
      <>
        <CardHeader className='px-4 pb-2'>
          <CardTitle className='text-[#162F58]'>Create New Campaign</CardTitle>
          <CardDescription className='sr-only'>
            Write a brief and precise prompt to guide the creation of your
            content
          </CardDescription>
        </CardHeader>
        <ScrollArea className='h-full'>
          <CardContent className='p-0 px-4 pb-4'>
            <NewCampaignForm form={form} />
          </CardContent>
        </ScrollArea>
        <CardFooter className='grid gap-4 border-t border-border bg-muted/70 px-5 py-4'>
          <Button
            iconStart={<Icons.enhancePrompt />}
            onClick={() => setIsAICampaignCardOpen(true)}
            className={cn({
              hidden: isAICampaignCardOpen,
            })}
          >
            AI Assisted campaign
          </Button>
          <form.AppForm>
            <form.SubmitButton
              className='border border-[#214784] bg-[#5F94E81A] text-[#214784] hover:bg-[#5F94E833]'
              onClick={form.handleSubmit}
            >
              Create custom campaign
            </form.SubmitButton>
          </form.AppForm>
        </CardFooter>
      </>
    );
  },
});
