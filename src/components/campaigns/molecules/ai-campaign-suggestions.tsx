import React from 'react';

import {
  CampaignResponse,
  AICampaignProposal,
} from '@/api/models/dtos/compaign.dto';

import { cn } from '@/lib/utils';

import { useFormatDate } from '@/hooks/use-date-fns-locale';

import { useCampaignStore } from '@/stores/campaign-store';

import { Icons } from '@/components/shared/icons';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardTitle,
  CardHeader,
  CardContent,
  CardDescription,
} from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';

interface AICampaignSuggestionProps {
  campaignsSuggestions: CampaignResponse[];
}

export function AICampaignSuggestions({
  campaignsSuggestions,
}: AICampaignSuggestionProps) {
  const { format } = useFormatDate();
  const { insertMultiAiCampaignProposals } = useCampaignStore();

  return campaignsSuggestions.map((campaignSuggestion, index) => (
    <Card key={index} className='flex h-full flex-col overflow-hidden'>
      <CardHeader className='space-y-1.5 p-2'>
        <CardTitle className='space-y-1'>
          <div className='flex items-center justify-between'>
            <span className='text-sm font-semibold text-[#0C2144]'>
              {campaignSuggestion.Name}
            </span>
            <div className='flex items-center'>
              <Icons.facebookOfficial className='size-4' />
              <Icons.instagramOfficial className='size-4' />
              <Icons.linkedinOfficial className='size-4' />
              <Icons.x className='size-4' />
            </div>
          </div>
          <div className='text-[0.688rem] font-normal text-[#3F3F46]'>
            {format(campaignSuggestion.CreationTimestamp, 'dd/MM/yyyy')}
          </div>
        </CardTitle>
        <CardDescription>{campaignSuggestion.Description}</CardDescription>
      </CardHeader>

      <CardContent className='flex-1 overflow-hidden px-2 py-2'>
        <Card className='flex h-full flex-col overflow-hidden'>
          <CardHeader className='p-2'>
            <CardTitle className='flex items-center justify-between'>
              <h3 className='text-xs font-semibold'>Suggestions</h3>
              <Button
                size='sm'
                className='rounded-xl bg-[#006FEE33] text-[#006FEE]'
                onClick={() => {
                  if (campaignSuggestion.Proposal) {
                    insertMultiAiCampaignProposals(campaignSuggestion.Proposal);
                  }
                }}
              >
                Choose all {campaignSuggestion.Proposal?.length}
              </Button>
            </CardTitle>
          </CardHeader>
          <ScrollArea className='h-full'>
            <CardContent className='h-full flex-1 overflow-hidden px-2 pb-2 pt-0'>
              <div className='space-y-2'>
                {campaignSuggestion.Proposal &&
                  campaignSuggestion.Proposal.map((suggestion, index) => (
                    <SuggestionCard key={index} suggestion={suggestion} />
                  ))}
              </div>
            </CardContent>
          </ScrollArea>
        </Card>
      </CardContent>
    </Card>
  ));
}

interface SuggestionCardProps {
  suggestion: AICampaignProposal;
  hideChooseButton?: boolean;
}

export function SuggestionCard({
  suggestion,
  hideChooseButton = false,
}: SuggestionCardProps) {
  const { format } = useFormatDate();
  const { insertNewAiCampaignProposal, deleteAiCampaignProposal } =
    useCampaignStore();

  return (
    <Card>
      <CardContent className='flex flex-col gap-2 p-2'>
        <div className='flex items-center gap-2'>
          <h3 className='text-xs font-bold text-[#52525B]'>Context :</h3>
          <h4 className='text-[0.688rem] font-semibold text-[#27272A]'>
            {suggestion.Name}
          </h4>
        </div>

        <p className='text-xs text-[#3F3F46]'>
          {format(suggestion.DateTime, 'dd/MM/yyyy')}
        </p>

        <div className='grid gap-0.5 text-xs'>
          <p className='font-bold text-[#11181C]'>Project :</p>
          <p className='text-[#27272A]'>{suggestion.Description}</p>
        </div>

        <div className='flex items-center justify-between'>
          <Icons.facebookOfficial className='size-4' />

          <div className='flex items-center space-x-2'>
            <Icons.trash
              className='size-6 cursor-pointer rounded bg-rose/30 p-1 text-rose transition-all hover:bg-rose hover:text-background'
              onClick={() => deleteAiCampaignProposal(suggestion)}
            />
            <Button
              size='sm'
              className={cn(
                'rounded-xl border border-[#3779E2] bg-[#006FEE33] text-[#3779E2]',
                {
                  hidden: hideChooseButton,
                }
              )}
              onClick={() => insertNewAiCampaignProposal(suggestion)}
            >
              Choose
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
