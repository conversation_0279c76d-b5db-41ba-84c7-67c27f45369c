import React from 'react';

import { CampaignResponse } from '@/api/models/dtos/compaign.dto';

import { useFormatDate } from '@/hooks/use-date-fns-locale';

import { useCampaignStore } from '@/stores/campaign-store';

import { ProposalSuggestionCard } from '@/components/campaigns/molecules/proposal-suggestion-card';
import { Icons } from '@/components/shared/icons';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardTitle,
  CardHeader,
  CardContent,
  CardDescription,
} from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';

interface AICampaignSuggestionProps {
  campaignsSuggestions: CampaignResponse[];
}

export function AICampaignSuggestions({
  campaignsSuggestions,
}: AICampaignSuggestionProps) {
  const { format } = useFormatDate();
  const { selectMultipleProposals } = useCampaignStore();

  return campaignsSuggestions
    .filter(
      (campaignSuggestion) =>
        campaignSuggestion.Proposal && campaignSuggestion.Proposal.length > 0
    )
    .map((campaignSuggestion, index) => (
      <Card key={index} className='flex h-full flex-col overflow-hidden'>
        <CardHeader className='space-y-1.5 p-2'>
          <CardTitle className='space-y-1'>
            <div className='flex items-center justify-between'>
              <span className='text-sm font-semibold text-[#0C2144]'>
                {campaignSuggestion.Name}
              </span>
              <div className='flex items-center'>
                <Icons.facebookOfficial className='size-4' />
                <Icons.instagramOfficial className='size-4' />
                <Icons.linkedinOfficial className='size-4' />
                <Icons.x className='size-4' />
              </div>
            </div>
            <div className='text-[0.688rem] font-normal text-[#3F3F46]'>
              {format(campaignSuggestion.CreationTimestamp, 'dd/MM/yyyy')}
            </div>
          </CardTitle>
          <CardDescription>{campaignSuggestion.Description}</CardDescription>
        </CardHeader>

        <CardContent className='flex-1 overflow-hidden px-2 py-2'>
          <Card className='flex h-full flex-col overflow-hidden'>
            <CardHeader className='p-2'>
              <CardTitle className='flex items-center justify-between'>
                <h3 className='text-xs font-semibold'>Suggestions</h3>
                <Button
                  size='sm'
                  className='rounded-xl bg-[#006FEE33] text-[#006FEE]'
                  onClick={() => {
                    if (campaignSuggestion.Proposal) {
                      selectMultipleProposals(campaignSuggestion.Proposal);
                    }
                  }}
                >
                  Choose all
                </Button>
              </CardTitle>
            </CardHeader>
            <ScrollArea className='h-full'>
              <CardContent className='h-full flex-1 overflow-hidden px-2 pb-2 pt-0'>
                <div className='space-y-2'>
                  {campaignSuggestion.Proposal &&
                    campaignSuggestion.Proposal.map((suggestion, index) => (
                      <ProposalSuggestionCard
                        key={index}
                        suggestion={suggestion}
                      />
                    ))}
                </div>
              </CardContent>
            </ScrollArea>
          </Card>
        </CardContent>
      </Card>
    ));
}
