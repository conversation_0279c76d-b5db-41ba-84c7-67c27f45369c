import React from 'react';

import { useCreativePostOptions } from '@/hooks/creative/use-creative-post-options';

import { simpleCreateCampaignFormOpts } from '@/config/campaigns/form.config';
import { useOptionsConfig } from '@/config/campaigns/options.config';

import { withForm } from '@/components/shared/form';

export const NewAICampaignForm = withForm({
  ...simpleCreateCampaignFormOpts,
  defaultValues: {
    ...simpleCreateCampaignFormOpts.defaultValues,
  },
  render: function Render({ form }) {
    const { marketingStrategies } = useCreativePostOptions();
    const { periodOptions, frequencyOptions, regionsAndCountriesOptions } =
      useOptionsConfig();

    return (
      <form
        className='grid gap-3'
        onSubmit={async (e) => {
          e.preventDefault();
          e.stopPropagation();
          await form.handleSubmit();
        }}
      >
        <form.AppField name='MarketingStrategy'>
          {(field) => (
            <field.SelectField
              required={false}
              label='Maketing strategy'
              placeholder='Select a marketing strategy'
              options={marketingStrategies}
            />
          )}
        </form.AppField>

        <form.AppField name='Period'>
          {(field) => (
            <field.SelectField
              required={false}
              label='Period'
              placeholder='Period'
              options={periodOptions}
            />
          )}
        </form.AppField>

        <form.AppField name='Frequency'>
          {(field) => (
            <field.SelectField
              required={false}
              label='Frequency'
              placeholder='Frequency'
              options={frequencyOptions}
            />
          )}
        </form.AppField>

        <form.AppField name='TimeZoneRegion'>
          {(field) => (
            <field.TimeZoneSelectField label='Time Zone' required={false} />
          )}
        </form.AppField>

        <form.AppField name='Evergreen'>
          {() => (
            <>
              <p className='font-bold text-[#11181C]'>
                Special events and dates
              </p>

              <form.AppField name='Evergreen.RegionOrCountries' mode='array'>
                {(field) => (
                  <field.MultiSelectGroupedOptionsField
                    label='Regions or Countries'
                    placeholder='Select regions or countries'
                    options={regionsAndCountriesOptions}
                    required={false}
                    maxDisplayCount={3}
                    showClearAll={true}
                  />
                )}
              </form.AppField>

              <form.AppField name='Evergreen.Topics'>
                {(field) => (
                  <field.TextFieldWithInnerTags
                    label='Topics'
                    placeholder='Topics'
                    required={false}
                    inlineTags
                    showSeparateAddButton
                  />
                )}
              </form.AppField>
            </>
          )}
        </form.AppField>
      </form>
    );
  },
});
