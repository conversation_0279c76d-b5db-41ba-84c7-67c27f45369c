import React, { useId, Fragment, useState } from 'react';

import { XIcon, CheckIcon, ChevronDownIcon } from 'lucide-react';

import { useCreativePostOptions } from '@/hooks/creative/use-creative-post-options';

import { simpleCreateCampaignFormOpts } from '@/config/campaigns/form.config';

import { withForm } from '@/components/shared/form';
import { TimeZoneSelect } from '@/components/shared/time-zone-select';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandItem,
  CommandList,
  CommandEmpty,
  CommandGroup,
  CommandInput,
} from '@/components/ui/command';
import { Label } from '@/components/ui/label';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';

const periodOptions = [
  { value: '1 week', label: '1 Week' },
  { value: '2 week', label: '2 Week' },
  { value: '1 month', label: '1 Month' },
];

const frequencyOptions = [
  {
    value: 'Daily',
    label: 'Daily',
  },
  {
    value: 'Alternate Days',
    label: 'Alternate Days',
  },
  {
    value: 'Weekly',
    label: 'Weekly',
  },
  {
    value: 'Bi-Weekly',
    label: 'Bi Weekly',
  },
  {
    value: 'Monthly',
    label: 'Monthly',
  },
  {
    value: 'Seasonal/Occasional',
    label: 'Seasonal / Occasional',
  },
  {
    value: 'Event-Based',
    label: 'Event Based',
  },
];

interface Options {
  groupLabel: string;
  items: { value: string; label: string; icon?: React.ReactNode }[];
}

const options: Options[] = [
  {
    groupLabel: 'Regions',
    items: [
      { value: 'north-america', label: 'North America', icon: '🌎' },
      { value: 'south-america', label: 'South America', icon: '🌎' },
      { value: 'europe', label: 'Europe', icon: '🌍' },
      { value: 'asia', label: 'Asia', icon: '🌏' },
      { value: 'africa', label: 'Africa', icon: '🌍' },
      { value: 'oceania', label: 'Oceania', icon: '🌏' },
    ],
  },
  {
    groupLabel: 'Countries',
    items: [
      { value: 'united-states', label: 'United States', icon: '🇺🇸' },
      { value: 'canada', label: 'Canada', icon: '🇨🇦' },
      { value: 'mexico', label: 'Mexico', icon: '🇲🇽' },
      { value: 'united-kingdom', label: 'United Kingdom', icon: '🇬🇧' },
      { value: 'france', label: 'France', icon: '🇫🇷' },
      { value: 'germany', label: 'Germany', icon: '🇩🇪' },
      { value: 'china', label: 'China', icon: '🇨🇳' },
      { value: 'japan', label: 'Japan', icon: '🇯🇵' },
      { value: 'india', label: 'India', icon: '🇮🇳' },
      { value: 'australia', label: 'Australia', icon: '🇦🇺' },
      { value: 'brazil', label: 'Brazil', icon: '🇧🇷' },
      { value: 'south-africa', label: 'South Africa', icon: '🇿🇦' },
    ],
  },
];

export const NewAICampaignForm = withForm({
  ...simpleCreateCampaignFormOpts,
  defaultValues: {
    ...simpleCreateCampaignFormOpts.defaultValues,
  },
  render: function Render({ form }) {
    // TODO: Extract this into a custom group multiselect component
    const id = useId();
    const [open, setOpen] = useState<boolean>(false);
    const [selectedValues, setSelectedValues] = useState<string[]>([]);

    const { marketingStrategies } = useCreativePostOptions();

    return (
      <form
        className='grid gap-3'
        onSubmit={async (e) => {
          e.preventDefault();
          e.stopPropagation();
          await form.handleSubmit();
        }}
      >
        <form.AppField name='MarketingStrategy'>
          {(field) => (
            <field.SelectField
              required={false}
              label='Maketing strategy'
              placeholder='Select a marketing strategy'
              options={marketingStrategies}
            />
          )}
        </form.AppField>

        <form.AppField name='Period'>
          {(field) => (
            <field.SelectField
              required={false}
              label='Period'
              placeholder='Period'
              options={periodOptions}
            />
          )}
        </form.AppField>

        <form.AppField name='Frequency'>
          {(field) => (
            <field.SelectField
              required={false}
              label='Frequency'
              placeholder='Frequency'
              options={frequencyOptions}
            />
          )}
        </form.AppField>

        <form.AppField name='TimeZoneRegion'>
          {() => <TimeZoneSelect />}
        </form.AppField>

        <form.AppField name='Evergreen'>
          {() => (
            <>
              <p className='font-bold text-[#11181C]'>
                Special events and dates
              </p>

              <form.AppField name='Evergreen.RegionOrCountries' mode='array'>
                {() => (
                  <>
                    <Label>Regions or Countries</Label>
                    <Popover open={open} onOpenChange={setOpen}>
                      <PopoverTrigger asChild>
                        <Button
                          id={id}
                          variant='outline'
                          role='combobox'
                          aria-expanded={open}
                          className='h-auto w-full justify-between border-input bg-background px-3 font-normal outline-none outline-offset-0 hover:bg-background focus-visible:outline-[3px]'
                        >
                          {selectedValues.length > 0 ? (
                            <div className='flex min-w-0 flex-1 flex-wrap items-center gap-1'>
                              {selectedValues.slice(0, 3).map((value) => {
                                const item = options
                                  .flatMap((group) => group.items)
                                  .find((item) => item.value === value);
                                return (
                                  <Badge
                                    key={value}
                                    variant='secondary'
                                    className='flex items-center gap-1 text-xs'
                                  >
                                    <span className='text-sm leading-none'>
                                      {item?.icon}
                                    </span>
                                    <span className='max-w-20 truncate'>
                                      {item?.label}
                                    </span>
                                    <button
                                      type='button'
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        setSelectedValues((prev) =>
                                          prev.filter((val) => val !== value)
                                        );
                                      }}
                                      className='ml-1 rounded-full p-0.5 hover:bg-muted-foreground/20'
                                    >
                                      <XIcon size={12} />
                                    </button>
                                  </Badge>
                                );
                              })}
                              {selectedValues.length > 3 && (
                                <Badge variant='outline' className='text-xs'>
                                  +{selectedValues.length - 3} more
                                </Badge>
                              )}
                            </div>
                          ) : (
                            <span className='text-muted-foreground'>
                              Select options
                            </span>
                          )}
                          <ChevronDownIcon
                            size={16}
                            className='shrink-0 text-muted-foreground/80'
                            aria-hidden='true'
                          />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent
                        className='w-full min-w-[var(--radix-popper-anchor-width)] border-input p-0'
                        align='start'
                      >
                        <Command>
                          <CommandInput placeholder='Search options...' />

                          <CommandList>
                            <CommandEmpty>No option found.</CommandEmpty>
                            {options.map((group) => (
                              <Fragment key={group.groupLabel}>
                                <CommandGroup heading={group.groupLabel}>
                                  {group.items.map((item) => (
                                    <CommandItem
                                      key={item.value}
                                      value={item.value}
                                      onSelect={(currentValue) => {
                                        setSelectedValues((prev: string[]) =>
                                          prev.includes(currentValue)
                                            ? prev.filter(
                                                (val: string) =>
                                                  val !== currentValue
                                              )
                                            : [...prev, currentValue]
                                        );
                                      }}
                                    >
                                      <span className='text-lg leading-none'>
                                        {item.icon}
                                      </span>{' '}
                                      {item.label}
                                      {selectedValues.includes(item.value) && (
                                        <CheckIcon
                                          size={16}
                                          className='ml-auto'
                                        />
                                      )}
                                    </CommandItem>
                                  ))}
                                </CommandGroup>
                              </Fragment>
                            ))}
                          </CommandList>

                          {selectedValues.length > 0 && (
                            <CommandItem asChild className='px-2'>
                              <Button
                                variant='ghost'
                                size='sm'
                                onClick={() => setSelectedValues([])}
                              >
                                Clear all selections
                              </Button>
                            </CommandItem>
                          )}
                        </Command>
                      </PopoverContent>
                    </Popover>
                  </>
                )}
              </form.AppField>

              <form.AppField name='Evergreen.Topics'>
                {(field) => (
                  <field.TextFieldWithInnerTags
                    label='Topics'
                    placeholder='Topics'
                    required={false}
                    inlineTags
                    showSeparateAddButton
                  />
                )}
              </form.AppField>
            </>
          )}
        </form.AppField>
      </form>
    );
  },
});
