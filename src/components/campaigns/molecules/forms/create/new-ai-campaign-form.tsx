import React, { useState } from 'react';

import { useCreativePostOptions } from '@/hooks/creative/use-creative-post-options';

import { simpleCreateCampaignFormOpts } from '@/config/campaigns/form.config';

import { withForm } from '@/components/shared/form';
import { MultiSelect, type MultiSelectGroup } from '@/components/ui/multi-select';

const periodOptions = [
  { value: '1 week', label: '1 Week' },
  { value: '2 week', label: '2 Week' },
  { value: '1 month', label: '1 Month' },
];

const frequencyOptions = [
  {
    value: 'Daily',
    label: 'Daily',
  },
  {
    value: 'Alternate Days',
    label: 'Alternate Days',
  },
  {
    value: 'Weekly',
    label: 'Weekly',
  },
  {
    value: 'Bi-Weekly',
    label: 'Bi Weekly',
  },
  {
    value: 'Monthly',
    label: 'Monthly',
  },
  {
    value: 'Seasonal/Occasional',
    label: 'Seasonal / Occasional',
  },
  {
    value: 'Event-Based',
    label: 'Event Based',
  },
];

const regionsAndCountriesOptions: MultiSelectGroup[] = [
  {
    groupLabel: 'Regions',
    items: [
      { value: 'north-america', label: 'North America', icon: '🌎' },
      { value: 'south-america', label: 'South America', icon: '🌎' },
      { value: 'europe', label: 'Europe', icon: '🌍' },
      { value: 'asia', label: 'Asia', icon: '🌏' },
      { value: 'africa', label: 'Africa', icon: '🌍' },
      { value: 'oceania', label: 'Oceania', icon: '🌏' },
    ],
  },
  {
    groupLabel: 'Countries',
    items: [
      { value: 'united-states', label: 'United States', icon: '🇺🇸' },
      { value: 'canada', label: 'Canada', icon: '🇨🇦' },
      { value: 'mexico', label: 'Mexico', icon: '🇲🇽' },
      { value: 'united-kingdom', label: 'United Kingdom', icon: '🇬🇧' },
      { value: 'france', label: 'France', icon: '🇫🇷' },
      { value: 'germany', label: 'Germany', icon: '🇩🇪' },
      { value: 'china', label: 'China', icon: '🇨🇳' },
      { value: 'japan', label: 'Japan', icon: '🇯🇵' },
      { value: 'india', label: 'India', icon: '🇮🇳' },
      { value: 'australia', label: 'Australia', icon: '🇦🇺' },
      { value: 'brazil', label: 'Brazil', icon: '🇧🇷' },
      { value: 'south-africa', label: 'South Africa', icon: '🇿🇦' },
    ],
  },
];

export const NewAICampaignForm = withForm({
  ...simpleCreateCampaignFormOpts,
  defaultValues: {
    ...simpleCreateCampaignFormOpts.defaultValues,
  },
  render: function Render({ form }) {
    const [selectedRegions, setSelectedRegions] = useState<string[]>([]);

    const { marketingStrategies } = useCreativePostOptions();

    return (
      <form
        className='grid gap-3'
        onSubmit={async (e) => {
          e.preventDefault();
          e.stopPropagation();
          await form.handleSubmit();
        }}
      >
        <form.AppField name='MarketingStrategy'>
          {(field) => (
            <field.SelectField
              required={false}
              label='Maketing strategy'
              placeholder='Select a marketing strategy'
              options={marketingStrategies}
            />
          )}
        </form.AppField>

        <form.AppField name='Period'>
          {(field) => (
            <field.SelectField
              required={false}
              label='Period'
              placeholder='Period'
              options={periodOptions}
            />
          )}
        </form.AppField>

        <form.AppField name='Frequency'>
          {(field) => (
            <field.SelectField
              required={false}
              label='Frequency'
              placeholder='Frequency'
              options={frequencyOptions}
            />
          )}
        </form.AppField>

        <form.AppField name='TimeZoneRegion'>
          {(field) => (
            <field.TimeZoneSelectField label='Time Zone' required={false} />
          )}
        </form.AppField>

        <form.AppField name='Evergreen'>
          {() => (
            <>
              <p className='font-bold text-[#11181C]'>
                Special events and dates
              </p>

              <form.AppField name='Evergreen.RegionOrCountries' mode='array'>
                {(field) => (
                  <MultiSelect
                    label="Regions or Countries"
                    placeholder="Select regions or countries"
                    searchPlaceholder="Search regions or countries..."
                    options={regionsAndCountriesOptions}
                    value={selectedRegions}
                    onValueChange={(values) => {
                      setSelectedRegions(values);
                      field.handleChange(values);
                    }}
                    maxDisplayCount={3}
                    showClearAll={true}
                    clearAllText="Clear all selections"
                    emptyText="No regions or countries found."
                  />
                )}
              </form.AppField>

              <form.AppField name='Evergreen.Topics'>
                {(field) => (
                  <field.TextFieldWithInnerTags
                    label='Topics'
                    placeholder='Topics'
                    required={false}
                    inlineTags
                    showSeparateAddButton
                  />
                )}
              </form.AppField>
            </>
          )}
        </form.AppField>
      </form>
    );
  },
});
