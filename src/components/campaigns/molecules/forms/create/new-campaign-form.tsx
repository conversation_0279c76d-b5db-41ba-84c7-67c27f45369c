import { max, isAfter, isBefore, startOfToday } from 'date-fns';
import React from 'react';

import { useAllKnowledge } from '@/api/hooks/knowledge/queries';
import { useCurrentUserStore } from '@/stores/current-user-store';

import { simpleCreateCampaignFormOpts } from '@/config/campaigns/form.config';

import { withForm } from '@/components/shared/form';
import { MultiSelect } from '@/components/shared/form/multi-select';
import { Label } from '@/components/ui/label';

// TODO: Extract this config into a hook
const presetColors = [
  '#1E90FF',
  '#87CEEB',
  '#FFD700',
  '#F5DEB3',
  '#32CD32',
  '#98FB98',
  '#DC143C',
  '#FFB6C1',
  '#3F51B5',
];

const marketingObjectivesOptions = [
  { value: 'brand awareness', label: 'Brand Awareness' },
  { value: 'audience engagement', label: 'Audience Engagement' },
  { value: 'lead generation', label: 'Lead Generation' },
  { value: 'driving website traffic', label: 'Driving Website Traffic' },
  { value: 'conversion', label: 'Conversion (Sales or Sign-ups)' },
  {
    value: 'customer retention and loyalty',
    label: 'Customer Retention and Loyalty',
  },
  { value: 'reputation management', label: 'Reputation Management' },
  { value: 'thought leadership', label: 'Thought Leadership' },
  { value: 'community building', label: 'Community Building' },
  { value: 'event promotion', label: 'Event Promotion' },
  { value: 'improving customer service', label: 'Improving Customer Service' },
  { value: 'product discovery', label: 'Product Discovery' },
  { value: 'sales and promotions', label: 'Sales and Promotions' },
  { value: 'showcasing product usage', label: 'Showcasing Product Usage' },
  { value: 'social proof and reviews', label: 'Social Proof and Reviews' },
  { value: 'direct conversions', label: 'Direct Conversions (Sales)' },
  { value: 'abandoned cart recovery', label: 'Abandoned Cart Recovery' },
  { value: 'seasonal campaigns', label: 'Seasonal Campaigns' },
  {
    value: 'upselling and cross-selling',
    label: 'Upselling and Cross-Selling',
  },
  { value: 'customer education', label: 'Customer Education' },
  {
    value: 'building customer trust and loyalty',
    label: 'Building Customer Trust and Loyalty',
  },
  { value: 'creating urgency', label: 'Creating Urgency (FOMO)' },
  {
    value: 'collaborations and influencer partnerships',
    label: 'Collaborations and Influencer Partnerships',
  },
  {
    value: 'encouraging repeat purchases',
    label: 'Encouraging Repeat Purchases',
  },
  {
    value: 'visual merchandising on social media',
    label: 'Visual Merchandising on Social Media',
  },
  {
    value: 'pre-orders and product launches',
    label: 'Pre-orders and Product Launches',
  },
];

export const NewCampaignForm = withForm({
  ...simpleCreateCampaignFormOpts,
  defaultValues: {
    ...simpleCreateCampaignFormOpts.defaultValues,
  },
  render: function Render({ form }) {
    const currentUser = useCurrentUserStore().getUser();

    const { data: knowledges } = useAllKnowledge(
      currentUser?.companyId,
      currentUser?.brandId
    );

    return (
      <form
        className='grid gap-3'
        onSubmit={async (e) => {
          e.preventDefault();
          e.stopPropagation();
          await form.handleSubmit();
        }}
      >
        <form.AppField name='Platforms'>
          {(field) => (
            <field.SocialMediaRadioField
              label='Choose targeted social media'
              multiSelect
            />
          )}
        </form.AppField>

        <form.AppField name='Name'>
          {(field) => <field.TextField label='Title' placeholder='content' />}
        </form.AppField>

        <form.AppField name='Description'>
          {(field) => (
            <field.TextareaField
              label='Description'
              placeholder='content'
              className='h-28'
            />
          )}
        </form.AppField>

        <form.AppField name='Color'>
          {(field) => (
            <field.ColorPickerField
              required={false}
              label='Define a color for this campaign'
              defaultPresets={presetColors}
            />
          )}
        </form.AppField>

        <form.AppField name='Objective'>
          {(field) => (
            <field.SelectField
              required={false}
              label='Objective'
              placeholder='Select an objective'
              options={marketingObjectivesOptions}
            />
          )}
        </form.AppField>

        <form.AppField name='Knowledges'>
          {(field) => {
            return (
              <>
                <Label htmlFor={field.name}>Knowledge</Label>
                <MultiSelect
                  options={
                    knowledges?.map((knowledge) => ({
                      label: knowledge.Name,
                      value: knowledge.Id,
                    })) ?? []
                  }
                  onValueChange={(selectedOptions) => {
                    field.handleChange(selectedOptions.map((option) => option));
                  }}
                  placeholder='Select knowledges'
                />
              </>
            );
          }}
        </form.AppField>

        <form.Subscribe
          selector={(state) => ({
            startDate: state.values.StartDate,
            endDate: state.values.EndDate,
          })}
        >
          {({ startDate, endDate }) => (
            <>
              <form.AppField
                name='StartDate'
                listeners={{
                  onChange: ({ value }) => {
                    if (value && endDate && isAfter(value, endDate)) {
                      form.setFieldValue('EndDate', undefined);
                    }
                  },
                }}
              >
                {(field) => (
                  <field.DatePickerField
                    label='Start Date'
                    required={false}
                    datePickerProps={{
                      disabled: (date) => isBefore(date, startOfToday()),
                    }}
                  />
                )}
              </form.AppField>

              <form.AppField name='EndDate'>
                {(field) => (
                  <field.DatePickerField
                    label='End Date'
                    required={false}
                    datePickerProps={{
                      disabled: (date) => {
                        const minAllowed = startDate
                          ? max([startDate, startOfToday()])
                          : startOfToday();
                        return isBefore(date, minAllowed);
                      },
                    }}
                  />
                )}
              </form.AppField>
            </>
          )}
        </form.Subscribe>
      </form>
    );
  },
});
