import { X } from 'lucide-react';

import { useCampaignStore } from '@/stores/campaign-store';

import { simpleCreateCampaignFormOpts } from '@/config/campaigns/form.config';

import { NewAICampaignForm } from '@/components/campaigns/molecules/forms/create/new-ai-campaign-form';
import { withForm } from '@/components/shared/form';
import { Button } from '@/components/ui/button';
import {
  CardTitle,
  CardHeader,
  CardFooter,
  CardContent,
  CardDescription,
} from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';

export const NewAiCampaignCard = withForm({
  ...simpleCreateCampaignFormOpts,
  defaultValues: {
    ...simpleCreateCampaignFormOpts.defaultValues,
  },
  render: function Render({ form }) {
    const { setIsAICampaignCardOpen } = useCampaignStore();

    return (
      <>
        <CardHeader className='relative px-4 pb-2'>
          <CardTitle className='flex items-center justify-between text-[#162F58]'>
            <span>AI assisted</span>
          </CardTitle>
          <CardDescription className='sr-only'>
            Write a brief and precise prompt to guide the creation of your
            content
          </CardDescription>
          <Button
            size='setting'
            className='absolute end-4 top-1/2 -translate-y-1/2 rounded-lg bg-[#E4E4E7] hover:bg-[#D1D5DB]'
            onClick={() => setIsAICampaignCardOpen(false)}
          >
            <X className='text-[#52525B]' />
          </Button>
        </CardHeader>
        <ScrollArea className='h-full'>
          <CardContent className='p-0 px-4 pb-4'>
            <NewAICampaignForm form={form} />
          </CardContent>
        </ScrollArea>
        <CardFooter className='grid gap-4 border-t border-border bg-muted/70 px-5 py-4'>
          <form.AppForm>
            <form.SubmitButton onClick={form.handleSubmit}>
              Generate
            </form.SubmitButton>
          </form.AppForm>
        </CardFooter>
      </>
    );
  },
});
