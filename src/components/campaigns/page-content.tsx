'use client';

import { CirclePlus } from 'lucide-react';

import { useAllCampaigns } from '@/api/hooks/campaign/queries';
import { Link } from '@/i18n/routing';

import { useCampaignsTableColumns } from '@/config/campaigns/use-campaigns-table-columns';

import { GridViewMode } from '@/components/campaigns/molecules/grid-view-mode';
import { DataTable } from '@/components/shared/table/data-table';
import { Button } from '@/components/ui/button';

export default function PageContent() {
  const { columnsDef } = useCampaignsTableColumns();

  const {
    data: allCampaigns,
    refetch,
    isFetching,
    isError,
    error,
  } = useAllCampaigns();

  return (
    <div className='flex h-full flex-col'>
      <div className='flex items-start justify-between'>
        <div>
          <div className='flex items-center justify-between'>
            <h1 className='text-2xl font-bold'>Campaigns List</h1>
          </div>

          <p className='text-gray-0 my-2'>
            A campaign is a strategic plan designed to achieve specific goals
            for your brand
          </p>
        </div>

        <Link href='/dashboard/campaigns/new-campaign'>
          <Button
            className='rounded-lg'
            iconEnd={<CirclePlus className='fill-background text-primary' />}
          >
            Create a Campaign
          </Button>
        </Link>
      </div>

      <DataTable
        columns={columnsDef}
        data={allCampaigns ?? []}
        isLoading={isFetching}
        isError={isError}
        error={error}
        refetch={refetch}
        gridView={(campaignItem) => <GridViewMode campaign={campaignItem} />}
        initialColumnVisibility={{
          CreationTimestamp: false,
        }}
      />
    </div>
  );
}
