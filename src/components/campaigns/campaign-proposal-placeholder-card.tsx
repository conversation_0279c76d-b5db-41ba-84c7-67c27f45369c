import { Icons } from '@/components/shared/icons';
import { But<PERSON> } from '@/components/ui/button';

export function CampaignProposalPlaceholderCard() {
  return (
    <div className='absolute bottom-20 start-1/2 grid -translate-x-1/2 place-items-center gap-2'>
      <Button
        className='w-full rounded-full bg-[#162F58] py-6 text-[1.25rem] hover:bg-[#162F58]/80'
        size='lg'
        iconEnd={<Icons.circlePlay className='!size-7' />}
      >
        Watch
      </Button>
      <p className='max-w-52 text-center text-[10px] text-[#71717A]'>
        For more information about the campaign and its creation, you can watch
        this video.
      </p>
    </div>
  );
}
