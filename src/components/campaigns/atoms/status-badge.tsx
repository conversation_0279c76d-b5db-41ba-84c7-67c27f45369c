import { useTranslations } from 'next-intl';

import { CampaignResponse } from '@/api/models/dtos/compaign.dto';

import { Badge } from '@/components/ui/badge';

interface StatusBadgeProps {
  status: CampaignResponse['Status'];
}

export function StatusBadge({ status }: StatusBadgeProps) {
  const t = useTranslations();

  switch (status) {
    case 'Pending':
      return (
        <Badge
          variant='outline'
          className='flex w-fit items-center gap-2 rounded-md bg-[#F4F4F5] font-medium text-[#979797]'
        >
          <div className='size-2 rounded-full bg-[#3776DC]' />
          Pending
        </Badge>
      );
    case 'In progress':
      return (
        <Badge
          variant='outline'
          className='flex w-fit items-center gap-2 rounded-md bg-[#F4F4F5] font-medium text-[#979797]'
        >
          <div className='size-2 rounded-full bg-[#17C964]' />
          In progress
        </Badge>
      );
    case 'Completed':
      return (
        <Badge
          variant='outline'
          className='flex w-fit items-center gap-2 rounded-md bg-[#F4F4F5] font-medium text-[#979797]'
        >
          <div className='size-2 rounded-full bg-[#71717A]' />
          Completed
        </Badge>
      );
    default:
      return <Badge variant='destructive'>{t('not_handled')}</Badge>;
  }
}
