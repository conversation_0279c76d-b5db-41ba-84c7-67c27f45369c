import Image from 'next/image';
import React from 'react';

import { Card, CardContent } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';

const ProfileCard = () => {
  return (
    <Card className='overflow-hidden'>
      <CardContent className='p-0'>
        <div className='flex items-center justify-center gap-2'>
          {/* Profile Image */}
          <div className='flex items-center justify-center ps-2'>
            <Image
              src='/images/card-image-right-sidebar.png'
              alt='Profile'
              width={64}
              height={64}
              className='h-16 w-16 object-cover'
            />
          </div>

          {/* Profile Info */}
          <div className='flex-1 p-1'>
            <h3 className='text-[0.56rem] font-semibold tracking-wide text-gray-900'>
              KWORE
            </h3>

            <p className='text-[0.53rem] tracking-tighter text-gray-600'>
              Dive into seamless communication ...
              <span className='ml-1 font-medium text-blue-600'>show</span>
            </p>

            <div className='flex items-center justify-between text-[0.6rem] font-normal'>
              <p>Analysis Post Performance</p>
              <span>70%</span>
            </div>

            <div>
              <Progress value={70} className='h-1 bg-gray-200' />
            </div>

            {/* Date */}
            <p className='text-[0.5rem] font-semibold text-[#3F3F46]'>
              08/01/2025 14:00 GMT+1
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ProfileCard;
