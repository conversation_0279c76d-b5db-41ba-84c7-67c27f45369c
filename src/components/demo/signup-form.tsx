'use client';

import { useTranslations } from 'next-intl';
import React from 'react';
import { toast } from 'sonner';

import { useRegister } from '@/api/hooks/auth/mutations';
import { signupSchema } from '@/api/models/schemas/signup.schema';
import { Link, useRouter } from '@/i18n/routing';

import { useAppForm } from '@/components/shared/form';

export default function LoginForm() {
  const t = useTranslations();
  const { mutateAsync } = useRegister();
  const router = useRouter();

  const { AppForm, AppField, SubmitButton, handleSubmit, reset } = useAppForm({
    defaultValues: {
      UserName: '',
      UserEmail: '',
      UserPassword: '',
      UserPasswordConfirm: '',
      terms: false,
    },
    validators: {
      onChange: signupSchema(t),
    },
    onSubmit: async ({ value }) => {
      try {
        await mutateAsync(value);
        toast.success(t('form.success'));
        router.push('/login');
        reset();
      } catch (error: unknown) {
        if (error instanceof Error) {
          toast.error(t(error.message));
        }
      }
    },
    onSubmitInvalid: () => {
      toast.error(t('form.invalid_form'));
    },
  });

  return (
    <form
      className='flex flex-col gap-4'
      onSubmit={async (e) => {
        e.preventDefault();
        await handleSubmit();
      }}
    >
      <h1 className='text-2xl font-semibold text-foreground/80'>
        {t('title_signup')}
      </h1>

      <div className='grid gap-4 sm:gap-2'>
        <AppField name='UserName'>
          {(field) => (
            <field.TextField
              label={t('form.username')}
              labelClassName='text-xs font-normal text-foreground/70'
              autoComplete='current-password'
              placeholder={t('form.username_placeholder')}
              className='rounded-xl border-2 border-[#E4E4E7] placeholder:text-xs'
            />
          )}
        </AppField>

        <AppField name='UserEmail'>
          {(field) => (
            <field.TextField
              type='email'
              label={t('form.email')}
              labelClassName='text-xs font-normal text-foreground/70'
              autoComplete='email'
              placeholder='<EMAIL>'
              className='rounded-xl border-2 border-[#E4E4E7] placeholder:text-xs'
            />
          )}
        </AppField>

        <AppField name='UserPassword'>
          {(field) => (
            <field.TextField
              type='password'
              label={t('form.password')}
              labelClassName='text-xs font-normal text-foreground/70'
              autoComplete='current-password'
              placeholder='********'
              className='rounded-xl border-2 border-[#E4E4E7] placeholder:text-xs'
            />
          )}
        </AppField>

        <AppField name='UserPasswordConfirm'>
          {(field) => (
            <field.TextField
              type='password'
              label={t('form.confirm_password')}
              labelClassName='text-xs font-normal text-foreground/70'
              autoComplete='current-password'
              placeholder='********'
              className='rounded-xl border-2 border-[#E4E4E7] placeholder:text-xs'
            />
          )}
        </AppField>

        <AppField name='terms'>
          {(field) => (
            <field.CheckboxField
              label={
                <>
                  <span>{t('i_agree_with_the')} </span>
                  <Link
                    href='#'
                    className='text-primary underline underline-offset-2'
                  >
                    {t('terms')}
                  </Link>
                  <span> {t('and')} </span>
                  <Link
                    href='#'
                    className='text-primary underline underline-offset-2'
                  >
                    {t('privacy_policy')}
                  </Link>
                </>
              }
              required={false}
              labelClassName='text-xs font-normal text-foreground/70'
              checkboxClassName='rounded-md'
            />
          )}
        </AppField>
      </div>

      <AppForm>
        <SubmitButton className='h-7 rounded-lg text-xs font-light'>
          <span>{t('signup')}</span>
        </SubmitButton>
      </AppForm>
    </form>
  );
}
