import clsx from 'clsx';
import React from 'react';

import { useLocalTheme } from '@/providers/local-theme-provider';

type LocalThemeWrapperProps = {
  children: React.ReactNode;
  overrideTheme?: 'light' | 'dark';
  className?: string;
};

export const LocalThemeWrapper: React.FC<LocalThemeWrapperProps> = ({
  children,
  overrideTheme,
  className = '',
}) => {
  const { theme } = useLocalTheme();
  const activeTheme = overrideTheme || theme;

  return (
    <div className={clsx(activeTheme === 'dark' && 'dark', className)}>
      {children}
    </div>
  );
};
