import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';

import { MultiSelect, type MultiSelectGroup, type MultiSelectOption } from './multi-select';

const mockFlatOptions: MultiSelectOption[] = [
  { value: 'option1', label: 'Option 1', icon: '🔥' },
  { value: 'option2', label: 'Option 2', icon: '⭐' },
  { value: 'option3', label: 'Option 3' },
];

const mockGroupedOptions: MultiSelectGroup[] = [
  {
    groupLabel: 'Group 1',
    items: [
      { value: 'group1-option1', label: 'Group 1 Option 1', icon: '🌎' },
      { value: 'group1-option2', label: 'Group 1 Option 2', icon: '🌍' },
    ],
  },
  {
    groupLabel: 'Group 2',
    items: [
      { value: 'group2-option1', label: 'Group 2 Option 1', icon: '🇺🇸' },
      { value: 'group2-option2', label: 'Group 2 Option 2', icon: '🇨🇦' },
    ],
  },
];

describe('MultiSelect', () => {
  it('renders with default props', () => {
    render(
      <MultiSelect
        options={mockFlatOptions}
        onValueChange={vi.fn()}
      />
    );

    expect(screen.getByRole('combobox')).toBeInTheDocument();
    expect(screen.getByText('Select options')).toBeInTheDocument();
  });

  it('renders with label when provided', () => {
    render(
      <MultiSelect
        label="Test Label"
        options={mockFlatOptions}
        onValueChange={vi.fn()}
      />
    );

    expect(screen.getByText('Test Label')).toBeInTheDocument();
  });

  it('shows required indicator when required is true', () => {
    render(
      <MultiSelect
        label="Required Field"
        required={true}
        options={mockFlatOptions}
        onValueChange={vi.fn()}
      />
    );

    const label = screen.getByText('Required Field');
    expect(label).toHaveClass("after:content-['*']");
  });

  it('displays custom placeholder', () => {
    render(
      <MultiSelect
        placeholder="Custom placeholder"
        options={mockFlatOptions}
        onValueChange={vi.fn()}
      />
    );

    expect(screen.getByText('Custom placeholder')).toBeInTheDocument();
  });

  it('displays selected values as badges', () => {
    render(
      <MultiSelect
        options={mockFlatOptions}
        value={['option1', 'option2']}
        onValueChange={vi.fn()}
      />
    );

    expect(screen.getByText('Option 1')).toBeInTheDocument();
    expect(screen.getByText('Option 2')).toBeInTheDocument();
  });

  it('shows "+X more" badge when maxDisplayCount is exceeded', () => {
    render(
      <MultiSelect
        options={mockFlatOptions}
        value={['option1', 'option2', 'option3']}
        maxDisplayCount={2}
        onValueChange={vi.fn()}
      />
    );

    expect(screen.getByText('Option 1')).toBeInTheDocument();
    expect(screen.getByText('Option 2')).toBeInTheDocument();
    expect(screen.getByText('+1 more')).toBeInTheDocument();
  });

  it('is disabled when disabled prop is true', () => {
    render(
      <MultiSelect
        options={mockFlatOptions}
        disabled={true}
        onValueChange={vi.fn()}
      />
    );

    const trigger = screen.getByRole('combobox');
    expect(trigger).toBeDisabled();
  });

  it('handles grouped options', () => {
    render(
      <MultiSelect
        options={mockGroupedOptions}
        onValueChange={vi.fn()}
      />
    );

    expect(screen.getByRole('combobox')).toBeInTheDocument();
  });
});
