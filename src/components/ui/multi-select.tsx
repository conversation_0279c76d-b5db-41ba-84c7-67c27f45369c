import React, { useId, Fragment, useState } from 'react';

import { XIcon, CheckIcon, ChevronDownIcon } from 'lucide-react';

import { cn } from '@/lib/utils';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandItem,
  CommandList,
  CommandEmpty,
  CommandGroup,
  CommandInput,
} from '@/components/ui/command';
import { Label } from '@/components/ui/label';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';

export interface MultiSelectOption {
  value: string;
  label: string;
  icon?: React.ReactNode;
}

export interface MultiSelectGroup {
  groupLabel: string;
  items: MultiSelectOption[];
}

export interface MultiSelectProps {
  label?: string;
  placeholder?: string;
  searchPlaceholder?: string;
  options: MultiSelectGroup[] | MultiSelectOption[];
  value?: string[];
  onValueChange?: (values: string[]) => void;
  maxDisplayCount?: number;
  disabled?: boolean;
  required?: boolean;
  className?: string;
  popoverClassName?: string;
  showClearAll?: boolean;
  clearAllText?: string;
  emptyText?: string;
}

export function MultiSelect({
  label,
  placeholder = 'Select options',
  searchPlaceholder = 'Search options...',
  options,
  value = [],
  onValueChange,
  maxDisplayCount = 3,
  disabled = false,
  required = false,
  className,
  popoverClassName,
  showClearAll = true,
  clearAllText = 'Clear all selections',
  emptyText = 'No option found.',
}: MultiSelectProps) {
  const id = useId();
  const [open, setOpen] = useState<boolean>(false);

  const normalizedOptions: MultiSelectGroup[] =
    Array.isArray(options) && 'groupLabel' in options[0]
      ? (options as MultiSelectGroup[])
      : [{ groupLabel: '', items: options as MultiSelectOption[] }];

  const allOptions = normalizedOptions.flatMap((group) => group.items);

  const handleValueChange = (newValues: string[]) => {
    onValueChange?.(newValues);
  };

  const handleSelect = (selectedValue: string) => {
    const newValues = value.includes(selectedValue)
      ? value.filter((val) => val !== selectedValue)
      : [...value, selectedValue];

    handleValueChange(newValues);
  };

  const handleRemove = (valueToRemove: string) => {
    const newValues = value.filter((val) => val !== valueToRemove);
    handleValueChange(newValues);
  };

  const handleClearAll = () => {
    handleValueChange([]);
  };

  return (
    <div className='space-y-2'>
      {label && (
        <Label
          htmlFor={id}
          className={cn(
            required &&
              "after:ml-0.5 after:text-destructive after:content-['*']"
          )}
        >
          {label}
        </Label>
      )}

      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            id={id}
            variant='outline'
            role='combobox'
            aria-expanded={open}
            disabled={disabled}
            className={cn(
              'h-auto w-full justify-between border-input bg-background px-3 font-normal outline-none outline-offset-0 hover:bg-background focus-visible:outline-[3px]',
              className
            )}
          >
            {value.length > 0 ? (
              <div className='flex min-w-0 flex-1 flex-wrap items-center gap-1'>
                {value.slice(0, maxDisplayCount).map((selectedValue) => {
                  const item = allOptions.find(
                    (item) => item.value === selectedValue
                  );
                  return (
                    <Badge
                      key={selectedValue}
                      variant='secondary'
                      className='flex items-center gap-1 text-xs'
                    >
                      {item?.icon && (
                        <span className='text-sm leading-none'>
                          {item.icon}
                        </span>
                      )}
                      <span className='max-w-20 truncate'>{item?.label}</span>
                      <button
                        type='button'
                        onClick={(e) => {
                          e.stopPropagation();
                          handleRemove(selectedValue);
                        }}
                        className='ml-1 rounded-full p-0.5 hover:bg-muted-foreground/20'
                        disabled={disabled}
                      >
                        <XIcon size={12} />
                      </button>
                    </Badge>
                  );
                })}
                {value.length > maxDisplayCount && (
                  <Badge variant='outline' className='text-xs'>
                    +{value.length - maxDisplayCount} more
                  </Badge>
                )}
              </div>
            ) : (
              <span className='text-muted-foreground'>{placeholder}</span>
            )}
            <ChevronDownIcon
              size={16}
              className='shrink-0 text-muted-foreground/80'
              aria-hidden='true'
            />
          </Button>
        </PopoverTrigger>

        <PopoverContent
          className={cn(
            'w-full min-w-[var(--radix-popper-anchor-width)] border-input p-0',
            popoverClassName
          )}
          align='start'
        >
          <Command>
            <CommandInput placeholder={searchPlaceholder} />

            <CommandList>
              <CommandEmpty>{emptyText}</CommandEmpty>

              {normalizedOptions.map((group) => (
                <Fragment key={group.groupLabel}>
                  {group.groupLabel && (
                    <CommandGroup heading={group.groupLabel}>
                      {group.items.map((item) => (
                        <CommandItem
                          key={item.value}
                          value={item.value}
                          onSelect={() => handleSelect(item.value)}
                        >
                          {item.icon && (
                            <span className='mr-2 text-lg leading-none'>
                              {item.icon}
                            </span>
                          )}
                          {item.label}
                          {value.includes(item.value) && (
                            <CheckIcon size={16} className='ml-auto' />
                          )}
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  )}

                  {!group.groupLabel &&
                    group.items.map((item) => (
                      <CommandItem
                        key={item.value}
                        value={item.value}
                        onSelect={() => handleSelect(item.value)}
                      >
                        {item.icon && (
                          <span className='mr-2 text-lg leading-none'>
                            {item.icon}
                          </span>
                        )}
                        {item.label}
                        {value.includes(item.value) && (
                          <CheckIcon size={16} className='ml-auto' />
                        )}
                      </CommandItem>
                    ))}
                </Fragment>
              ))}
            </CommandList>

            {showClearAll && value.length > 0 && (
              <CommandItem asChild className='px-2'>
                <Button
                  variant='ghost'
                  size='sm'
                  onClick={handleClearAll}
                  disabled={disabled}
                >
                  {clearAllText}
                </Button>
              </CommandItem>
            )}
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
}
