import React, { useState } from 'react';

import { MultiSelect, type MultiSelectGroup, type MultiSelectOption } from './multi-select';

// Example 1: Flat options (simple list)
const flatOptions: MultiSelectOption[] = [
  { value: 'react', label: 'React', icon: '⚛️' },
  { value: 'vue', label: 'Vue.js', icon: '💚' },
  { value: 'angular', label: 'Angular', icon: '🅰️' },
  { value: 'svelte', label: 'Svelte', icon: '🧡' },
  { value: 'nextjs', label: 'Next.js', icon: '▲' },
];

// Example 2: Grouped options (with categories)
const groupedOptions: MultiSelectGroup[] = [
  {
    groupLabel: 'Frontend Frameworks',
    items: [
      { value: 'react', label: 'React', icon: '⚛️' },
      { value: 'vue', label: 'Vue.js', icon: '💚' },
      { value: 'angular', label: 'Angular', icon: '🅰️' },
      { value: 'svelte', label: 'Svelte', icon: '🧡' },
    ],
  },
  {
    groupLabel: 'Backend Frameworks',
    items: [
      { value: 'express', label: 'Express.js', icon: '🚀' },
      { value: 'nestjs', label: 'NestJS', icon: '🐱' },
      { value: 'fastify', label: 'Fastify', icon: '⚡' },
      { value: 'koa', label: 'Koa.js', icon: '🥥' },
    ],
  },
  {
    groupLabel: 'Databases',
    items: [
      { value: 'mongodb', label: 'MongoDB', icon: '🍃' },
      { value: 'postgresql', label: 'PostgreSQL', icon: '🐘' },
      { value: 'mysql', label: 'MySQL', icon: '🐬' },
      { value: 'redis', label: 'Redis', icon: '🔴' },
    ],
  },
];

// Example 3: Countries and regions (like the original use case)
const regionsAndCountriesOptions: MultiSelectGroup[] = [
  {
    groupLabel: 'North America',
    items: [
      { value: 'us', label: 'United States', icon: '🇺🇸' },
      { value: 'ca', label: 'Canada', icon: '🇨🇦' },
      { value: 'mx', label: 'Mexico', icon: '🇲🇽' },
    ],
  },
  {
    groupLabel: 'Europe',
    items: [
      { value: 'gb', label: 'United Kingdom', icon: '🇬🇧' },
      { value: 'fr', label: 'France', icon: '🇫🇷' },
      { value: 'de', label: 'Germany', icon: '🇩🇪' },
      { value: 'es', label: 'Spain', icon: '🇪🇸' },
      { value: 'it', label: 'Italy', icon: '🇮🇹' },
    ],
  },
  {
    groupLabel: 'Asia',
    items: [
      { value: 'jp', label: 'Japan', icon: '🇯🇵' },
      { value: 'kr', label: 'South Korea', icon: '🇰🇷' },
      { value: 'cn', label: 'China', icon: '🇨🇳' },
      { value: 'in', label: 'India', icon: '🇮🇳' },
    ],
  },
];

export function MultiSelectExamples() {
  const [selectedTechnologies, setSelectedTechnologies] = useState<string[]>([]);
  const [selectedFrameworks, setSelectedFrameworks] = useState<string[]>(['react', 'express']);
  const [selectedRegions, setSelectedRegions] = useState<string[]>([]);

  return (
    <div className="space-y-8 p-6 max-w-2xl mx-auto">
      <h1 className="text-2xl font-bold">MultiSelect Component Examples</h1>
      
      {/* Example 1: Basic usage with flat options */}
      <div className="space-y-4">
        <h2 className="text-lg font-semibold">Example 1: Basic Usage (Flat Options)</h2>
        <MultiSelect
          label="Select Technologies"
          placeholder="Choose your favorite technologies"
          searchPlaceholder="Search technologies..."
          options={flatOptions}
          value={selectedTechnologies}
          onValueChange={setSelectedTechnologies}
          maxDisplayCount={2}
          required
        />
        <p className="text-sm text-muted-foreground">
          Selected: {selectedTechnologies.length > 0 ? selectedTechnologies.join(', ') : 'None'}
        </p>
      </div>

      {/* Example 2: Grouped options with pre-selected values */}
      <div className="space-y-4">
        <h2 className="text-lg font-semibold">Example 2: Grouped Options (Pre-selected)</h2>
        <MultiSelect
          label="Development Stack"
          placeholder="Select your development stack"
          searchPlaceholder="Search frameworks and tools..."
          options={groupedOptions}
          value={selectedFrameworks}
          onValueChange={setSelectedFrameworks}
          maxDisplayCount={3}
          clearAllText="Clear entire stack"
        />
        <p className="text-sm text-muted-foreground">
          Selected: {selectedFrameworks.length > 0 ? selectedFrameworks.join(', ') : 'None'}
        </p>
      </div>

      {/* Example 3: Countries and regions (original use case) */}
      <div className="space-y-4">
        <h2 className="text-lg font-semibold">Example 3: Regions and Countries</h2>
        <MultiSelect
          label="Target Markets"
          placeholder="Select regions or countries"
          searchPlaceholder="Search regions or countries..."
          options={regionsAndCountriesOptions}
          value={selectedRegions}
          onValueChange={setSelectedRegions}
          maxDisplayCount={4}
          emptyText="No regions or countries found."
          clearAllText="Clear all selections"
        />
        <p className="text-sm text-muted-foreground">
          Selected: {selectedRegions.length > 0 ? selectedRegions.join(', ') : 'None'}
        </p>
      </div>

      {/* Example 4: Disabled state */}
      <div className="space-y-4">
        <h2 className="text-lg font-semibold">Example 4: Disabled State</h2>
        <MultiSelect
          label="Disabled Field"
          placeholder="This field is disabled"
          options={flatOptions}
          value={['react', 'vue']}
          onValueChange={() => {}}
          disabled
        />
      </div>

      {/* Example 5: Custom styling */}
      <div className="space-y-4">
        <h2 className="text-lg font-semibold">Example 5: Custom Styling</h2>
        <MultiSelect
          label="Custom Styled Field"
          placeholder="Custom placeholder text"
          searchPlaceholder="Type to search..."
          options={flatOptions}
          value={[]}
          onValueChange={() => {}}
          maxDisplayCount={1}
          className="border-2 border-blue-300 focus:border-blue-500"
          popoverClassName="border-blue-300"
          showClearAll={false}
          emptyText="Oops! Nothing found here."
        />
      </div>

      {/* Example 6: Form integration example */}
      <div className="space-y-4">
        <h2 className="text-lg font-semibold">Example 6: Form Integration</h2>
        <form className="space-y-4">
          <MultiSelect
            label="Required Field"
            placeholder="This field is required"
            options={flatOptions}
            value={[]}
            onValueChange={() => {}}
            required
          />
          <button 
            type="submit" 
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Submit Form
          </button>
        </form>
      </div>
    </div>
  );
}

// Usage in a form with validation
export function FormExample() {
  const [formData, setFormData] = useState({
    technologies: [] as string[],
    regions: [] as string[],
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Form submitted:', formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6 p-6 max-w-md mx-auto">
      <h2 className="text-xl font-bold">Campaign Form Example</h2>
      
      <MultiSelect
        label="Technologies"
        placeholder="Select technologies"
        options={flatOptions}
        value={formData.technologies}
        onValueChange={(values) => 
          setFormData(prev => ({ ...prev, technologies: values }))
        }
        required
      />

      <MultiSelect
        label="Target Regions"
        placeholder="Select target regions"
        options={regionsAndCountriesOptions}
        value={formData.regions}
        onValueChange={(values) => 
          setFormData(prev => ({ ...prev, regions: values }))
        }
      />

      <button 
        type="submit"
        className="w-full px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
      >
        Create Campaign
      </button>
    </form>
  );
}
