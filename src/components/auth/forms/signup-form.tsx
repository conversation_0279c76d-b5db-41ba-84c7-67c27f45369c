'use client';

import { createUserWithEmailAndPassword } from 'firebase/auth';
import { signIn } from 'next-auth/react';
import { useTranslations } from 'next-intl';
import React, { useState, useEffect } from 'react';
import { toast } from 'sonner';

import { Eye, EyeOff } from 'lucide-react';

import { cn } from '@/lib/utils';

import { auth } from '@/firebase/config';
import { useRouter } from '@/i18n/routing';
import { Link } from '@/i18n/routing';

import { Icons } from '@/components/shared/icons';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

export function SignupForm({
  className,
  ...props
}: React.ComponentProps<'div'>) {
  const [email, setEmail] = useState('');
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [passwordConfirm, setPasswordConfirm] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [erreur, setErreur] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showPasswordConfirm, setShowPasswordConfirm] = useState(false);
  const [isPolicyAccepted, setIsPolicyAccepted] = useState(false);
  const router = useRouter();

  useEffect(() => {
    setEmail('');
    setPassword('');
  }, []);

  const handleGoogleSignIn = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    signIn('google', { callbackUrl: '/dashboard' });
  };

  const handleLinkedinSignIn = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    signIn('linkedin', { callbackUrl: '/dashboard' });
  };

  const handleMicrosoftSignIn = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    signIn('microsoft', { callbackUrl: '/dashboard' });
  };

  const handleSignUp = async () => {
    setIsLoading(true);
    setErreur('');

    if (!email || !password || !passwordConfirm || !username) {
      setErreur(t('signup_toast.error_empty_field'));
      setIsLoading(false);
      return;
    }
    if (password !== passwordConfirm) {
      setErreur(t('signup_toast.error_passwords_imcopatible'));
      setIsLoading(false);
      return;
    }

    if (!isPolicyAccepted) {
      setErreur(t('signup_toast.error_policy_accepted'));
      setIsLoading(false);
      return;
    }
    try {
      const userAuth = await createUserWithEmailAndPassword(
        auth,
        email,
        password
      );

      console.log(userAuth);

      setEmail('');
      setPassword('');

      toast.success(t('signup_toast.success'));

      setTimeout(() => {
        router.push('/dashboard');
      }, 1000);
    } catch (error) {
      console.error("Erreur d'inscription:", error);

      if (typeof error === 'object' && error !== null && 'code' in error) {
        const firebaseError = error as { code: string };

        if (firebaseError.code === 'auth/email-already-in-use') {
          setErreur(t('signup_toast.error_email_used'));
        } else if (firebaseError.code === 'auth/weak-password') {
          setErreur(t('signup_toast.error_weak_password'));
        } else if (firebaseError.code === 'auth/invalid-email') {
          setErreur(t('signup_toast.error_email_invalid'));
        } else {
          setErreur(t('signup_toast.error_system'));
        }
      } else {
        setErreur(t('signup_toast.error_inconnue'));
      }
    } finally {
      setIsLoading(false);
    }
  };

  const t = useTranslations();

  return (
    <div className={cn('flex flex-col gap-3', className)} {...props}>
      <form onSubmit={(e) => e.preventDefault()}>
        <div className='flex flex-col gap-2'>
          <h1 className='text-2xl font-bold'>{t('title_signup')}</h1>
          <div className='grid gap-2'>
            <Label htmlFor='username'>{t('form.username')}</Label>
            <Input
              id='username'
              type='username'
              placeholder='username'
              autoComplete='off'
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              required
            />
          </div>
          <div className='grid gap-2'>
            <Label htmlFor='email'>{t('form.email')}</Label>
            <Input
              id='email'
              type='email'
              placeholder='<EMAIL>'
              autoComplete='off'
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
            />
          </div>
          <div className='grid gap-2'>
            <Label htmlFor='password'>{t('form.password')}</Label>
            <div className='relative'>
              <Input
                id='password'
                type={showPassword ? 'text' : 'password'}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                autoComplete='new-password'
                placeholder='********'
                className='pr-10'
              />
              <button
                type='button'
                className='absolute end-2 top-1/2 -translate-y-1/2 transform'
                onClick={() => setShowPassword((prev) => !prev)}
              >
                {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
              </button>
            </div>
          </div>
          <div className='grid gap-2'>
            <Label htmlFor='confirmpassword'>
              {t('form.confirm_password')}
            </Label>
            <div className='relative'>
              <Input
                id='confirm-password'
                type={showPasswordConfirm ? 'text' : 'password'}
                autoComplete='new-password'
                value={passwordConfirm}
                onChange={(e) => setPasswordConfirm(e.target.value)}
                required
                placeholder='********'
              />
              <button
                type='button'
                className='absolute end-2 top-1/2 -translate-y-1/2 transform'
                onClick={() => setShowPasswordConfirm((prev) => !prev)}
              >
                {showPasswordConfirm ? <EyeOff size={20} /> : <Eye size={20} />}
              </button>
            </div>
          </div>
          {erreur && <p style={{ color: 'red', fontSize: '12px' }}>{erreur}</p>}
          <div className='flex justify-between'>
            <div className='flex items-center gap-4 py-2 text-xs text-muted-foreground'>
              <Checkbox
                id='private-policy'
                onCheckedChange={(checked) => setIsPolicyAccepted(!!checked)}
              />
              <Label htmlFor='private-policy' className='cursor-pointer'>
                {t('I_agree_with_the')}{' '}
                <Link
                  href='#'
                  className='text-primary underline underline-offset-4'
                >
                  {t('Terms')}
                </Link>{' '}
                {t('and')}{' '}
                <Link
                  href='#'
                  className='text-primary underline underline-offset-4'
                >
                  {t('Privacy')}
                </Link>{' '}
              </Label>
            </div>
          </div>
          <Button
            className='w-full'
            onClick={handleSignUp}
            disabled={isLoading}
            type='submit'
          >
            {isLoading ? 'signing up...' : t('signup')}
          </Button>
          <div className='relative text-center text-sm after:absolute after:inset-0 after:top-1/2 after:z-0 after:flex after:items-center after:border-t after:border-border'>
            <span className='relative z-10 bg-background px-2 text-muted-foreground'>
              {t('or_continue_with')}
            </span>
          </div>
          <div className='grid grid-cols-3 gap-4'>
            <Button
              variant='outline'
              className='w-full'
              onClick={handleGoogleSignIn}
              disabled={isLoading}
            >
              <Icons.googleOfficial />
              <span className='text-xs'>{t('google')}</span>
            </Button>
            <Button
              variant='outline'
              className='w-full'
              onClick={handleMicrosoftSignIn}
              disabled={isLoading}
            >
              <Icons.microsoftOfficial />
              <span className='text-xs'>{t('microsoft')}</span>
            </Button>
            <Button
              variant='outline'
              className='w-full'
              onClick={handleLinkedinSignIn}
              disabled={isLoading}
            >
              <Icons.linkedin />
              <span className='text-xs'>{t('linkedin')}</span>
            </Button>
          </div>
          <div className='flex items-center justify-center gap-2 text-sm text-muted-foreground'>
            <span>{t('Already_have_an_account')}</span>
            <Link
              href='/login'
              className='text-primary underline underline-offset-4'
            >
              {t('login')}
            </Link>
          </div>
        </div>
      </form>
    </div>
  );
}
