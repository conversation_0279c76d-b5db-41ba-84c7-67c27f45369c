'use client';
import { confirmPasswordReset } from 'firebase/auth';
import { useTranslations } from 'next-intl';
import { useSearchParams } from 'next/navigation';
import { useState } from 'react';
import { toast } from 'sonner';

import { auth } from '@/firebase/config';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

export default function NewPassword() {
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const searchParams = useSearchParams();

  const oobCode = searchParams.get('oobCode');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      if (!oobCode) throw new Error(t('new_password_toast.error_oob_code'));
      if (password.length < 6)
        throw new Error(t('new_password_toast.error_password_min_length'));

      await confirmPasswordReset(auth, oobCode, password);
      toast.success(t('new_password_toast.success'));
      // Rediriger vers la page de connexion
    } catch (error: unknown) {
      console.error('Erreur:', error);
      const errorMessage =
        error instanceof Error
          ? error.message
          : t('new_password_toast.error_unknown');
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const t = useTranslations();

  return (
    <div className='flex min-h-screen items-center justify-center'>
      <form onSubmit={handleSubmit} className='w-full max-w-sm space-y-4'>
        <h1 className='text-center text-2xl font-bold'>{t('new_password')}</h1>

        <Input
          type='password'
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          placeholder={t('imput_new_passord')}
          required
          minLength={6}
        />

        <Button type='submit' className='w-full' disabled={isLoading}>
          {isLoading ? t('treatment') : t('reset_password')}
        </Button>
      </form>
    </div>
  );
}
