'use client';

import { sendPasswordResetEmail } from 'firebase/auth';
import { useTranslations } from 'next-intl';
import { useSearchParams } from 'next/navigation';
import { toast } from 'sonner';

import { auth } from '@/firebase/config';
import { Link } from '@/i18n/routing';

import { Button } from '@/components/ui/button';

export default function EmailConfirmation() {
  const searchParams = useSearchParams();
  const email = searchParams.get('email'); // Récupère l'email depuis l'URL

  const handleResend = async () => {
    if (
      !email ||
      typeof email !== 'string' ||
      !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)
    ) {
      toast.error(t('email_confirm.toast_error_email'));
      return;
    }

    try {
      await sendPasswordResetEmail(auth, email, {
        url: `${window.location.origin}/new-password`, // URL de redirection après réinitialisation
        handleCodeInApp: true,
      });

      toast.success(t('email_confirm.toast_success'));
    } catch (error: unknown) {
      if (error instanceof Error) {
        return toast.error(t('email_confirm.toast_error'));
      }
    }
  };
  const t = useTranslations();
  return (
    <div className='flex min-h-screen items-center justify-center bg-gray-50'>
      <div className='w-full max-w-md rounded-lg bg-white p-8 text-center shadow-md'>
        <h1 className='mb-4 text-2xl font-bold'>{t('email_confirm.title')}</h1>

        <p className='mb-6 text-gray-600'>
          {t('email_confirm.subtitle')}{' '}
          <span className='block font-medium text-blue-600'>
            {email || t('email_confirm.email')}
          </span>
          {t('email_confirm.subtitle_')}{' '}
        </p>

        <div className='mt-6'>
          <Button
            onClick={handleResend}
            className='font-medium text-blue-600 hover:text-blue-800'
            variant='ghost'
          >
            {t('email_confirm.button_resend')}{' '}
          </Button>
        </div>

        <Link
          href='/login'
          className='mt-8 text-sm text-gray-600 hover:text-gray-800'
        >
          {t('email_confirm.button_back')}{' '}
        </Link>
      </div>
    </div>
  );
}
