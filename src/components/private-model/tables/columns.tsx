'use client';

import type { FilterFn, ColumnDef } from '@tanstack/react-table';

import { format, isSameDay } from 'date-fns';
import { useTranslations } from 'next-intl';
import { useCallback } from 'react';

import { PrivateModelResponse } from '@/api/models/dtos/ai-model.dto';

import { cn } from '@/lib/utils';

import { Link, useRouter } from '@/i18n/routing';

import { Icons } from '@/components/shared/icons';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';

interface ColumnsProps {
  t: ReturnType<typeof useTranslations>;
  onStar: (id: string) => Promise<void>;
  onDelete: (id: string) => Promise<void>;
}

export const usePrivateModelColumns = ({
  t,
  onStar,
  onDelete,
}: ColumnsProps) => {
  const { push } = useRouter();

  const multiColumnFilterFn: FilterFn<PrivateModelResponse> = useCallback(
    (row, columnId, filterValue) => {
      const searchableRowContent =
        `${row.original.ModelName} ${row.original.ModelDescription}`.toLowerCase();
      return searchableRowContent.includes((filterValue ?? '').toLowerCase());
    },
    []
  );

  const typeFilterFn: FilterFn<PrivateModelResponse> = useCallback(
    (row, columnId, filterValue: string[]) => {
      if (!filterValue?.length) return true;
      const type = row.getValue<string>(columnId);
      return filterValue.includes(type);
    },
    []
  );

  const columns: ColumnDef<PrivateModelResponse>[] = [
    {
      header: t('name'),
      accessorKey: 'ModelName',
      cell: ({ row }) => (
        <Link
          href={`/dashboard/private-model/${row.original.Id}`}
          className='truncate font-medium hover:underline'
        >
          {row.getValue('ModelName')}
        </Link>
      ),
      size: 200,
      filterFn: multiColumnFilterFn,
      enableHiding: false,
    },
    {
      header: t('description'),
      accessorKey: 'ModelDescription',
      size: 300,
    },
    {
      header: t('creation_Date'),
      accessorKey: 'CreationTimestamp',
      size: 120,
      cell: ({ row }) => {
        return format(new Date(row.getValue('CreationTimestamp')), 'PP');
      },
      filterFn: (row, columnId, filterValue) => {
        const cellDate = new Date(row.getValue(columnId));
        return filterValue ? isSameDay(cellDate, filterValue) : true;
      },
    },
    {
      header: t('type'),
      accessorKey: 'ModelType',
      cell: ({ row }) => (
        <Badge
          className={cn(
            'rounded-xl font-normal',
            row.getValue('ModelType') === 'IMAGE'
              ? 'bg-[#214784] text-[#ffffff]'
              : 'bg-[#3776DC] text-[#ffffff]'
          )}
        >
          {row.getValue('ModelType')}
        </Badge>
      ),
      size: 100,
      filterFn: typeFilterFn,
    },
    {
      header: t('created_by'),
      accessorKey: 'UserEmail',
      size: 180,
    },
    {
      id: 'actions',
      header: t('actions'),
      cell: ({ row }) => {
        const isFavorite = row.original.Favorite;

        return (
          <div className='flex gap-2'>
            <Button
              variant='ghost'
              size='setting'
              className='text-muted-foreground hover:text-primary'
              onClick={() => {
                push(`/dashboard/private-model/${row.original.Id}`);
              }}
            >
              <Icons.eye />
            </Button>

            <Button
              variant='ghost'
              size='setting'
              onClick={async () => await onStar(row.original.Id)}
              className={cn({
                'text-muted-foreground hover:text-[#F5A524]': !isFavorite,
                'text-[#F5A524] hover:text-muted-foreground': isFavorite,
              })}
            >
              <Icons.star fill={isFavorite ? 'currentColor' : 'white'} />
            </Button>

            <Button
              variant='ghost'
              size='icon'
              className='size-8 text-muted-foreground hover:text-destructive'
              onClick={async () => await onDelete(row.original.Id)}
            >
              <Icons.trash />
            </Button>
          </div>
        );
      },
      size: 60,
      enableHiding: false,
    },
  ];

  return columns;
};
