'use client';

import { useTranslations } from 'next-intl';
import Image from 'next/image';

import { useGetPrivateModelDetail } from '@/api/hooks/ai/queries';

import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
interface PrivateModelDetailProps {
  modelId: string;
}

export default function PrivateModelDetail({
  modelId,
}: PrivateModelDetailProps) {
  const {
    data: privateModelDetail,
    isLoading,
    isError,
  } = useGetPrivateModelDetail(modelId);
  const t = useTranslations();

  if (isLoading) {
    return (
      <div className='flex h-full flex-col space-y-4 p-4'>
        <Skeleton className='h-8 w-1/3' />
        <Skeleton className='h-6 w-1/4' />
        <Skeleton className='h-64 w-full' />
      </div>
    );
  }

  if (isError || !privateModelDetail) {
    return (
      <div className='flex h-full items-center justify-center text-red-500'>
        {t('details.error_loading_model')}
      </div>
    );
  }

  return (
    <div className='flex h-full flex-col space-y-4 p-4'>
      <h1 className='text-2xl font-bold text-gray-900'>
        {t('details.private_model_details')}
      </h1>
      <div className='rounded-lg border bg-white p-6 shadow-sm'>
        <div className='grid grid-cols-1 gap-4 md:grid-cols-3'>
          <div className='space-y-4'>
            <p className='font-semibold'>{privateModelDetail.ModelName}</p>
            <p className='text-gray-700'>
              {privateModelDetail.ModelDescription}
            </p>
          </div>
          <div className='space-y-4'>
            <div className='flex gap-2'>
              <span className='font-medium'>{t('details.creation_date')}</span>
              <span className='text-gray-600'>
                {new Date(
                  privateModelDetail.CreationTimestamp
                ).toLocaleString()}
              </span>
            </div>
            <div className='flex gap-2'>
              <span className='font-medium'>{t('details.created_by')}</span>
              <span className='text-gray-600'>
                {privateModelDetail.UserEmail}
              </span>
            </div>
            <div className='flex gap-2'>
              <span className='font-medium'>{t('details.model_status')}</span>
              <Badge className='bg-green-100 text-green-800 hover:bg-green-200'>
                Succeeded
              </Badge>
            </div>
          </div>
          <div className='flex gap-2'>
            <span className='font-medium'>{t('details.tags')}</span>
            <div className='flex flex-wrap items-baseline justify-end gap-2'>
              {' '}
              {privateModelDetail.ImageModelRequest?.Tags?.map((tag, index) => (
                <Badge
                  key={index}
                  className='bg-gray-100 py-1 text-sm text-gray-800 hover:bg-gray-200'
                >
                  {tag}
                </Badge>
              )) || (
                <span className='text-gray-400'>{t('details.no_tags')}</span>
              )}
            </div>
          </div>
        </div>
      </div>
      <div>
        {privateModelDetail.ModelType === 'IMAGE' ? (
          <>
            <h1 className='text-2xl font-bold text-gray-900'>
              {t('details.images')}
            </h1>
            <div className='grid grid-cols-2 gap-4 p-4 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6'>
              {privateModelDetail.ImageModelRequest.ImageList?.map(
                (item, index) => (
                  <Card key={index} className='overflow-hidden'>
                    <Image
                      src={item.CleanPublicUrl || item.RawPublicUrl}
                      alt={`Brand Image ${index + 1}`}
                      width={500}
                      height={500}
                      className='h-auto w-full object-cover'
                    />
                    <CardContent className='p-4'>
                      <p className='text-xs leading-snug text-muted-foreground'>
                        {item.Caption}
                      </p>
                    </CardContent>
                  </Card>
                )
              )}
            </div>
          </>
        ) : (
          <>
            <h1 className='px-4 text-2xl font-bold text-gray-900'>
              {t('details.texts')}
            </h1>
            <div className='grid grid-cols-2 gap-4 py-4 md:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5'>
              {privateModelDetail.LLMRawData?.map((item, index) => (
                <Card
                  key={index}
                  className='flex h-full flex-col justify-between'
                >
                  <div className='flex items-center justify-between px-4 py-2'>
                    <p className='text-md font-medium text-black'>
                      {item.platform}
                    </p>
                    <button className='text-xl text-gray-700 hover:text-gray-600'>
                      ×
                    </button>
                  </div>
                  <CardContent className='px-4 py-2'>
                    <p className='text-sm text-gray-800'>{item.text}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </>
        )}
      </div>
    </div>
  );
}
