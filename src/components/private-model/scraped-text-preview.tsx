import { useTranslations } from 'next-intl';
import React from 'react';

import { X } from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardHeader, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';

interface ScrapedCardPreviewProps {
  platform: string;
  text: string;
}

export function ScrapedCardPreview({
  platform,
  text,
}: ScrapedCardPreviewProps) {
  const t = useTranslations();

  return (
    <Card>
      <CardHeader className='flex flex-row items-center justify-between border-b p-4 pb-0'>
        <h3 className='text-lg font-medium capitalize'>{platform}</h3>
        <Button variant='ghost' size='icon' className='h-8 w-8 rounded-full'>
          <X className='h-4 w-4' />
          <span className='sr-only'>{t('preview.close')}</span>
        </Button>
      </CardHeader>
      <CardContent className='space-y-3 p-4 pt-4'>
        <p>{text}</p>
      </CardContent>
    </Card>
  );
}

export function ScrapedCardPreviewSkeleton() {
  return (
    <Card>
      <CardHeader className='flex flex-row items-center justify-between p-4 pb-0'>
        <Skeleton className='h-6 w-24' />
        <Skeleton className='h-8 w-8 rounded-full' />
      </CardHeader>
      <CardContent className='space-y-3 p-4 pt-4'>
        <Skeleton className='h-4 w-full' />
        <Skeleton className='h-4 w-[90%]' />
        <Skeleton className='h-4 w-full' />
        <Skeleton className='h-4 w-[95%]' />
        <Skeleton className='h-4 w-[80%]' />
        <Skeleton className='mt-2 h-4 w-[60%]' />
      </CardContent>
    </Card>
  );
}
