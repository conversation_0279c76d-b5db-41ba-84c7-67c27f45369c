'use client';

import { useTranslations } from 'next-intl';
import React from 'react';

import { preparePrivateModelImageFormOpts } from '@/config/private-model/forms-config';
import { usePrivateModelOptionsConfig } from '@/config/private-model/options-config';

import { withForm } from '@/components/shared/form';

export const ImageModelForm = withForm({
  ...preparePrivateModelImageFormOpts,
  render: function Render({ form: imageForm }) {
    const { imageCategories } = usePrivateModelOptionsConfig();
    const t = useTranslations();

    return (
      <form
        className='mt-2 flex flex-col gap-4'
        onSubmit={async (e) => {
          e.preventDefault();
          e.stopPropagation();
          await imageForm.handleSubmit();
        }}
      >
        <div className='grid gap-3'>
          <imageForm.AppField name='modelRequest.ModelName'>
            {(field) => (
              <field.TextField
                label={t('private_model_form.model_name')}
                placeholder={t('private_model_form.model_name_placeholder')}
              />
            )}
          </imageForm.AppField>
          <imageForm.AppField name='modelRequest.Description'>
            {(field) => (
              <field.TextareaField
                label={t('private_model_form.description')}
                placeholder={t('private_model_form.description_placeholder')}
                className='h-28'
              />
            )}
          </imageForm.AppField>
          <imageForm.AppField name='modelRequest.Type'>
            {(field) => (
              <field.SelectField
                label={t('private_model_form.image_category')}
                placeholder={t('private_model_form.image_category_placeholder')}
                options={imageCategories}
              />
            )}
          </imageForm.AppField>
          <imageForm.AppField name='modelRequest.Keyword'>
            {(field) => (
              <field.TextField
                label={t('private_model_form.keyword')}
                placeholder={t('private_model_form.keyword')}
              />
            )}
          </imageForm.AppField>
          <imageForm.AppField name='modelRequest.Object'>
            {(field) => (
              <field.TextField
                label={t('private_model_form.object_name')}
                placeholder={t('private_model_form.object_name')}
              />
            )}
          </imageForm.AppField>
          <imageForm.AppField name='modelRequest.Tags'>
            {(field) => (
              <field.TextFieldWithInnerTags
                showSeparateAddButton
                separateAddButtonText={t('private_model_form.add')}
                label={t('private_model_form.topics')}
                placeholder={t('private_model_form.topics')}
              />
            )}
          </imageForm.AppField>
          <imageForm.AppField name='modelRequest.ImageUrlsUpload'>
            {(field) => (
              <field.TextFieldWithInnerTags
                showSeparateAddButton
                separateAddButtonText={t('private_model_form.add')}
                label={t('private_model_form.image_urls')}
                placeholder={t('private_model_form.url')}
              />
            )}
          </imageForm.AppField>

          <imageForm.AppField name='files'>
            {(field) => (
              <field.FileUploadField
                dragAndDropText={t('private_model_form.drag_drop')}
                chooseFileLabel={t('private_model_form.link')}
                uploadText={t('private_model_form.upload')}
                deleteText={t('private_model_form.delete')}
                label={t('private_model_form.import_image')}
              />
            )}
          </imageForm.AppField>
        </div>
      </form>
    );
  },
});
