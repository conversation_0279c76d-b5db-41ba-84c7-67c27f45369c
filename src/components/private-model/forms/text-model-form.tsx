'use client';

import { useTranslations } from 'next-intl';
import React from 'react';

import { Plus, Trash2 } from 'lucide-react';

import { privateModelTextFormOpts } from '@/config/private-model/forms-config';
import { usePrivateModelOptionsConfig } from '@/config/private-model/options-config';

import { withForm } from '@/components/shared/form';
import { FieldErrors } from '@/components/shared/form/field-errors';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';

export const TextModelForm = withForm({
  ...privateModelTextFormOpts,
  render: function Render({ form }) {
    const { socialMediaOptions } = usePrivateModelOptionsConfig();
    const t = useTranslations();

    return (
      <form
        className='mt-2 flex flex-col gap-4'
        onSubmit={async (e) => {
          e.preventDefault();
          e.stopPropagation();
          await form.handleSubmit();
        }}
      >
        <div className='grid gap-3'>
          <form.AppField name='ModelName'>
            {(field) => (
              <field.TextField
                label={t('private_model_form.model_name')}
                placeholder={t('private_model_form.model_name_placeholder')}
              />
            )}
          </form.AppField>
          <form.AppField name='Description'>
            {(field) => (
              <field.TextareaField
                label={t('private_model_form.description')}
                placeholder={t('private_model_form.description_placeholder')}
                className='h-28'
              />
            )}
          </form.AppField>
          <form.AppField name='Tags'>
            {(field) => (
              <field.TextFieldWithInnerTags
                showSeparateAddButton
                separateAddButtonText={t('private_model_form.add')}
                label={t('private_model_form.topics')}
                placeholder={t('private_model_form.topics')}
              />
            )}
          </form.AppField>
          <form.AppField name='SocialMediaData' mode='array'>
            {(field) => (
              <>
                <div className='flex items-center justify-between'>
                  <div>
                    <Label htmlFor='SocialMediaData'>
                      {t('private_model_form.social_media')}
                    </Label>
                    <span className='ms-0.5 text-destructive'>*</span>
                  </div>
                  <Button
                    type='button'
                    size='sm'
                    className='rounded-xl bg-blue-50 text-blue-600 hover:bg-blue-100 hover:text-blue-700'
                    iconStart={<Plus />}
                    onClick={() =>
                      field.pushValue({ platform: 'facebook', pageName: '' })
                    }
                  >
                    {t('private_model_form.add_social_media')}
                  </Button>
                </div>
                <FieldErrors meta={field.state.meta} />
                {field.state.value.map((_, index) => (
                  <div key={index} className='flex items-start gap-2'>
                    <form.AppField name={`SocialMediaData[${index}].platform`}>
                      {(subField) => (
                        <subField.SelectField
                          placeholder={t('private_model_form.select_platform')}
                          showOptionIcon
                          showOptionLabel={false}
                          options={socialMediaOptions}
                        />
                      )}
                    </form.AppField>
                    <form.AppField name={`SocialMediaData[${index}].pageName`}>
                      {(subField) => (
                        <subField.TextField
                          placeholder={t('private_model_form.page_name')}
                          wrapperClassName='flex-1'
                        />
                      )}
                    </form.AppField>
                    <Button
                      variant='destructive'
                      size='icon'
                      asChild
                      className='bg-transparent p-[0.3rem] text-destructive shadow-none hover:text-destructive-foreground'
                    >
                      <Trash2
                        onClick={() => field.removeValue(index)}
                        className='hover:rotate-6'
                      />
                    </Button>
                  </div>
                ))}
              </>
            )}
          </form.AppField>
        </div>
      </form>
    );
  },
});
