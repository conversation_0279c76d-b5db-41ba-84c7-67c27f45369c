'use client';

import Image from 'next/image';

import { X } from 'lucide-react';

import { PrepareImageModelResponse } from '@/api/models/dtos/ai-model.dto';

import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';

interface ScrapedImageCardProps {
  imageScrapedResults?: PrepareImageModelResponse;
  isLoading: boolean;
  onRemoveImage?: (index: number) => void;
}

export default function ScrapedImageCard({
  imageScrapedResults,
  isLoading,
  onRemoveImage,
}: ScrapedImageCardProps) {
  if (isLoading) {
    return (
      <div className='grid gap-1 md:grid-cols-5'>
        {[...Array(10)].map((_: unknown, i: number) => (
          <ScrapedCardPreviewSkeleton key={i} />
        ))}
      </div>
    );
  }

  return (
    <div className='grid gap-1 md:grid-cols-5'>
      {imageScrapedResults?.ImageList?.map((item, index) => (
        <Card
          className='w-full overflow-hidden border-none shadow-xl'
          key={item.RawPublicUrl}
        >
          <div className='relative'>
            <Button
              variant='ghost'
              size='icon'
              className='absolute end-2 top-2 z-10 text-white hover:bg-white/10'
              onClick={() => {
                if (onRemoveImage) {
                  onRemoveImage(index);
                }
              }}
            >
              <X className='h-4 w-4' />
            </Button>
            <div className='relative h-60 w-full overflow-hidden'>
              <Image
                src={item.RawPublicUrl}
                alt='Brand identity visual'
                fill
                sizes='100vw'
                className='object-cover'
              />
            </div>
          </div>
          <CardContent className='p-4'>
            <p className='text-lg font-medium leading-tight'>{item.Caption}</p>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

function ScrapedCardPreviewSkeleton() {
  return (
    <div className='w-full overflow-hidden border-none shadow-xl'>
      <div className='relative'>
        <div className='relative h-60 w-full overflow-hidden'>
          <Skeleton className='h-full w-full rounded-none' />
        </div>

        <Skeleton className='absolute end-2 top-2 z-10 h-8 w-8 rounded-full' />
      </div>

      <div className='space-y-2 p-4'>
        <Skeleton className='h-6 w-3/4' />
        <Skeleton className='h-4 w-1/2' />
      </div>
    </div>
  );
}
