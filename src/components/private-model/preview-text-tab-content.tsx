import React from 'react';

import { usePrepareTextPrivateModel } from '@/api/hooks/ai/queries';
import { usePrivateModelTextStore } from '@/stores/private-model-text-store';

import {
  ScrapedCardPreview,
  ScrapedCardPreviewSkeleton,
} from '@/components/private-model/scraped-text-preview';

export function PreviewTabContent() {
  const { textFormValues } = usePrivateModelTextStore();

  const { data, isLoading } = usePrepareTextPrivateModel(
    textFormValues?.SocialMediaData[0].platform,
    textFormValues?.SocialMediaData[0].pageName
  );

  return (
    <div>
      {isLoading && (
        <div className='grid gap-1 md:grid-cols-2'>
          {[...Array(20)].map((_, i) => (
            <ScrapedCardPreviewSkeleton key={i} />
          ))}
        </div>
      )}

      <div className='grid gap-1 md:grid-cols-2'>
        {data?.map((item, index) => (
          <ScrapedCardPreview key={index} {...item} />
        ))}
      </div>
    </div>
  );
}
