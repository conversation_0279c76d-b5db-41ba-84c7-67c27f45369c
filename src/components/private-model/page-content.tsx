'use client';

import { useTranslations } from 'next-intl';
import { useQueryState, parseAsStringLiteral } from 'nuqs';

import { Plus } from 'lucide-react';

import { PrivateModelForm } from '@/components/private-model/forms/private-model-form';
import { PrivateModelTable } from '@/components/private-model/tables/private-model-table';
import { Button } from '@/components/ui/button';
import { Dialog, DialogTrigger } from '@/components/ui/dialog';

export const tabNames = ['preview', 'info-general'] as const;
export const modelTypes = ['text', 'image'] as const;

export default function PageContent() {
  const t = useTranslations();
  const [tabName, setTabName] = useQueryState(
    'tabName',
    parseAsStringLiteral(tabNames)
  );
  const [modelType, setModelType] = useQueryState(
    'modelType',
    parseAsStringLiteral(modelTypes).withOptions({ history: 'replace' })
  );

  return (
    <div className='flex h-full flex-col'>
      <div className='flex items-center justify-between'>
        <h1 className='text-2xl font-bold'>{t('title_private_model_list')}</h1>
        <Dialog>
          <DialogTrigger asChild>
            <Button
              iconStart={<Plus />}
              onClick={async () => {
                const promiseUrl = Promise.all([
                  setModelType('text'),
                  setTabName('info-general'),
                ]);
                await promiseUrl;
              }}
            >
              {t('create_a_private_model')}
            </Button>
          </DialogTrigger>
          <PrivateModelForm
            tabName={tabName}
            modelType={modelType}
            setTabName={setTabName}
            setModelType={setModelType}
          />
        </Dialog>
      </div>

      <p className='text-gray-0 my-2'>{t('subtitle_private_model_list')}</p>

      <PrivateModelTable />
    </div>
  );
}
