import { useTranslations } from 'next-intl';

import { Laptop, Smartphone } from 'lucide-react';

import { ModeSwitcher } from '@/components/shared/mode-switcher';
import { CardTitle } from '@/components/ui/card';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';

export function PreviewHeader() {
  const t = useTranslations();

  return (
    <CardTitle className='flex items-center justify-between'>
      <h1>{t('post_suggestion_preview.title')}</h1>
      <div className='flex items-center gap-2'>
        <ModeSwitcher />
        <Tabs defaultValue='laptop-view' className='items-center'>
          <TabsList className='shadow-xs -space-x-px bg-background p-0 rtl:space-x-reverse'>
            <TabsTrigger
              value='phone-view'
              className='rounded-none border first:rounded-s last:rounded-e data-[state=active]:bg-muted data-[state=active]:text-primary'
            >
              <Smartphone size={16} aria-hidden='true' />
            </TabsTrigger>
            <TabsTrigger
              value='laptop-view'
              className='rounded-none border first:rounded-s last:rounded-e data-[state=active]:bg-muted data-[state=active]:text-primary'
            >
              <Laptop size={16} aria-hidden='true' />
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>
    </CardTitle>
  );
}
