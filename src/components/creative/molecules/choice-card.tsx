'use client';

import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { useState } from 'react';

import { cn } from '@/lib/utils';

import { useImageSelector } from '@/stores/creative-image-selector-store';

import { CategoryFilter } from '@/components/creative/molecules/category-filter';
import {
  CardTitle,
  CardHeader,
  CardContent,
  CardDescription,
} from '@/components/ui/card';
import { ScrollBar, ScrollArea } from '@/components/ui/scroll-area';

interface ChoiceCardProps {
  selectorType: 'type' | 'style' | 'effect';
}

export const ImageChoiceCard = ({ selectorType }: ChoiceCardProps) => {
  const t = useTranslations();
  const [filteredCategory, setFilteredCategory] = useState<string>('all');

  const { selectItem, isItemSelected, availableItems } =
    useImageSelector(selectorType)();

  const titleText = (() => {
    switch (selectorType) {
      case 'type':
        return t('image_choice_card.type.title');
      case 'style':
        return t('image_choice_card.style.title');
      case 'effect':
        return t('image_choice_card.effects.title');
    }
  })();

  const filteredItems =
    filteredCategory === 'all'
      ? availableItems
      : availableItems.filter((item) => item.category === filteredCategory);

  return (
    <>
      <CardHeader className='space-y-4 px-4 pb-0'>
        <CardTitle>
          <h2>{titleText}</h2>
        </CardTitle>
        <ScrollArea>
          <CardDescription className='m-0 pb-4'>
            <CategoryFilter
              selectorType={selectorType}
              onCategoryChange={setFilteredCategory}
            />
          </CardDescription>
          <ScrollBar orientation='horizontal' />
        </ScrollArea>
      </CardHeader>
      <CardContent className='space-y-4 overflow-auto p-0 px-4'>
        {filteredItems.map((category) => (
          <div key={category.category} className='space-y-2'>
            <h3 className='text-base font-medium capitalize'>
              {t(category.category)}
            </h3>
            <div className='grid grid-cols-2 gap-1 md:grid-cols-3 lg:grid-cols-7'>
              {category.content.map((item) => (
                <div
                  key={item.id}
                  className={cn(
                    'group relative flex h-36 w-full cursor-pointer flex-col items-center justify-center overflow-hidden rounded-lg border',
                    {
                      'ring-2 ring-primary': isItemSelected(item.id),
                    }
                  )}
                  onClick={() => selectItem(item, category.category)}
                >
                  <div className='relative h-36 w-full'>
                    <Image
                      src={item.src}
                      alt={item.title}
                      fill
                      sizes='(max-width: 768px) 100vw, 50vw'
                      className='object-cover transition-transform group-hover:scale-105'
                    />
                  </div>
                  <div className='absolute inset-0 flex items-end bg-gradient-to-t from-black/70 to-transparent p-3 text-white'>
                    <p className='text-xs'>{t(item.title)}</p>
                  </div>
                  {isItemSelected(item.id) && (
                    <div className='absolute start-2 top-2 flex h-5 w-5 items-center justify-center rounded-full bg-primary text-xs text-primary-foreground'>
                      ✓
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        ))}

        {filteredItems.length === 0 && (
          <div className='flex h-32 items-center justify-center text-muted-foreground'>
            No images found for this category.
          </div>
        )}
      </CardContent>
    </>
  );
};
