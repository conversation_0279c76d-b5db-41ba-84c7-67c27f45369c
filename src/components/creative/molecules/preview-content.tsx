import { WheelGesturesPlugin } from 'embla-carousel-wheel-gestures';
import { useState, useEffect } from 'react';

import { useCreativePostStore } from '@/stores/creative-post-store';

import { SocialMediaCard } from '@/components/creative/molecules/social-media-card';
import { LocalThemeWrapper } from '@/components/demo/local-theme-wrapper';
import {
  Card,
  CardHeader,
  CardFooter,
  CardContent,
} from '@/components/ui/card';
import {
  Carousel,
  CarouselApi,
  CarouselItem,
  CarouselNext,
  CarouselContent,
  CarouselPrevious,
} from '@/components/ui/carousel';
import { Skeleton } from '@/components/ui/skeleton';

export function PreviewContent() {
  const [api, setApi] = useState<CarouselApi>();

  const selectedIndex = useCreativePostStore((s) => s.selectedIndex);
  const postSuggestions = useCreativePostStore((s) => s.postSuggestions);
  const isGeneratingNewPostSuggestions = useCreativePostStore(
    (s) => s.isGeneratingNewPostSuggestions
  );

  const setSelectedIndex = useCreativePostStore((s) => s.setSelectedIndex);
  const setCurrentPostSuggestion = useCreativePostStore(
    (s) => s.setCurrentPostSuggestion
  );

  useEffect(() => {
    if (!api) return;

    const handleSelect = () => {
      const newIndex = api.selectedScrollSnap();
      setSelectedIndex(newIndex);
      if (postSuggestions) {
        setCurrentPostSuggestion(postSuggestions.results[newIndex]);
      }
    };

    api.on('select', handleSelect);

    return () => {
      api.off('select', handleSelect);
    };
  }, [api, postSuggestions, setCurrentPostSuggestion, setSelectedIndex]);

  useEffect(() => {
    if (api) {
      api.scrollTo(selectedIndex);
    }
  }, [api, selectedIndex]);

  return (
    <Carousel
      className='flex h-full w-full items-center justify-center'
      opts={{
        loop: false,
        align: 'center',
        skipSnaps: false,
        containScroll: 'trimSnaps',
        startIndex: 1,
      }}
      plugins={[
        WheelGesturesPlugin({
          forceWheelAxis: 'x',
        }),
      ]}
      setApi={setApi}
    >
      <CarouselContent className='h-full w-3/5'>
        {postSuggestions &&
        postSuggestions.results &&
        postSuggestions.results.length > 0
          ? postSuggestions.results.map((post, index) => (
              <CarouselItem
                key={`post-${index}`}
                className='flex items-center justify-center transition-all duration-300 ease-in-out'
                style={{
                  transform: `scale(${selectedIndex === index ? 0.96 : 0.85})`,
                  opacity: selectedIndex === index ? 1 : 0.7,
                  zIndex: selectedIndex === index ? 10 : 0,
                }}
              >
                <LocalThemeWrapper className='h-full w-full'>
                  <SocialMediaCard post={post} />
                </LocalThemeWrapper>
              </CarouselItem>
            ))
          : !isGeneratingNewPostSuggestions &&
            Array.from({ length: 3 }).map((_, index) => (
              <CarouselItem
                key={`placeholder-${index}`}
                className='transition-all duration-300 ease-in-out'
                style={{
                  transform: `scale(${selectedIndex === index ? 0.96 : 0.85})`,
                  opacity: selectedIndex === index ? 1 : 0.7,
                  zIndex: selectedIndex === index ? 10 : 0,
                }}
              >
                <LocalThemeWrapper className='h-full'>
                  <PreviewContentItemPlaceholder />
                </LocalThemeWrapper>
              </CarouselItem>
            ))}

        {/* Show skeleton items right after generated content when generating new suggestions */}
        {isGeneratingNewPostSuggestions &&
          Array.from({ length: 3 }).map((_, index) => (
            <CarouselItem
              key={`skeleton-${index}`}
              className='flex items-center justify-center transition-all duration-300 ease-in-out'
              style={{
                transform: `scale(${selectedIndex === (postSuggestions?.results?.length || 0) + index ? 0.96 : 0.85})`,
                opacity:
                  selectedIndex ===
                  (postSuggestions?.results?.length || 0) + index
                    ? 1
                    : 0.7,
                zIndex:
                  selectedIndex ===
                  (postSuggestions?.results?.length || 0) + index
                    ? 10
                    : 0,
              }}
            >
              <LocalThemeWrapper className='h-full w-full'>
                <PreviewContentSkeleton />
              </LocalThemeWrapper>
            </CarouselItem>
          ))}
      </CarouselContent>
      <CarouselPrevious className='start-2' />
      <CarouselNext className='end-2' />
    </Carousel>
  );
}

function PreviewContentItemPlaceholder() {
  return (
    <Card className='flex h-full flex-col shadow-xl'>
      <CardHeader className='px-3 py-2'>
        <div className='flex items-center gap-2'>
          <div className='h-10 w-10 flex-shrink-0 rounded-full bg-gray-200' />
          <div className='w-full space-y-1'>
            {Array.from({ length: 2 }).map((_, index) => (
              <div key={index} className='h-2 w-1/2 rounded-full bg-gray-200' />
            ))}
          </div>
        </div>
      </CardHeader>
      <CardContent className='flex flex-1 flex-col gap-3 overflow-auto px-3 py-0'>
        <div className='space-y-2'>
          {Array.from({ length: 3 }).map((_, index) => (
            <div key={index} className='h-2 w-full rounded-full bg-gray-200' />
          ))}
          <div className='h-2 w-3/4 rounded-full bg-gray-200' />
        </div>
        <div className='aspect-video flex-1 rounded-md bg-gray-100' />
      </CardContent>
      <CardFooter className='flex items-center justify-center gap-2 px-3 py-2.5'>
        <div className='h-8 w-full rounded-md bg-gray-100' />
      </CardFooter>
    </Card>
  );
}

function PreviewContentSkeleton() {
  return (
    <Card className='flex h-full flex-col shadow-xl'>
      <CardHeader className='px-3 py-2'>
        <div className='flex items-center gap-2'>
          <Skeleton className='h-10 w-10 flex-shrink-0 rounded-full' />
          <div className='w-full space-y-1'>
            {Array.from({ length: 2 }).map((_, index) => (
              <Skeleton key={index} className='h-2 w-1/2 rounded-full' />
            ))}
          </div>
        </div>
      </CardHeader>
      <CardContent className='flex flex-1 flex-col gap-3 overflow-auto px-3 py-0'>
        <div className='space-y-2'>
          {Array.from({ length: 3 }).map((_, index) => (
            <Skeleton key={index} className='h-2 w-full rounded-full' />
          ))}
          <Skeleton className='h-2 w-3/4 rounded-full' />
        </div>
        <Skeleton className='aspect-video flex-1 rounded-md' />
      </CardContent>
      <CardFooter className='flex items-center justify-center gap-2 px-3 py-2.5'>
        <Skeleton className='h-8 w-full rounded-md' />
      </CardFooter>
    </Card>
  );
}
