'use client';

import { useTranslations } from 'next-intl';
import { useState } from 'react';

import { cn } from '@/lib/utils';

import { useImageSelector } from '@/stores/creative-image-selector-store';

import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';

interface CategoryFilterProps {
  selectorType: 'type' | 'style' | 'effect';
  onCategoryChange: (category: string) => void;
}

export const CategoryFilter = ({
  selectorType,
  onCategoryChange,
}: CategoryFilterProps) => {
  const t = useTranslations();
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  const { availableItems } = useImageSelector(selectorType)();

  const categories = [
    'all',
    ...Array.from(new Set(availableItems.map((item) => item.category))),
  ];

  const displayCategoryName = (category: string) => {
    if (category === 'all') return t('image_choice_card.all_categories');
    return t(category);
  };

  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category);
    onCategoryChange(category);
  };

  return (
    <RadioGroup
      value={selectedCategory}
      onValueChange={handleCategoryChange}
      className='flex gap-2'
    >
      {categories.map((category) => (
        <div
          key={category}
          className={cn(
            'relative w-fit cursor-pointer rounded-full border px-3 py-1',
            selectedCategory === category
              ? 'border-primary bg-primary/10 text-primary'
              : 'border-input hover:bg-muted/50'
          )}
        >
          <RadioGroupItem
            value={category}
            id={`category-${selectorType}-${category}`}
            className='sr-only'
          />
          <Label
            htmlFor={`category-${selectorType}-${category}`}
            className='cursor-pointer text-nowrap text-sm after:absolute after:inset-0'
          >
            {displayCategoryName(category)}
          </Label>
        </div>
      ))}
    </RadioGroup>
  );
};
