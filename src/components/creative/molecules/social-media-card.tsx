'use client';

import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { useState, useEffect } from 'react';

import { ImageOff } from 'lucide-react';

import { PostSuggestion } from '@/api/models/dtos/post.dto';

import { getFallbackInitials } from '@/lib/get-fallback-initials';
import { cn } from '@/lib/utils';

import { useRelativeTimeFormatter } from '@/hooks/use-relative-time-formatter';

import { useBrandById } from '@/api/hooks/brand/queries';
import { useCreativePostStore } from '@/stores/creative-post-store';

import { SocialMediaActions } from '@/components/creative/molecules/social-media-actions';
import PdfWrapper from '@/components/shared/pdf-wrapper';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import {
  Card,
  CardFooter,
  CardHeader,
  CardContent,
} from '@/components/ui/card';
import * as Editable from '@/components/ui/editable';
import { Textarea } from '@/components/ui/textarea';

interface SocialMediaCard {
  post: PostSuggestion;
  canEdit?: boolean;
}

export function SocialMediaCard({ post, canEdit = false }: SocialMediaCard) {
  const t = useTranslations();
  const [postTitle, setPostTitle] = useState('');
  const [postDescription, setPostDescription] = useState('');

  const formatRelativeTime = useRelativeTimeFormatter();

  const setEditPostSuggestion = useCreativePostStore(
    (s) => s.setEditPostSuggestion
  );

  const { data: brand } = useBrandById(post.BrandId);

  useEffect(() => {
    if (post.Ad.message.Text) {
      setPostDescription(post.Ad.message.Text);
    }
  }, [post.Ad.message.Text]);

  useEffect(() => {
    if (post.Ad.title) {
      setPostTitle(post.Ad.title);
    }
  }, [post.Ad.title]);

  return (
    <Card className='flex h-full w-full flex-col shadow-xl'>
      <CardHeader className='px-3 py-2'>
        <div className='flex items-center gap-3'>
          <Avatar className='h-10 w-10 border'>
            <AvatarImage
              src={
                brand?.GcsLinkPublic ?? '/placeholder.svg?height=40&width=40'
              }
              alt={brand?.Description ?? t('brand_image')}
            />
            <AvatarFallback>
              {getFallbackInitials(brand?.Name ?? t('brand_name)'))}
            </AvatarFallback>
          </Avatar>
          <div>
            <h3 className='font-medium text-foreground'>
              {brand?.Name ?? t('brand_name)')}
            </h3>
            <p className='text-xs text-gray-500'>
              {formatRelativeTime(post?.CreationTimestamp.toString())}
            </p>
          </div>
        </div>
      </CardHeader>
      <CardContent className='flex flex-1 flex-col gap-3 overflow-auto px-3 pb-2'>
        <div>
          <Editable.Root
            dir={post.Ad.language === 'arabic' ? 'rtl' : 'ltr'}
            className='p-0'
            placeholder='Enter your text here'
            disabled={!canEdit}
            value={postTitle}
            onValueChange={(value) => {
              setPostTitle(value);
            }}
            onSubmit={() => {
              setEditPostSuggestion({
                ...post,
                Ad: {
                  ...post.Ad,
                  title: postTitle,
                },
              });
            }}
          >
            <Editable.Area>
              <Editable.Preview className='p-0 text-sm text-foreground' />
              <Editable.Input />
            </Editable.Area>
          </Editable.Root>

          <Editable.Root
            disabled={!canEdit}
            dir={post.Ad.language === 'arabic' ? 'rtl' : 'ltr'}
            value={postDescription}
            placeholder='Enter your text here'
            onValueChange={(value) => {
              setPostDescription(value);
            }}
            onSubmit={() => {
              setEditPostSuggestion({
                ...post,
                Ad: {
                  ...post.Ad,
                  message: {
                    ...post.Ad.message,
                    Text: postDescription,
                  },
                },
              });
            }}
          >
            <Editable.Area>
              <Editable.Preview className='line-clamp-2 text-wrap p-0 text-sm text-foreground' />
              <Editable.Input asChild>
                <Textarea
                  className={cn('resize-none p-0', {
                    'flex h-[4.35rem] items-center pt-1': canEdit,
                  })}
                  placeholder='Enter your text here'
                  value={postDescription}
                  onChange={(e) => setPostDescription(e.target.value)}
                />
              </Editable.Input>
            </Editable.Area>
          </Editable.Root>
        </div>

        <div className='flex flex-1 items-center justify-center overflow-hidden rounded-xl'>
          {post.Medias ? (
            post.AdType === 'text_for_pdf' ? (
              <PdfWrapper src={post.Medias[0]?.GcsLinkPublic} height='100%' />
            ) : (
              <Image
                className='rounded-xl'
                src={post.Medias[0]?.GcsLinkPublic}
                alt={post.Ad.message.Text}
                width={post.Medias[0]?.Width}
                height={post.Medias[0]?.Height}
                style={{
                  aspectRatio: `${post.Medias[0]?.Width}/${post.Medias[0]?.Height}`,
                }}
              />
            )
          ) : (
            <ImageOff className='size-24 text-gray-400' />
          )}
        </div>
      </CardContent>

      <CardFooter className='px-3 py-2.5'>
        <SocialMediaActions platform={post.SocialMedia} />
      </CardFooter>
    </Card>
  );
}
