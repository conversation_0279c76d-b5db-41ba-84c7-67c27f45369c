import { useTranslations } from 'next-intl';

import { Heart, Bookmark } from 'lucide-react';

import { PostSuggestion } from '@/api/models/dtos/post.dto';

import { Icons } from '@/components/shared/icons';

interface SocialMediaActionsProps {
  platform: PostSuggestion['SocialMedia'];
}

export function SocialMediaActions({ platform }: SocialMediaActionsProps) {
  switch (platform) {
    case 'facebook':
      return <FacebookActions />;
    case 'instagram':
      return <InstagramActions />;
    case 'linkedin':
      return <LinkedInActions />;
    case 'x':
      return <XActions />;
    default:
      return null;
  }
}

function FacebookActions() {
  const t = useTranslations();

  return (
    <div className='flex w-full items-center justify-between'>
      <div className='flex flex-1 items-center justify-center gap-1.5 text-foreground hover:cursor-default hover:bg-transparent hover:text-foreground'>
        <Icons.facebookLike className='size-5' />
        <span className='text-sm'>{t('social_media_actions.like')}</span>
      </div>
      <div className='flex flex-1 items-center justify-center gap-1.5 text-foreground hover:text-foreground'>
        <Icons.facebookChat className='size-5' />
        <span className='text-sm'>{t('social_media_actions.comment')}</span>
      </div>
      <div className='flex flex-1 items-center justify-center gap-1.5 text-foreground hover:text-foreground'>
        <Icons.facebookShare className='size-5' />
        <span className='text-sm'>{t('social_media_actions.share')}</span>
      </div>
    </div>
  );
}

function InstagramActions() {
  return (
    <div className='flex w-full items-center justify-between'>
      <div className='flex gap-4'>
        <div className='text-foreground'>
          <Heart strokeWidth={1.5} />
        </div>
        <div className='flex items-center text-foreground'>
          <Icons.facebookChat className='size-5' />
        </div>
      </div>
      <Bookmark strokeWidth={1.5} />
    </div>
  );
}

function LinkedInActions() {
  const t = useTranslations();

  return (
    <div className='flex w-full items-center justify-between px-8'>
      <div className='flex items-center gap-1 text-foreground'>
        <Icons.linkedinThumbsUp className='size-4' />
        <span className='text-xs'>{t('social_media_actions.like')}</span>
      </div>
      <div className='flex items-center gap-1 text-foreground'>
        <Icons.linkedinComment className='size-4' />
        <span className='text-xs'>{t('social_media_actions.comment')}</span>
      </div>
      <div className='flex items-center gap-1 text-foreground'>
        <Icons.linkedinRepost className='size-4' />
        <span className='text-xs'>{t('social_media_actions.repost')}</span>
      </div>
      <div className='flex items-center gap-1 text-foreground'>
        <Icons.linkedinSend className='size-4' />
        <span className='text-xs'>{t('social_media_actions.send')}</span>
      </div>
    </div>
  );
}

function XActions() {
  return (
    <div className='flex w-full items-center justify-between'>
      <div className='flex items-center gap-1 text-foreground'>
        <Icons.xReply className='size-5' />
      </div>
      <div className='flex items-center gap-1 text-foreground'>
        <Icons.xRepost className='size-5' />
      </div>
      <div className='flex items-center gap-1 text-foreground'>
        <Icons.xLike className='size-5' />
      </div>
      <div className='flex items-center gap-1 text-foreground'>
        <Icons.xView className='size-5' />
      </div>
      <div className='flex items-center gap-2 text-foreground'>
        <Icons.xBookmark className='size-5' />
        <Icons.xShare className='size-5' />
      </div>
    </div>
  );
}
