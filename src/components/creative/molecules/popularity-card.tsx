'use client';

import { useTranslations } from 'next-intl';
import React from 'react';

import { PostSuggestion } from '@/api/models/dtos/post.dto';

import { cn } from '@/lib/utils';

import { GaugeChart } from '@/components/creative/atoms/gauge-chart';
import { Card<PERSON><PERSON>le, CardHeader, CardContent } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { ScrollArea } from '@/components/ui/scroll-area';

interface PopularityCardProps {
  post: PostSuggestion | null;
}

export function PopularityCard({ post }: PopularityCardProps) {
  const t = useTranslations();

  return (
    <>
      <CardHeader className='p-0 px-4'>
        <CardTitle className='flex flex-col items-start justify-between gap-10'>
          <h1>{t('popularity.title')}</h1>
          <GaugeChart
            isMonoColor={!post}
            label='Score'
            value={
              post?.Score.PopularityScore
                ? Number(post?.Score.PopularityScore)
                : 0
            }
            size='sm'
            animated={true}
          />
        </CardTitle>
      </CardHeader>
      <ScrollArea>
        <CardContent className='flex-1 space-y-4 p-0 px-4 pb-4'>
          <div className='space-y-1.5'>
            <div className='flex items-center justify-between'>
              <p className='text-xs font-medium capitalize tracking-wide text-muted-foreground'>
                {t('popularity.awareness')}
              </p>
              <p className='text-xs font-medium'>
                {post ? post.Score.AwarenessScore : 0}%
              </p>
            </div>
            <Progress
              value={Number(post?.Score.AwarenessScore)}
              className='bg-[#E4E4E7]'
              progressIndicatorClassName='bg-[#30AD43]'
            />
          </div>
          <div className='space-y-1.5'>
            <div className='flex items-center justify-between'>
              <p className='text-xs font-medium capitalize tracking-wide text-muted-foreground'>
                {t('popularity.consideration')}
              </p>
              <p className='text-xs font-medium'>
                {post ? post.Score.ConsiderationScore : 0}%
              </p>
            </div>
            <Progress
              value={Number(post?.Score.ConsiderationScore)}
              className='bg-[#E4E4E7]'
              progressIndicatorClassName='bg-[#84BD32]'
            />
          </div>
          <div className='space-y-1.5'>
            <div className='flex items-center justify-between'>
              <p className='text-xs font-medium capitalize tracking-wide text-muted-foreground'>
                {t('popularity.conversion')}
              </p>
              <p className='text-xs font-medium'>
                {post ? post.Score.ConversionScore : 0}%
              </p>
            </div>
            <Progress
              value={Number(post?.Score.ConversionScore)}
              className='bg-[#E4E4E7]'
              progressIndicatorClassName='bg-[#FF8888]'
            />
          </div>
          <div className='space-y-1.5'>
            <div className='flex items-center justify-between'>
              <p className='text-xs font-medium capitalize tracking-wide text-muted-foreground'>
                {t('popularity.loyalty')}
              </p>
              <p className='text-xs font-medium'>
                {post ? post.Score.LoyaltyScore : 0}%
              </p>
            </div>
            <Progress
              value={Number(post?.Score.LoyaltyScore)}
              className='bg-[#E4E4E7]'
              progressIndicatorClassName='bg-[#D1D80F]'
            />
          </div>

          <div
            className={cn('space-y-1.5', {
              'space-y-3': !post,
            })}
          >
            <p className='text-xs font-medium capitalize tracking-wide text-muted-foreground'>
              {t('popularity.explanation')}
            </p>
            <div
              className={cn('text-sm font-medium')}
              dir={post?.Ad.language === 'arabic' ? 'rtl' : 'ltr'}
            >
              {post ? (
                <p className='text-[0.8rem]'>{post.Score.Explanation}</p>
              ) : (
                <div className='space-y-2'>
                  {Array.from({ length: 4 }).map((_, index) => (
                    <div
                      key={index}
                      className='h-1.5 rounded-full bg-gray-200'
                    />
                  ))}
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </ScrollArea>
    </>
  );
}
