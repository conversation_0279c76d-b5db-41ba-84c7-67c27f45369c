'use client';

import React, { useRef, useEffect } from 'react';

import { cn } from '@/lib/utils';

import { useDirection } from '@/hooks/use-direction';

interface GaugeChartProps {
  value: number;
  min?: number;
  max?: number;
  label?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  animated?: boolean;
  className?: string;
  isMonoColor?: boolean;
}

export function GaugeChart({
  value,
  min = 0,
  max = 100,
  label,
  size = 'md',
  animated = true,
  isMonoColor = false,
  className,
}: GaugeChartProps) {
  const needleRef = useRef<SVGGElement>(null);
  const { isRtlLanguage } = useDirection();

  // Normalize the value within bounds
  const normalizedValue = Math.min(Math.max(value, min), max);

  // Calculate the angle based on the value (from -90 to 90 degrees)
  // For RTL, flip the direction of the gauge
  const angle = isRtlLanguage
    ? 90 - (180 * (normalizedValue - min)) / (max - min)
    : -90 + (180 * (normalizedValue - min)) / (max - min);

  // Size mappings
  const sizeMap = {
    sm: { width: 200, height: 100, strokeWidth: 6, needleBaseSize: 6 },
    md: { width: 200, height: 140, strokeWidth: 8, needleBaseSize: 8 },
    lg: { width: 300, height: 200, strokeWidth: 10, needleBaseSize: 10 },
    xl: { width: 400, height: 260, strokeWidth: 12, needleBaseSize: 12 },
  };

  const { width, height, strokeWidth, needleBaseSize } = sizeMap[size];

  // Calculate positions for the gauge arc
  const centerX = width / 2;
  const centerY = height - 5;
  const radius = height - 10;

  // Calculate start and end points for the arc
  // For RTL, we flip the start and end angles
  const startAngle = isRtlLanguage ? 0 : -180;
  const endAngle = isRtlLanguage ? -180 : 0;

  // Calculate points on the arc for start and end
  const startX = centerX + radius * Math.cos((startAngle * Math.PI) / 180);
  const startY = centerY + radius * Math.sin((startAngle * Math.PI) / 180);
  const endX = centerX + radius * Math.cos((endAngle * Math.PI) / 180);
  const endY = centerY + radius * Math.sin((endAngle * Math.PI) / 180);

  // Create the path for the gauge arc
  const arcPath = `M ${startX} ${startY} A ${radius} ${radius} 0 0 ${isRtlLanguage ? 0 : 1} ${endX} ${endY}`;

  // Calculate needle position
  const needleLength = radius - 10;
  const needleAngleRad = (angle * Math.PI) / 180;
  const needleX = centerX + needleLength * Math.cos(needleAngleRad);
  const needleY = centerY + needleLength * Math.sin(needleAngleRad);

  // Create the path for the needle
  const needlePath = `M ${centerX - 3} ${centerY} L ${centerX + 3} ${centerY} L ${needleX} ${needleY} Z`;

  useEffect(() => {
    if (animated && needleRef.current) {
      // Apply rotation transformation to the needle
      needleRef.current.style.transform = `rotate(${angle}deg)`;
      needleRef.current.style.transformOrigin = `${centerX}px ${centerY}px`;
      needleRef.current.style.transition = 'transform 0.5s ease-out';
    }
  }, [angle, animated, centerX, centerY]);

  // For RTL mode, use a flipped gradient
  const gradientId = isRtlLanguage ? 'gauge-gradient-rtl' : 'gauge-gradient';

  return (
    <div className={cn('flex w-full flex-col items-center gap-2', className)}>
      <svg width={'100%'} height={'100%'} viewBox={`0 0 ${width} ${height}`}>
        {/* Background gradients for both LTR and RTL */}
        <defs>
          <linearGradient id='gauge-gradient' x1='0%' y1='0%' x2='100%' y2='0%'>
            <stop offset='0%' stopColor='#f44336' />
            <stop offset='50%' stopColor='#ffeb3b' />
            <stop offset='100%' stopColor='#4caf50' />
          </linearGradient>
          <linearGradient
            id='gauge-gradient-rtl'
            x1='100%'
            y1='0%'
            x2='0%'
            y2='0%'
          >
            <stop offset='0%' stopColor='#f44336' />
            <stop offset='50%' stopColor='#ffeb3b' />
            <stop offset='100%' stopColor='#4caf50' />
          </linearGradient>
        </defs>

        {/* Arc */}
        <path
          d={arcPath}
          fill='none'
          stroke={isMonoColor ? '#E4E4E7' : `url(#${gradientId})`}
          strokeWidth={strokeWidth}
          strokeLinecap='round'
        />

        {/* Tick marks */}
        {Array.from({ length: 9 }).map((_, index) => {
          // For RTL, flip the tick angles
          const tickAngle = isRtlLanguage
            ? 0 - (index * 180) / 8
            : -180 + (index * 180) / 8;
          const tickAngleRad = (tickAngle * Math.PI) / 180;
          const outerRadius = radius - 3;
          const innerRadius = radius - (index % 2 === 0 ? 15 : 10);

          const outerX = centerX + outerRadius * Math.cos(tickAngleRad);
          const outerY = centerY + outerRadius * Math.sin(tickAngleRad);
          const innerX = centerX + innerRadius * Math.cos(tickAngleRad);
          const innerY = centerY + innerRadius * Math.sin(tickAngleRad);

          return (
            <line
              key={index}
              x1={innerX}
              y1={innerY}
              x2={outerX}
              y2={outerY}
              stroke='#333'
              strokeWidth={index % 2 === 0 ? 2 : 1}
              opacity={0.7}
            />
          );
        })}

        {/* Needle */}
        {animated ? (
          <g ref={needleRef}>
            <circle
              cx={centerX}
              cy={centerY}
              r={needleBaseSize}
              fill='#333'
              filter='url(#shadow)'
            />
            <path
              d={`M ${centerX} ${centerY - needleLength} L ${centerX - 5} ${centerY} L ${centerX + 5} ${centerY} Z`}
              fill='#333'
              stroke='#333'
              strokeWidth='1'
            />
          </g>
        ) : (
          <>
            <path d={needlePath} fill='#333' stroke='#333' strokeWidth='1' />
            <circle cx={centerX} cy={centerY} r={needleBaseSize} fill='#333' />
          </>
        )}

        {/* Shadow filter */}
        <defs>
          <filter id='shadow' x='-20%' y='-20%' width='140%' height='140%'>
            <feDropShadow dx='0' dy='1' stdDeviation='2' floodOpacity='0.3' />
          </filter>
        </defs>
      </svg>

      {label && (
        <div
          className={cn(
            'rounded px-1.5 py-1 text-center text-xs font-medium text-background transition-colors duration-300',
            {
              'bg-[#E4E4E7]': isMonoColor,
              'bg-[#FF8888]': !isMonoColor,
            }
          )}
        >
          {label}
        </div>
      )}
      <div className='text-xl font-bold'>{normalizedValue}%</div>
    </div>
  );
}
