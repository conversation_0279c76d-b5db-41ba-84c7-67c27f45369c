import { useTranslations } from 'next-intl';
import React from 'react';

import { creativePostFormOpts } from '@/config/creative-post/form.config';

import { ExpandableCard } from '@/components/shared/expandable-card';
import { withForm } from '@/components/shared/form';

export const GeneralInfos = withForm({
  ...creativePostFormOpts,
  render: function Render({ form }) {
    const t = useTranslations();

    return (
      <ExpandableCard
        title={t('creative_card.form.general_infos.title')}
        description={t('creative_card.form.general_infos.description')}
        required
        initialOpen={true}
      >
        <div className='grid gap-3'>
          <form.AppField name='SocialMedia'>
            {(field) => (
              <field.SocialMediaRadioField
                label={t('creative_card.form.general_infos.social_media')}
              />
            )}
          </form.AppField>
          <form.AppField name='Context'>
            {(field) => (
              <field.TextField
                type='text'
                label={t('creative_card.form.general_infos.fields.title.label')}
                placeholder={t(
                  'creative_card.form.general_infos.fields.title.placeholder'
                )}
              />
            )}
          </form.AppField>
          <form.AppField name='Project'>
            {(field) => (
              <field.TextareaField
                label={t(
                  'creative_card.form.general_infos.fields.description.label'
                )}
                placeholder={t(
                  'creative_card.form.general_infos.fields.title.label'
                )}
              />
            )}
          </form.AppField>
        </div>
      </ExpandableCard>
    );
  },
});
