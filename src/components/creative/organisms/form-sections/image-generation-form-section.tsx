import { useTranslations } from 'next-intl';
import Image from 'next/image';
import React, { useState, useEffect } from 'react';

import { toBase64 } from '@/lib/to-base64';
import { cn } from '@/lib/utils';

import { useCreativePostOptions } from '@/hooks/creative/use-creative-post-options';

import {
  useImageTypeStore,
  useImageStyleStore,
  useImageEffectsStore,
} from '@/stores/creative-image-selector-store';

import { creativePostFormOpts } from '@/config/creative-post/form.config';

import { ExpandableCard } from '@/components/shared/expandable-card';
import { withForm } from '@/components/shared/form';
import { Icons } from '@/components/shared/icons';
import { Alert, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Switch } from '@/components/ui/switch';

export const ImageGenerationFormSection = withForm({
  ...creativePostFormOpts,
  render: function Render({ form }) {
    const [toggleFileUpload, setToggleFileUpload] = useState(false);
    const [imageSize, setImageSize] =
      useState<(typeof imageSizes)[number]['value']>('custom');
    const t = useTranslations();

    const { imageSizes } = useCreativePostOptions();

    const {
      selectedItem: selectedType,
      toggleSelector: toggleTypeSelector,
      closeSelector: closeTypeSelector,
    } = useImageTypeStore();

    const {
      selectedItem: selectedStyle,
      toggleSelector: toggleStyleSelector,
      closeSelector: closeStyleSelector,
    } = useImageStyleStore();

    const {
      getSelectedItemsArray: getSelectedEffects,
      toggleSelector: toggleEffectsSelector,
      closeSelector: closeEffectsSelector,
    } = useImageEffectsStore();

    const handleTypeSelectorToggle = () => {
      closeStyleSelector();
      closeEffectsSelector();
      toggleTypeSelector();
    };

    const handleStyleSelectorToggle = () => {
      closeTypeSelector();
      closeEffectsSelector();
      toggleStyleSelector();
    };

    const handleEffectsSelectorToggle = () => {
      closeTypeSelector();
      closeStyleSelector();
      toggleEffectsSelector();
    };

    const selectedEffects = getSelectedEffects();

    useEffect(() => {
      if (selectedType) {
        form.setFieldValue('ImageOptions.ImageType', selectedType.title);
      }
    }, [form, selectedType]);

    useEffect(() => {
      if (selectedStyle) {
        form.setFieldValue('ImageOptions.ImageStyle', selectedStyle.title);
      }
    }, [form, selectedStyle]);

    useEffect(() => {
      if (selectedEffects.length > 0) {
        const effectTitles = selectedEffects.map((effect) => effect.item.title);
        form.setFieldValue('ImageOptions.ImageEffects', effectTitles);
      } else {
        form.setFieldValue('ImageOptions.ImageEffects', []);
      }
    }, [selectedEffects, form]);

    return (
      <ExpandableCard
        title={t('creative_card.form.image_generation.title')}
        description={t('creative_card.form.image_generation.description')}
      >
        <form.Subscribe selector={(state) => state.values.GenerationType}>
          {(state) =>
            state === 'text_only' ||
            state === 'text_for_image' ||
            state === 'text_for_pdf' ? (
              <form.AppField
                name='ImageList'
                listeners={{
                  onChange: async ({ value }) => {
                    const file = value?.[0];

                    if (file && toggleFileUpload) {
                      const extension = file.name
                        .split('.')
                        .pop()
                        ?.toLowerCase();

                      const base64Content = await toBase64(file);

                      if (extension === 'pdf') {
                        form.setFieldValue('GenerationType', 'text_for_pdf');
                      } else {
                        form.setFieldValue('GenerationType', 'text_for_image');
                      }

                      form.setFieldValue(
                        'ImageOptions.ImageUplaod',
                        base64Content
                      );
                      form.setFieldValue(
                        'ImageOptions.FileUploadExtension',
                        extension
                      );
                    } else {
                      form.setFieldValue('GenerationType', 'text_only');
                      form.setFieldValue('ImageOptions.ImageUplaod', undefined);
                      form.setFieldValue(
                        'ImageOptions.FileUploadExtension',
                        undefined
                      );
                    }
                  },
                }}
              >
                {(field) => (
                  <field.FileUploadField
                    label={t('creative_card.form.image_generation.question')}
                    labelAction={
                      <div className='inline-flex items-center gap-2'>
                        <Switch
                          id={'1'}
                          checked={toggleFileUpload}
                          onClick={() => {
                            setToggleFileUpload(!toggleFileUpload);
                          }}
                        />
                        <Label htmlFor={'1'} className='sr-only'>
                          Colored switch
                        </Label>
                      </div>
                    }
                    required={false}
                    maxFiles={1}
                    accept='/*.png,.jpg,.jpeg,.pdf'
                    disabled={!toggleFileUpload}
                  />
                )}
              </form.AppField>
            ) : (
              <div className='grid gap-2'>
                <form.AppField name='ImageOptions.Keywords'>
                  {(field) => (
                    <field.TextFieldWithInnerTags
                      inlineTags
                      label={t(
                        'creative_card.form.image_generation.fields.image_content.label'
                      )}
                      required={false}
                      placeholder='content'
                    />
                  )}
                </form.AppField>
                <form.AppField
                  name='ImageOptions.ImageRatio'
                  listeners={{
                    onChange: ({ value }) => {
                      const size = imageSizes.find(
                        (size) => size.value === value
                      );

                      if (size) {
                        form.setFieldValue(
                          'ImageOptions.ImageWidth',
                          size.width
                        );
                        form.setFieldValue(
                          'ImageOptions.ImageHeight',
                          size.height
                        );
                      }
                    },
                  }}
                >
                  {(field) => (
                    <div className='relative w-full overflow-hidden'>
                      <RadioGroup
                        className='flex items-center gap-2 overflow-x-auto pb-2'
                        value={field.state.value ?? imageSize}
                        onValueChange={(value) => {
                          setImageSize(
                            value as (typeof imageSizes)[number]['value']
                          );
                          field.handleChange(value);
                        }}
                      >
                        {imageSizes.map((size) => (
                          <div
                            key={size.value}
                            className={cn(
                              'shadow-xs relative flex size-16 shrink-0 cursor-pointer flex-col items-center justify-center overflow-hidden rounded-lg border border-input bg-gray-400 text-center text-background outline-none',
                              {
                                'bg-[#5F91E3]': imageSize === size.value,
                              }
                            )}
                          >
                            <RadioGroupItem
                              id={size.value}
                              value={size.value}
                              className='sr-only'
                            />
                            <div className='grid h-full w-full place-items-center p-2'>
                              {size.icon ? (
                                <size.icon className='size-5' />
                              ) : (
                                <span className='w-full truncate text-sm'>
                                  {size.value}
                                </span>
                              )}
                              <label
                                htmlFor={size.value}
                                className='w-full cursor-pointer truncate text-xs font-medium leading-none after:absolute after:inset-0'
                              >
                                {size.label}
                              </label>
                            </div>
                          </div>
                        ))}
                      </RadioGroup>
                    </div>
                  )}
                </form.AppField>
                <form.AppField name='ImageOptions.ImageWidth'>
                  {(field) => (
                    <field.SliderWithInputField
                      minValue={100}
                      maxValue={2000}
                      initialValue={[field.state.value]}
                      label={t(
                        'creative_card.form.image_generation.fields.width.label'
                      )}
                      disabled={imageSize !== 'custom'}
                      required={false}
                    />
                  )}
                </form.AppField>
                <form.AppField name='ImageOptions.ImageHeight'>
                  {(field) => (
                    <field.SliderWithInputField
                      minValue={100}
                      maxValue={2000}
                      initialValue={[field.state.value]}
                      label={t(
                        'creative_card.form.image_generation.fields.height.label'
                      )}
                      disabled={imageSize !== 'custom'}
                      required={false}
                    />
                  )}
                </form.AppField>

                <form.AppField name='ImageOptions.ImageType'>
                  {(field) => (
                    <>
                      <Label htmlFor={field.name}>
                        {t(
                          'creative_card.form.image_generation.fields.image_type.label'
                        )}
                      </Label>
                      <Alert className='relative overflow-hidden'>
                        <Input
                          id={field.name}
                          name={field.name}
                          type='text'
                          value={field.state.value}
                          readOnly
                          placeholder='content'
                          className='hidden'
                        />
                        {selectedType && (
                          <div className='absolute inset-0'>
                            <Image
                              src={selectedType.src}
                              alt={`${selectedType.title} background`}
                              fill
                              priority
                              sizes='(max-width: 768px) 100vw, 50vw'
                              className='object-cover [object-position:75%_20%]'
                            />
                          </div>
                        )}
                        <AlertTitle className='relative m-0 flex items-center justify-between text-background'>
                          <div
                            className={cn('font-semibold', {
                              'text-foreground': !selectedType,
                            })}
                          >
                            {selectedType
                              ? t(selectedType.title)
                              : t(
                                  'creative_card.form.image_generation.fields.image_type.placeholder'
                                )}
                          </div>
                          <Button
                            type='button'
                            size='sm'
                            className='rounded-lg bg-[#3776DC]'
                            iconEnd={<Icons.swap className='size-4' />}
                            onClick={handleTypeSelectorToggle}
                          >
                            {t(
                              'creative_card.form.image_generation.fields.image_type.button'
                            )}
                          </Button>
                        </AlertTitle>
                      </Alert>
                    </>
                  )}
                </form.AppField>

                <form.AppField name='ImageOptions.ImageStyle'>
                  {(field) => (
                    <>
                      <Label htmlFor={field.name}>
                        {t(
                          'creative_card.form.image_generation.fields.image_style.label'
                        )}
                      </Label>
                      <Alert className='relative overflow-hidden'>
                        <Input
                          id={field.name}
                          name={field.name}
                          type='text'
                          readOnly
                          value={field.state.value}
                          placeholder='content'
                          className='hidden'
                        />
                        {selectedStyle && (
                          <div className='absolute inset-0'>
                            <Image
                              src={selectedStyle.src}
                              alt={`${selectedStyle.title} background`}
                              fill
                              priority
                              sizes='(max-width: 768px) 100vw, 50vw'
                              className='object-cover [object-position:75%_20%]'
                            />
                          </div>
                        )}
                        <AlertTitle className='relative m-0 flex items-center justify-between text-background'>
                          <div
                            className={cn('font-semibold', {
                              'text-foreground': !selectedStyle,
                            })}
                          >
                            {selectedStyle
                              ? t(selectedStyle.title)
                              : t(
                                  'creative_card.form.image_generation.fields.image_style.placeholder'
                                )}
                          </div>
                          <Button
                            type='button'
                            size='sm'
                            className='rounded-lg bg-[#3776DC]'
                            iconEnd={<Icons.swap className='size-4' />}
                            onClick={handleStyleSelectorToggle}
                          >
                            {t(
                              'creative_card.form.image_generation.fields.image_style.button'
                            )}
                          </Button>
                        </AlertTitle>
                      </Alert>
                    </>
                  )}
                </form.AppField>

                <form.AppField name='ImageOptions.ImageEffects'>
                  {(field) => (
                    <>
                      <Label htmlFor={field.name}>
                        {t(
                          'creative_card.form.image_generation.fields.image_effects.label'
                        )}
                      </Label>
                      <Alert className='relative overflow-hidden'>
                        <Input
                          id={field.name}
                          name={field.name}
                          type='text'
                          value={field.state.value}
                          readOnly
                          placeholder='content'
                          className='hidden'
                        />

                        {selectedEffects.length > 0 && (
                          <div className='grid grid-cols-1 gap-2 py-2 sm:grid-cols-2 md:grid-cols-3'>
                            {selectedEffects.map(({ category, item }) => (
                              <div
                                key={item.id}
                                className='relative h-24 overflow-hidden rounded-md'
                              >
                                <Image
                                  src={item.src}
                                  alt={`${item.title} effect`}
                                  fill
                                  priority
                                  sizes='(max-width: 768px) 33vw, 25vw'
                                  className='object-cover'
                                />
                                <div className='absolute inset-0 bg-gradient-to-t from-black/70 to-transparent'>
                                  <div className='absolute start-2 top-2 rounded bg-black/50 px-2 py-1 text-xs font-medium text-white'>
                                    {t(category)}
                                  </div>
                                  <div className='absolute bottom-2 left-2 text-sm font-medium text-white'>
                                    {t(item.title)}
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        )}

                        <AlertTitle className='relative m-0 flex items-center justify-between text-foreground'>
                          <div className='font-semibold'>
                            {selectedEffects.length < 1 &&
                              t(
                                'creative_card.form.image_generation.fields.image_effects.placeholder'
                              )}
                          </div>
                          <Button
                            type='button'
                            size='sm'
                            className='rounded-lg bg-[#3776DC]'
                            iconEnd={<Icons.swap className='size-4' />}
                            onClick={handleEffectsSelectorToggle}
                          >
                            {selectedEffects.length > 0
                              ? t(
                                  'creative_card.form.image_generation.fields.image_effects.button.change'
                                )
                              : t(
                                  'creative_card.form.image_generation.fields.image_effects.button.add'
                                )}
                          </Button>
                        </AlertTitle>
                      </Alert>
                    </>
                  )}
                </form.AppField>
              </div>
            )
          }
        </form.Subscribe>
      </ExpandableCard>
    );
  },
});
