import { useTranslations } from 'next-intl';
import React from 'react';

import { useCreativePostOptions } from '@/hooks/creative/use-creative-post-options';

import { creativePostFormOpts } from '@/config/creative-post/form.config';

import { ExpandableCard } from '@/components/shared/expandable-card';
import { withForm } from '@/components/shared/form';
import { Icons } from '@/components/shared/icons';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';

export const AdvancedOptionsContent = withForm({
  ...creativePostFormOpts,
  render: function Render({ form }) {
    const { toneOfVoices, languages } = useCreativePostOptions();
    const t = useTranslations();

    return (
      <ExpandableCard
        title={t('creative_card.form.advanced_options.title')}
        description={t('creative_card.form.advanced_options.description')}
      >
        <form.AppField name='TextOptions'>
          {() => (
            <div className='grid gap-3'>
              <div className='grid grid-cols-2 gap-2'>
                <form.AppField name={'TextOptions.Language'}>
                  {(subField) => (
                    <subField.SelectField
                      label={t(
                        'creative_card.form.advanced_options.select.language.label'
                      )}
                      required={false}
                      placeholder={t(
                        'creative_card.form.advanced_options.select.placeholder'
                      )}
                      options={languages}
                    />
                  )}
                </form.AppField>
                <form.AppField name='TextOptions.ToneOfVoice'>
                  {(field) => (
                    <field.SelectField
                      required={false}
                      label={t(
                        'creative_card.form.advanced_options.select.tone_of_voice.label'
                      )}
                      placeholder={t(
                        'creative_card.form.advanced_options.select.placeholder'
                      )}
                      options={toneOfVoices}
                    />
                  )}
                </form.AppField>
              </div>
              <div className='grid w-full grid-cols-2 gap-2'>
                <div className='rounded-lg border px-3 py-2.5'>
                  <form.AppField name={'TextOptions.WithEmoji'}>
                    {(field) => (
                      <div className='flex items-center gap-2'>
                        <Icons.emojiIcon className='size-4' />
                        <Label
                          htmlFor={field.name}
                          className='flex-1 text-[#A1A1AA]'
                        >
                          {t(
                            'creative_card.form.advanced_options.checkbox.emoji'
                          )}
                        </Label>
                        <Checkbox
                          id={field.name}
                          checked={field.state.value}
                          onCheckedChange={(checked: boolean) =>
                            field.handleChange(checked)
                          }
                        />
                      </div>
                    )}
                  </form.AppField>
                </div>
                <div className='rounded-lg border px-3 py-2.5'>
                  <form.AppField name={'TextOptions.WithHashtag'}>
                    {(field) => (
                      <div className='flex items-center gap-2'>
                        <Icons.hashtag className='size-4' />
                        <Label
                          htmlFor={field.name}
                          className='flex-1 text-[#A1A1AA]'
                        >
                          {t(
                            'creative_card.form.advanced_options.checkbox.hushtag'
                          )}
                        </Label>
                        <Checkbox
                          id={field.name}
                          checked={field.state.value}
                          onCheckedChange={(checked: boolean) =>
                            field.handleChange(checked)
                          }
                        />
                      </div>
                    )}
                  </form.AppField>
                </div>
                <div className='col-span-2'>
                  <form.AppField name={'TextOptions.OriginalityLevel'}>
                    {(field) => (
                      <>
                        <Label>
                          {t('creative_card.form.advanced_options.originality')}
                        </Label>
                        <div>
                          <span
                            className='mb-3 flex w-full items-center justify-between gap-2 text-xs font-medium text-muted-foreground'
                            aria-hidden='true'
                          >
                            <span>0</span>
                            <span>10</span>
                          </span>
                          <Slider
                            defaultValue={[5]}
                            value={[field.state.value] as number[]}
                            onValueChange={(value) => {
                              field.handleChange(value[0]);
                            }}
                            step={1}
                            min={0}
                            max={10}
                            showTooltip={true}
                            aria-label='Slider with labels and tooltip'
                          />
                        </div>
                      </>
                    )}
                  </form.AppField>
                </div>
              </div>
            </div>
          )}
        </form.AppField>
      </ExpandableCard>
    );
  },
});
