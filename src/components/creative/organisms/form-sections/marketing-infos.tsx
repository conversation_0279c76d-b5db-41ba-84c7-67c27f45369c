import { useTranslations } from 'next-intl';
import React from 'react';

import { getFilteredPrivateModelsByModelType } from '@/lib/helpers/creative/get-filtered-private-models-by-model-type';

import { useCreativePostOptions } from '@/hooks/creative/use-creative-post-options';

import { useAllByCompanyBrandId } from '@/api/hooks/ai/queries';
import { useAllKnowledge } from '@/api/hooks/knowledge/queries';
import { useCurrentUserStore } from '@/stores/current-user-store';

import { creativePostFormOpts } from '@/config/creative-post/form.config';

import { ExpandableCard } from '@/components/shared/expandable-card';
import { withForm } from '@/components/shared/form';
import MultipleSelector from '@/components/shared/multiselect';
import { Label } from '@/components/ui/label';

export const MarketingInfos = withForm({
  ...creativePostFormOpts,
  render: function Render({ form }) {
    const t = useTranslations();

    const user = useCurrentUserStore().getUser();
    const { marketingStrategies } = useCreativePostOptions();

    const { data: privateModels, isFetching: isFetchingPrivateModels } =
      useAllByCompanyBrandId();
    const { data: knowledges } = useAllKnowledge(
      user?.companyId,
      user?.brandId
    );

    return (
      <ExpandableCard
        title={t('creative_card.form.marketing_infos.title')}
        description={t('creative_card.form.marketing_infos.description')}
      >
        <div className='grid gap-2'>
          <form.AppField name='MarketingStrategy'>
            {(field) => (
              <field.SelectField
                required={false}
                label={t(
                  'creative_card.form.marketing_infos.select.marketing_strategy.label'
                )}
                placeholder={t(
                  'creative_card.form.marketing_infos.select.placeholder'
                )}
                options={marketingStrategies}
              />
            )}
          </form.AppField>

          <form.AppField name='ImageOptions'>
            {() => (
              <form.Subscribe selector={(state) => state.values.GenerationType}>
                {(state) =>
                  (state === 'text_image' || state === 'text_many_image') && (
                    <form.AppField name='ImageOptions.PrivateModelId'>
                      {(subField) => (
                        <subField.SelectField
                          label={t(
                            'creative_card.form.marketing_infos.select.private_model.label'
                          )}
                          required={false}
                          placeholder={t(
                            'creative_card.form.marketing_infos.select.placeholder'
                          )}
                          isLoading={isFetchingPrivateModels}
                          options={getFilteredPrivateModelsByModelType({
                            modelType: 'IMAGE',
                            privateModelsDataList: privateModels,
                          })}
                        />
                      )}
                    </form.AppField>
                  )
                }
              </form.Subscribe>
            )}
          </form.AppField>

          <form.AppField name='TextOptions'>
            {() => (
              <>
                <form.Subscribe
                  selector={(state) => state.values.GenerationType}
                >
                  {(state) =>
                    (state === 'text_only' ||
                      state === 'text_for_image' ||
                      state === 'text_for_pdf') && (
                      <form.AppField name={'TextOptions.PrivateModelId'}>
                        {(subField) => (
                          <subField.SelectField
                            label={t(
                              'creative_card.form.marketing_infos.select.private_model.label'
                            )}
                            required={false}
                            placeholder={t(
                              'creative_card.form.marketing_infos.select.placeholder'
                            )}
                            isLoading={isFetchingPrivateModels}
                            options={getFilteredPrivateModelsByModelType({
                              modelType: 'LLM',
                              privateModelsDataList: privateModels,
                            })}
                          />
                        )}
                      </form.AppField>
                    )
                  }
                </form.Subscribe>

                <form.AppField name={'TextOptions.Knowledges'}>
                  {(field) => {
                    const selectedOptions = field.state.value
                      ? knowledges
                          ?.filter((k) => field.state.value?.includes(k.Id))
                          .map((k) => ({
                            label: k.Name,
                            value: k.Id,
                          }))
                      : [];

                    return (
                      <>
                        <Label>
                          {t(
                            'creative_card.form.marketing_infos.select.knowledge.label'
                          )}
                        </Label>
                        <MultipleSelector
                          options={
                            knowledges?.map((knowledge) => ({
                              label: knowledge.Name,
                              value: knowledge.Id,
                            })) ?? []
                          }
                          value={selectedOptions}
                          onChange={(selectedOptions) => {
                            field.handleChange(
                              selectedOptions.map((option) => option.value)
                            );
                          }}
                          placeholder={t(
                            'creative_card.form.marketing_infos.select.placeholder'
                          )}
                          emptyIndicator={
                            <p className='text-center text-sm'>
                              {t('no_results_found')}
                            </p>
                          }
                        />
                      </>
                    );
                  }}
                </form.AppField>
              </>
            )}
          </form.AppField>
        </div>
      </ExpandableCard>
    );
  },
});
