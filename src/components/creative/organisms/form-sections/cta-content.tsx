import { useTranslations } from 'next-intl';
import React from 'react';

import { useCreativePostOptions } from '@/hooks/creative/use-creative-post-options';

import { creativePostFormOpts } from '@/config/creative-post/form.config';

import { ExpandableCard } from '@/components/shared/expandable-card';
import { withForm } from '@/components/shared/form';

export const CtaContent = withForm({
  ...creativePostFormOpts,
  render: function Render({ form }) {
    const { ctaTexts } = useCreativePostOptions();
    const t = useTranslations();

    return (
      <ExpandableCard
        title={t('creative_card.form.cta_content.title')}
        description={t('creative_card.form.cta_content.description')}
      >
        <form.AppField name='CallToAction'>
          {() => (
            <div className='grid grid-cols-2 gap-2'>
              <form.AppField name={'CallToAction.CTAtype'}>
                {(subField) => (
                  <subField.SelectField
                    label={t('creative_card.form.cta_content.select.label')}
                    required={false}
                    placeholder={t(
                      'creative_card.form.cta_content.select.label'
                    )}
                    options={ctaTexts}
                  />
                )}
              </form.AppField>
              <form.AppField
                name='CallToAction.CTALink'
                listeners={{
                  onChange: ({ value }) => {
                    form.setFieldValue(
                      'CallToAction.WithCallToAction',
                      (value && value.length > 0) || false
                    );
                  },
                }}
              >
                {(subField) => (
                  <subField.TextField
                    required={false}
                    label={t('creative_card.form.cta_content.field.label')}
                    placeholder={t(
                      'creative_card.form.cta_content.field.placeholder'
                    )}
                  />
                )}
              </form.AppField>
            </div>
          )}
        </form.AppField>
      </ExpandableCard>
    );
  },
});
