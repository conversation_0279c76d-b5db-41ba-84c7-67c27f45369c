'use client';

import { LocalThemeProvider } from '@/providers/local-theme-provider';

import { PreviewContent } from '@/components/creative/molecules/preview-content';
import { PreviewFooter } from '@/components/creative/molecules/preview-footer';
import { PreviewHeader } from '@/components/creative/molecules/preview-header';
import { <PERSON><PERSON><PERSON><PERSON>, CardFooter, CardContent } from '@/components/ui/card';

export function PostSuggestionPreviewCard() {
  return (
    <LocalThemeProvider>
      <CardHeader className='px-4'>
        <PreviewHeader />
      </CardHeader>
      <CardContent className='h-full w-full flex-1 overflow-hidden p-0'>
        <PreviewContent />
      </CardContent>
      <CardFooter className='grid place-items-center px-5 py-4'>
        <PreviewFooter />
      </CardFooter>
    </LocalThemeProvider>
  );
}
