import { cn } from '@/lib/utils';

import { LocalThemeProvider } from '@/providers/local-theme-provider';
import { useCreativePostStore } from '@/stores/creative-post-store';

import { PreviewHeader } from '@/components/creative/molecules/preview-header';
import { SocialMediaCard } from '@/components/creative/molecules/social-media-card';
import { LocalThemeWrapper } from '@/components/demo/local-theme-wrapper';
import { CardHeader, CardContent } from '@/components/ui/card';

export function EditPostTextPreviewCard() {
  const currentPostSuggestion = useCreativePostStore(
    (s) => s.currentPostSuggestion
  );
  const editPostSuggestion = useCreativePostStore((s) => s.editPostSuggestion);

  return (
    <LocalThemeProvider>
      <CardHeader className='px-4'>
        <PreviewHeader />
      </CardHeader>
      <CardContent className='flex-1 overflow-hidden p-0'>
        <div className='h-full w-full'>
          <div className='flex h-full w-full flex-col items-center justify-center'>
            <div className={cn('h-[90%] w-3/5 rounded-xl')}>
              {currentPostSuggestion && (
                <LocalThemeWrapper className='h-full'>
                  <SocialMediaCard
                    post={editPostSuggestion ?? currentPostSuggestion}
                    canEdit
                  />
                </LocalThemeWrapper>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </LocalThemeProvider>
  );
}
