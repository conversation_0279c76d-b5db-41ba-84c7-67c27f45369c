'use client';

import { useTranslations } from 'next-intl';
import React from 'react';

import { updatePostPromptFormOpts } from '@/config/creative-post/form.config';

import { withForm } from '@/components/shared/form';

export const UpdatePostPromptForm = withForm({
  defaultValues: {
    ...updatePostPromptFormOpts.defaultValues,
  },
  props: {
    isPromptLoading: false,
  },
  render: function Render({ form, isPromptLoading }) {
    const t = useTranslations();

    return (
      <form
        onSubmit={async (e) => {
          e.preventDefault();
          await form.handleSubmit();
        }}
      >
        <form.AppField name='textprompt'>
          {(field) => (
            <field.TextareaField
              label={t('edit_text_creative_card.update_post_form.description')}
              placeholder={t(
                'edit_text_creative_card.update_post_form.placeholder'
              )}
              className='h-28'
              isLoading={isPromptLoading}
            />
          )}
        </form.AppField>
      </form>
    );
  },
});
