'use client';

import React from 'react';

import { creativePostFormOpts } from '@/config/creative-post/form.config';

import { AdvancedOptionsContent } from '@/components/creative/organisms/form-sections/advanced-options-content';
import { CtaContent } from '@/components/creative/organisms/form-sections/cta-content';
import { GeneralInfos } from '@/components/creative/organisms/form-sections/general-infos';
import { ImageGenerationFormSection } from '@/components/creative/organisms/form-sections/image-generation-form-section';
import { MarketingInfos } from '@/components/creative/organisms/form-sections/marketing-infos';
import { withForm } from '@/components/shared/form';

export const CreativeForm = withForm({
  ...creativePostFormOpts,
  render: function Render({ form }) {
    return (
      <form
        className='flex flex-col gap-4'
        onSubmit={async (e) => {
          e.preventDefault();
          await form.handleSubmit();
        }}
      >
        <GeneralInfos form={form} />

        <ImageGenerationFormSection form={form} />

        <CtaContent form={form} />

        <MarketingInfos form={form} />

        <AdvancedOptionsContent form={form} />
      </form>
    );
  },
});
