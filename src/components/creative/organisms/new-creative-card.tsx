'use client';

import { useTranslations } from 'next-intl';
import { toast } from 'sonner';

import { Penci<PERSON>, ImageIcon, RotateCcw } from 'lucide-react';

import { useCreativePostOptions } from '@/hooks/creative/use-creative-post-options';

import { useCreateNewPostSuggestions } from '@/api/hooks/posts/mutations';
import { generateNewPostSuggestionsSchema } from '@/api/models/schemas/creative.schema';
import { useCurrentUserStore } from '@/stores/current-user-store';

import { creativePostFormOpts } from '@/config/creative-post/form.config';

import { CreativeForm } from '@/components/creative/organisms/forms/creative-form';
import { useAppForm } from '@/components/shared/form';
import {
  CardTitle,
  CardFooter,
  CardHeader,
  CardContent,
  CardDescription,
} from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';

export function NewCreativeCard() {
  const t = useTranslations();

  const { generationTypes } = useCreativePostOptions();

  const { getUser } = useCurrentUserStore();
  const user = getUser();

  const { mutateAsync: createNewPostSuggestions } =
    useCreateNewPostSuggestions();

  const creativePostForm = useAppForm({
    defaultValues: {
      ...creativePostFormOpts.defaultValues,
      CompanyId: user?.companyId ?? '',
      BrandId: user?.brandId ?? '',
      UserEmail: user?.email ?? '',
    },
    validators: {
      onChange: generateNewPostSuggestionsSchema(t),
    },
    onSubmit: async ({ value }) => {
      const { ImageList, ...rest } = value;
      creativePostForm.setFieldValue('ImageList', ImageList);

      await createNewPostSuggestions({
        ...rest,
        CompanyId: user?.companyId ?? '',
        BrandId: user?.brandId ?? '',
        UserEmail: user?.email ?? '',
      });
    },
    onSubmitInvalid: () => {
      toast.error(t('form.invalid_form'));
    },
  });

  return (
    <>
      <CardHeader className='space-y-4 px-4 pb-2'>
        <CardTitle className='flex items-center justify-between'>
          <h1>{t('creative_card.title')}</h1>
          <span
            className='rounded-lg border border-primary/70 p-1 text-primary/60 hover:cursor-pointer hover:bg-background hover:text-primary'
            onClick={() => {
              creativePostForm.reset();
            }}
          >
            <RotateCcw className='size-5 rotate-45' />
          </span>
        </CardTitle>
        <CardDescription>
          <creativePostForm.AppField name='GenerationType'>
            {(field) => (
              <field.TabsField
                defaultValue='text_image'
                options={[
                  {
                    value: 'text_image',
                    label: t('creative_card.tabs.visual.title'),
                    icon: <ImageIcon className='h-4 w-4' />,
                    content: (
                      <field.RadioGroupField
                        options={generationTypes}
                        indicatorIconClassName='size-1.5'
                      />
                    ),
                  },
                  {
                    value: ['text_only', 'text_for_image', 'text_for_pdf'],
                    fieldValue: 'text_only',
                    label: t('creative_card.tabs.text.title'),
                    icon: <Pencil className='h-4 w-4' />,
                  },
                ]}
              />
            )}
          </creativePostForm.AppField>
        </CardDescription>
      </CardHeader>
      <ScrollArea className='h-full'>
        <CardContent className='p-0 px-4 pb-4'>
          <CreativeForm form={creativePostForm} />
        </CardContent>
      </ScrollArea>
      <CardFooter className='grid border-t border-border bg-muted/70 px-5 py-4'>
        <creativePostForm.AppForm>
          <creativePostForm.SubmitButton
            onClick={creativePostForm.handleSubmit}
          >
            {t('creative_card.buttons.generate')}
          </creativePostForm.SubmitButton>
        </creativePostForm.AppForm>
      </CardFooter>
    </>
  );
}
