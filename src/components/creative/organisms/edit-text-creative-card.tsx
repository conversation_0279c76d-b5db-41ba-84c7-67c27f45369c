'use client';

import { useTranslations } from 'next-intl';
import React, { useState } from 'react';
import { toast } from 'sonner';

import {
  useUpdatePost,
  useUpdatePostPrompt,
} from '@/api/hooks/posts/mutations';
import { useGetGeneratedPostPrompt } from '@/api/hooks/posts/queries';
import {
  updatePostSchema,
  UpdatePostPromptSchema,
  updatePostPromptSchema,
} from '@/api/models/schemas/creative.schema';
import { useCreativePostStore } from '@/stores/creative-post-store';

import { updatePostFormOpts } from '@/config/creative-post/form.config';

import { UpdatePostForm } from '@/components/creative/organisms/forms/edit-post-text-form';
import { UpdatePostPromptForm } from '@/components/creative/organisms/forms/edit-post-text-prompt-form';
import { useAppForm } from '@/components/shared/form';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>ontent,
  CardDescription,
} from '@/components/ui/card';
import { Scroll<PERSON>rea } from '@/components/ui/scroll-area';
import { Ta<PERSON>, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';

export function EditTextCreativeCard() {
  const t = useTranslations();
  const [tabName, setTabName] = useState<'adjust-options' | 'edit-prompt'>(
    'adjust-options'
  );

  const currentPostSuggestion = useCreativePostStore(
    (s) => s.currentPostSuggestion
  );

  const { data: generatedPostPrompt, isLoading: isLoadingGeneratedPostPrompt } =
    useGetGeneratedPostPrompt('TEXT', currentPostSuggestion?.Id);

  const { mutateAsync: updatePostPrompt } = useUpdatePostPrompt();
  const { mutateAsync: updatePost } = useUpdatePost();

  const updatePostPromptForm = useAppForm({
    defaultValues: {
      textprompt: generatedPostPrompt?.textprompt ?? '',
    } as UpdatePostPromptSchema,
    validators: {
      onChange: updatePostPromptSchema(t),
    },
    onSubmit: async ({ value }) => {
      await updatePostPrompt({
        type: 'TEXT',
        adContentId: currentPostSuggestion?.Id ?? '',
        payload: value,
      });
    },
    onSubmitInvalid: () => {
      toast.error(t('form.invalid_form'));
    },
  });

  const updatePostForm = useAppForm({
    defaultValues: {
      ...updatePostFormOpts.defaultValues,
      CompanyId: currentPostSuggestion?.CompanyId ?? '',
    },
    validators: {
      onChange: updatePostSchema(t),
    },
    onSubmit: async ({ value }) => {
      await updatePost({
        adContentId: currentPostSuggestion?.Id ?? '',
        payload: value,
      });
    },
    onSubmitInvalid: () => {
      toast.error(t('form.invalid_form'));
    },
  });

  return (
    <>
      <CardHeader className='space-y-4 px-4'>
        <CardTitle className='flex items-center justify-between'>
          <h1>{t('edit_text_creative_card.title')}</h1>
        </CardTitle>
        <CardDescription>
          <Tabs
            className='w-full'
            value={tabName}
            onValueChange={(value) => setTabName(value as typeof tabName)}
          >
            <TabsList className='grid w-full grid-cols-2'>
              <TabsTrigger
                value='adjust-options'
                className='flex items-center gap-2 data-[state=active]:bg-primary data-[state=active]:text-background rtl:flex-row-reverse'
              >
                {t('edit_text_creative_card.buttons.adjust_options')}
              </TabsTrigger>
              <TabsTrigger
                value='edit-prompt'
                className='flex items-center gap-2 data-[state=active]:bg-primary data-[state=active]:text-background rtl:flex-row-reverse'
              >
                {t('edit_text_creative_card.buttons.edit_prompt')}
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </CardDescription>
      </CardHeader>
      <ScrollArea className='h-full'>
        <CardContent className='px-4 py-0'>
          {tabName === 'adjust-options' ? (
            <UpdatePostForm form={updatePostForm} />
          ) : (
            <UpdatePostPromptForm
              form={updatePostPromptForm}
              isPromptLoading={isLoadingGeneratedPostPrompt}
            />
          )}
        </CardContent>
      </ScrollArea>
      <CardFooter className='grid border-t border-border bg-muted/70 px-5 py-4'>
        {tabName === 'adjust-options' ? (
          <updatePostForm.AppForm>
            <updatePostForm.SubmitButton onClick={updatePostForm.handleSubmit}>
              {t('edit_text_creative_card.buttons.regenerate')}
            </updatePostForm.SubmitButton>
          </updatePostForm.AppForm>
        ) : (
          <updatePostPromptForm.AppForm>
            <updatePostPromptForm.SubmitButton
              onClick={updatePostPromptForm.handleSubmit}
            >
              {t('edit_text_creative_card.buttons.regenerate')}
            </updatePostPromptForm.SubmitButton>
          </updatePostPromptForm.AppForm>
        )}
      </CardFooter>
    </>
  );
}
