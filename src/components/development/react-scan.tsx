'use client';

import { useEffect } from 'react';
import { scan } from 'react-scan';

interface ReactScanProps {
  showToolbar?: boolean;
  enableScanning?: boolean;
}

export function ReactScan({ showToolbar, enableScanning }: ReactScanProps) {
  useEffect(() => {
    scan({
      enabled:
        enableScanning ??
        (process.env.NODE_ENV === 'development' &&
          process.env.NEXT_PUBLIC_TESTING === 'true'),
      showToolbar:
        showToolbar ??
        (process.env.NODE_ENV === 'development' &&
          process.env.NEXT_PUBLIC_TESTING === 'true'),
    });
  }, [showToolbar, enableScanning]);

  return <></>;
}
