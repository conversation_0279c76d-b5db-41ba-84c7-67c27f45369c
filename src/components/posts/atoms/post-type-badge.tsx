import { useTranslations } from 'next-intl';

import { PostSuggestion } from '@/api/models/dtos/post.dto';

import { Badge } from '@/components/ui/badge';

interface PostTypeBadgeProps {
  type: PostSuggestion['AdType'];
}

export function PostTypeBadge({ type }: PostTypeBadgeProps) {
  const t = useTranslations();

  switch (type) {
    case 'text_only':
      return (
        <Badge
          variant='outline'
          className='flex w-fit items-center gap-2 rounded-full bg-[#E6F1FE] font-medium text-[#006FEE]'
        >
          {t('posts_page.filter_config.post_type.text_only')}
        </Badge>
      );
    case 'text_for_image':
      return (
        <Badge
          variant='outline'
          className='flex w-fit items-center gap-2 rounded-md bg-[#E6F1FE] font-medium text-[#006FEE]'
        >
          {t('posts_page.filter_config.post_type.text_for_image')}
        </Badge>
      );
    case 'text_for_pdf':
      return (
        <Badge
          variant='outline'
          className='flex w-fit items-center gap-2 rounded-md bg-[#E6F1FE] font-medium text-[#006FEE]'
        >
          {t('posts_page.filter_config.post_type.text_for_pdf')}
        </Badge>
      );
    case 'text_image':
      return (
        <Badge
          variant='outline'
          className='flex w-fit items-center gap-2 rounded-md bg-[#E6F1FE] font-medium text-[#006FEE]'
        >
          {t('posts_page.filter_config.post_type.text_image')}
        </Badge>
      );
    case 'text_many_image':
      return (
        <Badge
          variant='outline'
          className='flex w-fit items-center gap-2 rounded-md bg-[#E6F1FE] font-medium text-[#006FEE]'
        >
          {t('posts_page.filter_config.post_type.text_many_image')}
        </Badge>
      );
    default:
      return <Badge variant='destructive'>{t('not_handled')}</Badge>;
  }
}
