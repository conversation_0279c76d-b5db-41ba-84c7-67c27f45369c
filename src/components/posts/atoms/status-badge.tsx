import { useTranslations } from 'next-intl';

import { PostSuggestion } from '@/api/models/dtos/post.dto';

import { Badge } from '@/components/ui/badge';

interface StatusBadgeProps {
  status: PostSuggestion['Status'];
}

export function StatusBadge({ status }: StatusBadgeProps) {
  const t = useTranslations();

  switch (status) {
    case 'Draft':
      return (
        <Badge
          variant='outline'
          className='flex w-fit items-center gap-2 rounded-md bg-[#F4F4F5] font-medium text-[#3F3F46]'
        >
          <div className='size-2 rounded-full bg-[#F31260]' />
          {t('posts_page.filter_config.status.draft')}
        </Badge>
      );
    case 'Ready':
      return (
        <Badge
          variant='outline'
          className='flex w-fit items-center gap-2 rounded-full'
        >
          <div className='size-2 rounded-full bg-blue-600' />
          {t('posts_page.filter_config.status.ready')}
        </Badge>
      );
    case 'Approved':
      return (
        <Badge
          variant='outline'
          className='flex w-fit items-center gap-2 rounded-full'
        >
          <div className='size-2 rounded-full bg-blue-600' />
          {t('posts_page.filter_config.status.approved')}
        </Badge>
      );
    case 'Scheduled':
      return (
        <Badge
          variant='outline'
          className='flex w-fit items-center gap-2 rounded-full border-green-300 bg-green-100 text-green-800'
        >
          <div className='size-2 rounded-full bg-green-600' />
          {t('posts_page.filter_config.status.scheduled')}
        </Badge>
      );
    case 'Posted':
      return (
        <Badge
          variant='outline'
          className='flex w-fit items-center gap-2 rounded-full border-green-300 bg-green-100 text-green-800'
        >
          <div className='size-2 rounded-full bg-green-600' />
          {t('posts_page.filter_config.status.posted')}
        </Badge>
      );
    default:
      return <Badge variant='destructive'>{t('not_handled')}</Badge>;
  }
}
