import { PostSuggestion } from '@/api/models/dtos/post.dto';

import { Icons } from '@/components/shared/icons';
import { Badge } from '@/components/ui/badge';

interface PlatformBadgeProps {
  platform: PostSuggestion['SocialMedia'];
}

export function PlatformBadge({ platform }: PlatformBadgeProps) {
  switch (platform) {
    case 'facebook':
      return (
        <div className='flex items-center gap-1'>
          <Icons.facebookOfficial className='size-4' />
          <span className='text-xs text-[#006FEE]'>Facebook</span>
        </div>
      );
    case 'instagram':
      return (
        <div className='flex items-center gap-1'>
          <Icons.instagramOfficial className='size-4' />
          <span className='text-xs text-[#E1306C]'>Instagram</span>
        </div>
      );
    case 'linkedin':
      return (
        <div className='flex items-center gap-1'>
          <Icons.linkedinOfficial className='size-4' />
          <span className='text-xs text-[#0A66C2]'>LinkedIn</span>
        </div>
      );
    case 'x':
      return (
        <div className='flex items-center gap-1'>
          <Icons.x className='size-4' />
          <span className='text-xs'>X</span>
        </div>
      );
    default:
      return <Badge variant='destructive'>Not Found</Badge>;
  }
}
