'use client';

import { useTranslations } from 'next-intl';

import { CirclePlus } from 'lucide-react';

import { PostSuggestion } from '@/api/models/dtos/post.dto';

import { usePostsByCompanyBrandId } from '@/api/hooks/posts/queries';
import { Link } from '@/i18n/routing';

import { useOptionsConfig } from '@/config/posts/use-options-config';
import { usePostsColumns } from '@/config/posts/use-posts-table-columns';

import { GridViewMode } from '@/components/posts/molecules/grid-view-mode';
import { PostDetail } from '@/components/posts/post-detail';
import { DataTable } from '@/components/shared/table/data-table';
import { Button } from '@/components/ui/button';

export default function PageContent() {
  const t = useTranslations();

  const {
    data: allPosts,
    isPending,
    isError,
    error,
    refetch,
  } = usePostsByCompanyBrandId();
  const { postColumnsDef } = usePostsColumns();
  const { searchConfig, filterConfig } = useOptionsConfig();

  return (
    <div className='flex h-full flex-col'>
      <div className='flex items-start justify-between'>
        <div>
          <div className='flex items-center justify-between'>
            <h1 className='text-2xl font-bold'>{t('posts_page.title')}</h1>
          </div>

          <p className='text-gray-0 my-2'>{t('posts_page.description')}</p>
        </div>

        <Link href='/dashboard/creative'>
          <Button
            className='rounded-lg'
            iconEnd={<CirclePlus className='fill-background text-primary' />}
          >
            {t('posts_page.create_new_post')}
          </Button>
        </Link>
      </div>

      <DataTable
        columns={postColumnsDef}
        data={allPosts ?? []}
        isLoading={isPending}
        isError={isError}
        error={error}
        refetch={refetch}
        gridView={(postItem: PostSuggestion) => (
          <GridViewMode post={postItem} />
        )}
        initialColumnVisibility={{
          CreationTimestamp: false,
        }}
        searchConfig={searchConfig}
        filterConfig={filterConfig}
      />

      <PostDetail />
    </div>
  );
}
