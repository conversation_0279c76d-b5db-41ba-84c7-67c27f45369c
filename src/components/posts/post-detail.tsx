import { useTranslations } from 'next-intl';
import React from 'react';

import { usePostsById } from '@/api/hooks/posts/queries';
import { usePostStore } from '@/stores/post-store';

import { StatusBadge } from '@/components/posts/atoms/status-badge';
import { PostInteractionMetrics } from '@/components/posts/molecules/post-interaction-metric';
import { PostMediaDisplay } from '@/components/posts/molecules/post-media-display';
import { PostOverviewMetrics } from '@/components/posts/molecules/post-overview-metric';
import { PostScoreSection } from '@/components/posts/molecules/post-score-section';
import { PostSheetHeader } from '@/components/posts/molecules/post-sheet-header';
import { Sheet, SheetContent } from '@/components/ui/sheet';

export function PostDetail() {
  const t = useTranslations();

  const { openPostDetail, setOpenPostDetail, posts } = usePostStore();
  const { data: selectedPost, isLoading: isLoadingPost } = usePostsById(
    posts?.current
  );

  if (isLoadingPost) {
    return (
      <Sheet open={openPostDetail} onOpenChange={setOpenPostDetail}>
        <SheetContent className='flex flex-col gap-0 rounded-s-xl p-0'>
          <PostSheetHeader />

          <div className='grid flex-1 auto-rows-min gap-6 overflow-y-auto bg-[#FAFAFA] py-4'>
            {t('loading')}
          </div>
        </SheetContent>
      </Sheet>
    );
  }

  if (!selectedPost) {
    return (
      <Sheet open={openPostDetail} onOpenChange={setOpenPostDetail}>
        <SheetContent className='flex flex-col gap-0 rounded-s-xl p-0'>
          <div>No post selected</div>
        </SheetContent>
      </Sheet>
    );
  }

  return (
    <Sheet open={openPostDetail} onOpenChange={setOpenPostDetail}>
      <SheetContent className='flex flex-col gap-0 rounded-s-xl p-0'>
        <PostSheetHeader />

        <div className='grid flex-1 auto-rows-min gap-6 overflow-hidden overflow-y-auto rounded-s-xl bg-[#FAFAFA] py-4'>
          {selectedPost.Medias && (
            <PostMediaDisplay
              media={selectedPost.Medias}
              adType={selectedPost.AdType}
              altText={selectedPost.Ad.message.Text}
            />
          )}

          <div className='grid gap-3 px-3'>
            <h2 className='text-sm font-semibold'>{selectedPost.Ad.title}</h2>

            <StatusBadge status={selectedPost.Status} />

            <p className='text-sm text-gray-700'>
              {selectedPost?.Ad.message.Text}
            </p>

            <PostScoreSection score={selectedPost.Score} />

            <PostOverviewMetrics />

            <PostInteractionMetrics />
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
