import { useTranslations } from 'next-intl';
import React from 'react';

import { PostSuggestion } from '@/api/models/dtos/post.dto';

import { GaugeChart } from '@/components/creative/atoms/gauge-chart';
import { Progress } from '@/components/ui/progress';

interface PostScoreSectionProps {
  score: PostSuggestion['Score'];
}

export function PostScoreSection({ score }: PostScoreSectionProps) {
  const t = useTranslations();

  return (
    <div className='flex items-center gap-2'>
      <GaugeChart
        isMonoColor={!score}
        label={t('score')}
        value={Number(score.PopularityScore)}
        size='sm'
        animated={true}
        className='w-2/5'
      />

      <div className='grid flex-1 gap-3'>
        <ScoreProgress
          label={t('awareness')}
          value={Number(score.AwarenessScore)}
          progressColor='bg-[#30AD43]'
        />

        <ScoreProgress
          label={t('consideration')}
          value={Number(score.ConsiderationScore)}
          progressColor='bg-[#84BD32]'
        />

        <ScoreProgress
          label={t('conversion')}
          value={Number(score.ConversionScore)}
          progressColor='bg-[#FF8888]'
        />

        <ScoreProgress
          label={t('loyalty')}
          value={Number(score.LoyaltyScore)}
          progressColor='bg-[#D1D80F]'
        />
      </div>
    </div>
  );
}

interface ScoreProgressProps {
  label: string;
  value: number;
  progressColor: string;
}

function ScoreProgress({ label, value, progressColor }: ScoreProgressProps) {
  return (
    <div className='relative'>
      <div className='mb-1 flex items-center justify-between'>
        <p className='text-xs'>{label}:</p>
        <p className='text-xs font-medium'>{value ?? 0}%</p>
      </div>
      <Progress
        value={Number(value)}
        className='h-1.5 bg-[#E4E4E7]'
        progressIndicatorClassName={progressColor}
      />
    </div>
  );
}
