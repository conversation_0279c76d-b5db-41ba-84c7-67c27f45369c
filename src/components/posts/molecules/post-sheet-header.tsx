import { useTranslations } from 'next-intl';
import React from 'react';

import { ChevronLeft, ChevronRight } from 'lucide-react';

import { cn } from '@/lib/utils';

import {
  useDeletePost,
  useMarkPostAsFavorite,
} from '@/api/hooks/posts/mutations';
import { usePostsById } from '@/api/hooks/posts/queries';
import { useRouter } from '@/i18n/routing';
import { useCreativePostStore } from '@/stores/creative-post-store';
import { usePostStore } from '@/stores/post-store';

import { Icons } from '@/components/shared/icons';
import { Button } from '@/components/ui/button';
import {
  SheetTitle,
  SheetHeader,
  SheetDescription,
} from '@/components/ui/sheet';

export function PostSheetHeader() {
  const t = useTranslations();
  const router = useRouter();

  const { posts, setOpenPostDetail, goToNext, goToPrevious } = usePostStore();
  const { setCurrentPostSuggestion, setPreviousPage } = useCreativePostStore();

  const { data: selectedPost } = usePostsById(posts?.current);
  const { mutateAsync: handleFavorite } = useMarkPostAsFavorite();
  const { mutateAsync: handleDelete } = useDeletePost();

  return (
    <SheetHeader className='flex flex-row items-center justify-between space-y-0 px-4 py-2'>
      <SheetTitle className='flex items-center justify-center gap-2'>
        <Button
          size='sm'
          className='flex items-center gap-1 bg-[#006FEE33] font-normal text-[#006FEE] hover:text-background'
          iconStart={<Icons.calendarAdd />}
        >
          {t('schedule')}
        </Button>
        <Button
          variant='outline'
          size='sm'
          className='flex items-center gap-1 border-blue-600 bg-white font-normal text-blue-600'
          iconStart={<Icons.penEdit />}
          onClick={() => {
            setCurrentPostSuggestion(selectedPost ?? null);
            setPreviousPage('/dashboard/posts');
            router.push('/dashboard/creative/edit-text');
          }}
        >
          {t('update')}
        </Button>

        <Button
          size='setting'
          className={cn({
            'bg-[#F5A52433] text-muted-foreground hover:bg-[#F5A524]/70 hover:text-background':
              !selectedPost?.Favorite,
            'bg-[#F5A52433] text-[#F5A524] hover:bg-[#F5A524]/70 hover:text-background':
              selectedPost?.Favorite,
          })}
          onClick={async () => {
            setOpenPostDetail(false);
            await handleFavorite(selectedPost?.Id ?? '');
          }}
        >
          <Icons.star />
        </Button>
        <Button
          size='icon'
          className='size-8 bg-[#********] text-[#F31260] hover:bg-[#F31260]/70 hover:text-background'
          onClick={async () => {
            setOpenPostDetail(false);
            await handleDelete(selectedPost?.Id ?? '');
          }}
        >
          <Icons.trash />
        </Button>
      </SheetTitle>
      <SheetDescription className='sr-only'>Post Details</SheetDescription>

      <div className='flex items-center gap-1'>
        <Button
          variant='outline'
          size='setting'
          className='border-[1.5px] border-[#71717A] bg-[#F4F4F5] text-[#71717A]'
          onClick={goToPrevious}
        >
          <ChevronLeft />
        </Button>
        <Button
          variant='outline'
          size='setting'
          className='border-[1.5px] border-[#71717A] bg-[#F4F4F5] text-[#71717A]'
          onClick={goToNext}
        >
          <ChevronRight />
        </Button>
      </div>
    </SheetHeader>
  );
}
