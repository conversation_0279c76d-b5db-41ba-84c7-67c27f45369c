'use client';

import Image from 'next/image';
import React, { useState } from 'react';

import { ImageOff } from 'lucide-react';

import { PostSuggestion } from '@/api/models/dtos/post.dto';

import { getFallbackInitials } from '@/lib/get-fallback-initials';
import { cn } from '@/lib/utils';

import { useBrandById } from '@/api/hooks/brand/queries';
import { useMarkPostAsFavorite } from '@/api/hooks/posts/mutations';
import { useRouter } from '@/i18n/routing';
import { useCreativePostStore } from '@/stores/creative-post-store';
import { usePostStore } from '@/stores/post-store';

import { StatusBadge } from '@/components/posts/atoms/status-badge';
import { Icons } from '@/components/shared/icons';
import PdfWrapper from '@/components/shared/pdf-wrapper';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Card, CardHeader, CardContent } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';

interface GridViewModeProps {
  post: PostSuggestion;
}

export function GridViewMode({ post }: GridViewModeProps) {
  const [showMore, setShowMore] = useState(false);
  const router = useRouter();

  const { setPosts, setOpenPostDetail } = usePostStore();
  const { setCurrentPostSuggestion, setPreviousPage } = useCreativePostStore();

  const { data: brand } = useBrandById(post.BrandId);
  const { mutateAsync: handleFavorite } = useMarkPostAsFavorite();

  const handleViewPost = () => {
    setPosts({
      previous: post.Id,
      current: post.Id,
      next: post.Id,
    });
    setOpenPostDetail(true);
  };

  function handleEditPost() {
    setCurrentPostSuggestion(post);
    setPreviousPage('/dashboard/posts');
    router.push('/dashboard/creative/edit-text');
  }

  return (
    <Card key={post.Id} className='w-full overflow-hidden rounded-3xl'>
      <CardHeader className='px-5 py-2'>
        <div className='flex items-center gap-4'>
          <Avatar className='size-11 border'>
            <AvatarImage
              src={
                brand?.GcsLinkPublic ?? '/placeholder.svg?height=40&width=40'
              }
              alt={brand?.Description ?? 'Brand Image'}
            />
            <AvatarFallback>
              {getFallbackInitials(brand?.Name ?? '')}
            </AvatarFallback>
          </Avatar>
          <div className='w-full space-y-1.5'>
            <h3 className='text-sm font-semibold text-[#3F3F46]'>
              {brand?.Name}
            </h3>

            <StatusBadge status={post.Status} />

            <div>
              <div className='flex items-center justify-between gap-2'>
                <span className='text-xs'>Popularity Score</span>
                <span className='text-xs'>{post.Score.PopularityScore}%</span>
              </div>
              <Progress value={70} className='h-1.5' />
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent className='flex flex-col gap-2 px-4 py-2'>
        <p className='line-clamp-1 text-xs text-[#A1A1AA]'>{post.Ad.title}</p>
        <p
          className={cn('line-clamp-1 cursor-pointer text-xs text-[#A1A1AA]', {
            'line-clamp-none': showMore,
          })}
          onClick={() => setShowMore(!showMore)}
        >
          {post.Ad.message.Text}
        </p>

        <div className='relative flex h-48 items-center justify-center overflow-hidden rounded-xl'>
          {post.Medias ? (
            post.AdType === 'text_for_pdf' ? (
              <PdfWrapper src={post.Medias[0]?.GcsLinkPublic} height='100%' />
            ) : (
              <Image
                className='h-full w-full rounded-xl object-cover'
                src={post.Medias[0]?.GcsLinkPublic}
                alt={post.Ad.message.Text}
                width={post.Medias[0]?.Width}
                height={post.Medias[0]?.Height}
              />
            )
          ) : (
            <ImageOff className='size-24 text-muted-foreground' />
          )}
          <div className='absolute bottom-7 end-0 start-0 z-10 flex items-center justify-center'>
            <div className='flex h-7 items-center divide-x-2 divide-[#5F91E3] overflow-hidden rounded-md border-2 border-[#5F91E3]'>
              <Button
                variant='ghost'
                size='setting'
                className='rounded-none bg-background px-5 text-gray-400 hover:text-primary'
                onClick={handleViewPost}
              >
                <Icons.eye />
              </Button>
              <Button
                variant='ghost'
                size='setting'
                className={cn(
                  'rounded-none bg-background px-5',
                  post.Favorite
                    ? 'text-[#F5A524] hover:text-muted-foreground'
                    : 'text-muted-foreground hover:text-[#F5A524]'
                )}
                onClick={async () => await handleFavorite(post.Id)}
              >
                <Icons.star />
              </Button>
              <Button
                variant='ghost'
                size='setting'
                className='rounded-none bg-background px-5 text-muted-foreground hover:text-primary'
                onClick={handleEditPost}
              >
                <Icons.penEdit />
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
