import { useTranslations } from 'next-intl';
import React from 'react';

import { Eye, Radio, Heart, MousePointerClick } from 'lucide-react';

import { Card } from '@/components/ui/card';

export function PostOverviewMetrics() {
  const t = useTranslations();

  return (
    <div className='my-4'>
      <h3 className='mb-3 font-bold text-[#162F58]'>{t('overview')}</h3>
      <div className='grid grid-cols-4 gap-2'>
        <Card className='flex flex-col items-center border p-2'>
          <Eye className='mb-1 h-5 w-5 text-yellow-500' />
          <span className='text-xs'>{t('views')}</span>
        </Card>
        <Card className='flex flex-col items-center border p-2'>
          <Radio className='mb-1 h-5 w-5 text-purple-500' />
          <span className='text-xs'>{t('reach')}</span>
        </Card>
        <Card className='flex flex-col items-center border p-2'>
          <Heart className='mb-1 h-5 w-5 text-pink-500' />
          <span className='text-xs'>{t('interactions')}</span>
        </Card>
        <Card className='flex flex-col items-center border p-2'>
          <MousePointerClick className='mb-1 h-5 w-5 text-green-500' />
          <span className='text-xs'>{t('links_clicks')}</span>
        </Card>
      </div>
    </div>
  );
}
