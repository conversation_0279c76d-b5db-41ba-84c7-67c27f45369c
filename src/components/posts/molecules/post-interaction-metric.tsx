import { useTranslations } from 'next-intl';
import React from 'react';

import { Heart, Share2, Bookmark, MessageCircle } from 'lucide-react';

import { Card } from '@/components/ui/card';

export function PostInteractionMetrics() {
  const t = useTranslations();

  return (
    <div>
      <h3 className='mb-3 font-bold text-[#162F58]'>{t('interactions')}</h3>
      <div className='grid grid-cols-4 gap-2'>
        <Card className='flex flex-col items-center border p-2'>
          <Heart className='mb-1 h-5 w-5 text-pink-500' />
          <span className='text-xs'>{t('reactions')}</span>
        </Card>
        <Card className='flex flex-col items-center border p-2'>
          <MessageCircle className='mb-1 h-5 w-5 text-blue-500' />
          <span className='text-xs'>{t('comments')}</span>
        </Card>
        <Card className='flex flex-col items-center border p-2'>
          <Share2 className='mb-1 h-5 w-5 text-purple-500' />
          <span className='text-xs'>{t('shares')}</span>
        </Card>
        <Card className='flex flex-col items-center border p-2'>
          <Bookmark className='mb-1 h-5 w-5 text-gray-500' />
          <span className='text-xs'>{t('saves')}</span>
        </Card>
      </div>
    </div>
  );
}
