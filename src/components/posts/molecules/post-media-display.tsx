import Image from 'next/image';
import React from 'react';

import { PostSuggestion } from '@/api/models/dtos/post.dto';

import PdfWrapper from '@/components/shared/pdf-wrapper';

interface PostMediaDisplayProps {
  media: PostSuggestion['Medias'];
  adType: PostSuggestion['AdType'];
  altText: string;
}

export function PostMediaDisplay({
  media,
  adType,
  altText,
}: PostMediaDisplayProps) {
  if (!media || media.length === 0) {
    return null;
  }

  const mediaItem = media[0];

  return (
    <div className='flex items-start justify-center overflow-hidden'>
      <div className='w-1/2'>
        {adType === 'text_for_pdf' ? (
          <PdfWrapper src={mediaItem.GcsLinkPublic} height='100%' />
        ) : (
          <Image
            className='h-full w-full rounded-xl object-cover'
            src={mediaItem.GcsLinkPublic}
            alt={altText}
            width={mediaItem.Width}
            height={mediaItem.Height}
          />
        )}
      </div>
    </div>
  );
}
