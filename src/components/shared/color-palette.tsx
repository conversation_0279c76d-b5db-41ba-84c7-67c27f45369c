'use client';

import { useState } from 'react';
import { SketchPicker } from 'react-color';

import { Plus, Trash2 } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';

interface ColorPaletteProps {
  value: string[];
  onChange: (value: string[]) => void;
  maxColors?: number;
  selectColorText?: string;
}

export default function ColorPalette({ value, onChange, selectColorText }: ColorPaletteProps) {
  const [selectedColor, setSelectedColor] = useState('#000000');


  const addColor = () => {
    const emptyIndex = value.findIndex((color) => !color);
    if (emptyIndex !== -1) {
      const newPalette = [...value];
      newPalette[emptyIndex] = selectedColor;
      onChange(newPalette);
    }
  };

  const removeColor = (index: number) => {
    const newPalette = [...value];
    newPalette[index] = '';
    onChange(newPalette);
  };

  return (
    <div className='grid gap-6'>
      <div>
        <Label
          htmlFor='colorPicker'
          className='mb-2 block text-base font-medium'
        >
          {selectColorText} <span className='text-red-500'>*</span>
        </Label>
        <div className='grid grid-cols-[auto_repeat(2,_1fr)] grid-rows-3 gap-6'>
          <SketchPicker
            disableAlpha={true}
            presetColors={[]}
            color={selectedColor}
            onChange={(color) => setSelectedColor(color.hex)}
            className='row-span-3'
            styles={{
              default: {
                picker: {
                  borderRadius: '10px',
                },
                color: {
                  display: 'none',
                },
                saturation: {
                  borderRadius: '8px',
                },
                sliders: {
                  marginTop: '5px',
                  overflow: 'visible',
                },
                hue: {
                  borderRadius: '20px',
                },
              },
            }}
          />
          {Array.from({ length: 6 }).map((_, index) => (
            <ColorItem
              key={index}
              index={index}
              color={value[index]}
              removeColor={removeColor}
              addColor={addColor}
            />
          ))}
        </div>
      </div>
    </div>
  );
}

interface ColorItemProps {
  index: number;
  color: string;
  removeColor: (index: number) => void;
  addColor: () => void;
}

const ColorItem = ({ index, color, removeColor, addColor }: ColorItemProps) => (
  <div className='flex h-14 items-center gap-2 rounded-xl border border-gray-500'>
    {color ? (
      <>
        <div
          className='ms-3 h-8 w-8 rounded-full'
          style={{ backgroundColor: color }}
        />
        <span className='flex-1 font-mono text-sm'>{color.toUpperCase()}</span>
        <Button
          variant='ghost'
          size='icon'
          onClick={() => removeColor(index)}
          className='me-3 h-8 w-8 text-rose-500 hover:text-rose-600'
        >
          <Trash2 className='h-4 w-4' />
          <span className='sr-only'>Remove</span>
        </Button>
      </>
    ) : (
      <>
        <div className='ms-3 flex h-8 w-8 items-center justify-center rounded-full border border-gray-600'>
          <Plus
            className='h-4 w-4 cursor-pointer text-black'
            onClick={addColor}
          />
        </div>
        <span className='flex-1 font-mono text-sm text-gray-400'>#000000</span>
        <Button
          variant='ghost'
          size='icon'
          disabled
          className='me-3 h-8 w-8 text-gray-200'
        >
          <Trash2 className='h-4 w-4' />
          <span className='sr-only'>Remove</span>
        </Button>
      </>
    )}
  </div>
);
