import { cn } from '@/lib/utils';

import { DualRangeSlider } from '@/components/ui/dual-range-slider';
import { Label } from '@/components/ui/label';

import { useFieldContext } from '.';
import { FieldErrors } from './field-errors';

interface DualSliderFieldProps {
  label: string;
  showRangeOnLabel?: boolean;
  labelClassName?: string;
  required?: boolean;
  thumbLabel?: (value: number | undefined) => React.ReactNode;
  min?: number;
  max?: number;
  step?: number;
}

export function DualSliderField({
  label,
  labelClassName,
  showRangeOnLabel = false,
  required = true,
  thumbLabel,
  min = 18,
  max = 60,
  step = 1,
}: DualSliderFieldProps) {
  const field = useFieldContext<number[]>();

  const value =
    Array.isArray(field.state.value) && field.state.value.length === 2
      ? field.state.value
      : [min, max];

  return (
    <div className='space-y-2'>
      <div className='grid gap-4'>
        {label && (
          <Label htmlFor={field.name} className={cn(labelClassName)}>
            {label}
            {showRangeOnLabel && (
              <output className='ms-0.5'>
                ({field.state.value[0]} - {field.state.value[1]})
              </output>
            )}
            {required && <span className='ms-0.5 text-destructive'>*</span>}
          </Label>
        )}
        <DualRangeSlider
          className='text-xs'
          label={thumbLabel}
          value={value}
          onValueChange={field.handleChange}
          min={min}
          max={max}
          step={step}
        />
      </div>
      <FieldErrors meta={field.state.meta} />
    </div>
  );
}
