'use client';

import {
  useRef,
  type JSX,
  useState,
  type DragEvent,
  type ChangeEvent,
} from 'react';

import { cn } from '@/lib/utils';

import { Icons } from '@/components/shared/icons';

interface FileUploadProps {
  onFileChange?: (file: File | null) => void;
  className?: string;
  addPdfText?: string;
  dropFilesText?: string;
  uploadFileText?: string ;
}

export function FileUpload({
  onFileChange,
  className,
  addPdfText = 'Add PDF',
  dropFilesText = 'Drop files here or click to upload',
  uploadFileText = 'Upload file',
}: FileUploadProps): JSX.Element {
  const [file, setFile] = useState<File | null>(null);
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const fileInputRef = useRef<HTMLInputElement | null>(null);

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>): void => {
    const selectedFile = e.target.files?.[0] ?? null;
    setFile(selectedFile);
    onFileChange?.(selectedFile);
  };

  const handleDragOver = (e: DragEvent<HTMLDivElement>): void => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: DragEvent<HTMLDivElement>): void => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e: DragEvent<HTMLDivElement>): void => {
    e.preventDefault();
    setIsDragging(false);

    const droppedFile = e.dataTransfer.files?.[0];
    if (droppedFile && droppedFile.type === 'application/pdf') {
      setFile(droppedFile);
      onFileChange?.(droppedFile);
    } else {
      alert('Please upload a PDF file');
    }
  };

  const handleClick = (): void => {
    fileInputRef.current?.click();
  };

  return (
    <div className={cn('w-full', className)}>
      <h3 className='mb-2 text-sm font-medium'>{uploadFileText}</h3>
      <div
        className={cn(
          'cursor-pointer rounded-md border border-dashed bg-blue-50/50 p-6',
          isDragging && 'border-blue-500 bg-blue-100/50',
          'transition-colors duration-200'
        )}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={handleClick}
      >
        <input
          type='file'
          ref={fileInputRef}
          onChange={handleFileChange}
          className='hidden'
          accept='application/pdf,.pdf'
        />

        {file ? (
          <div className='flex items-center'>
            <div className='mr-4 flex h-10 w-10 items-center justify-center text-blue-500'>
              <Icons.uploadFile />
            </div>
            <div>
              <p className='text-sm font-medium text-blue-600'>{file.name}</p>
              <p className='text-xs text-muted-foreground'>
                {(file.size / 1024 / 1024).toFixed(2)} MB
              </p>
            </div>
          </div>
        ) : (
          <div className='flex items-center'>
            <div className='mr-4 flex h-10 w-10 items-center justify-center text-blue-500'>
              <Icons.uploadFile />
            </div>
            <div>
              <p className='text-sm font-medium text-blue-600'>{addPdfText}</p>
              <p className='text-xs text-muted-foreground'>
                {dropFilesText}
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
