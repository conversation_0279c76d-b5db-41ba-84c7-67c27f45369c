'use client';

import * as React from 'react';

import { useStore } from '@tanstack/react-form';

import { Spinner } from '@/components/shared/spinner';
import { Button, ButtonProps } from '@/components/ui/button';

import { useFormContext } from './index';

interface SubmitButtonProps extends ButtonProps {
  children: React.ReactNode;
}

export const SubmitButton = ({ children, ...props }: SubmitButtonProps) => {
  const form = useFormContext();

  const [isSubmitting, canSubmit] = useStore(form.store, (state) => [
    state.isSubmitting,
    state.canSubmit,
  ]);

  return (
    <Button type='submit' disabled={isSubmitting || !canSubmit} {...props}>
      {isSubmitting ? <Spinner variant='circle' /> : children}
    </Button>
  );
};
