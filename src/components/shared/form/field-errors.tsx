'use client';

import { ZodError } from 'zod';

import { AnyFieldMeta } from '@tanstack/react-form';

type FieldErrorsProps = {
  meta: AnyFieldMeta;
};

export const FieldErrors = ({ meta }: FieldErrorsProps) => {
  if (!meta.isTouched) return null;

  return meta.errors.map(({ message }: ZodError, index) => (
    <p key={index} className='text-xs font-normal text-destructive'>
      {message}
    </p>
  ));
};
