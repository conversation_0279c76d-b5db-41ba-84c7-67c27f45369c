import React, { useMemo, useState } from 'react';

import { CheckIcon, ChevronDownIcon } from 'lucide-react';

import { cn } from '@/lib/utils';

import { useDirection } from '@/hooks/use-direction';

import { FieldErrors } from '@/components/shared/form/field-errors';
import { useFieldContext } from '@/components/shared/form/index';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandItem,
  CommandList,
  CommandEmpty,
  CommandGroup,
  CommandInput,
} from '@/components/ui/command';
import { Label } from '@/components/ui/label';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';

interface TimeZoneSelectFieldProps {
  label?: string;
  required?: boolean;
}

export function TimeZoneSelectField({
  label = 'Time Zone',
  required = true,
}: TimeZoneSelectFieldProps) {
  const field = useFieldContext<string>();

  const [open, setOpen] = useState<boolean>(false);
  const { locale } = useDirection();

  const timezones = Intl.supportedValuesOf('timeZone');

  const formattedTimezones = useMemo(() => {
    return timezones
      .map((timezone) => {
        const formatter = new Intl.DateTimeFormat(locale, {
          timeZone: timezone,
          timeZoneName: 'shortOffset',
        });
        const parts = formatter.formatToParts(new Date());
        const offset =
          parts.find((part) => part.type === 'timeZoneName')?.value || '';
        const modifiedOffset = offset === 'GMT' ? 'GMT+0' : offset;

        return {
          value: timezone,
          label: `(${modifiedOffset}) ${timezone.replace(/_/g, ' ')}`,
          numericOffset: parseInt(
            offset.replace('GMT', '').replace('+', '') || '0'
          ),
        };
      })
      .sort((a, b) => a.numericOffset - b.numericOffset);
  }, [locale, timezones]);

  return (
    <div className='space-y-2'>
      <div className='grid gap-2'>
        <Label htmlFor={field.name}>
          {label}
          {required && <span className='ms-0.5 text-destructive'>*</span>}
        </Label>
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger asChild>
            <Button
              id={field.name}
              variant='outline'
              role='combobox'
              aria-expanded={open}
              className='w-full justify-between border-input bg-background px-3 font-normal outline-none outline-offset-0 hover:bg-background focus-visible:outline-[3px]'
            >
              <span
                className={cn(
                  'truncate',
                  !field.state.value && 'text-muted-foreground'
                )}
              >
                {field.state.value
                  ? formattedTimezones.find(
                      (timezone) => timezone.value === field.state.value
                    )?.label
                  : 'Select timezone'}
              </span>
              <ChevronDownIcon
                size={16}
                className='shrink-0 text-muted-foreground/80'
                aria-hidden='true'
              />
            </Button>
          </PopoverTrigger>
          <PopoverContent
            className='w-full min-w-[var(--radix-popper-anchor-width)] border-input p-0'
            align='start'
          >
            <Command
              filter={(value, search) => {
                const normalizedValue = value.toLowerCase();
                const normalizedSearch = search
                  .toLowerCase()
                  .replace(/\s+/g, '');
                return normalizedValue.includes(normalizedSearch) ? 1 : 0;
              }}
            >
              <CommandInput placeholder='Search timezone...' />
              <CommandList>
                <CommandEmpty>No timezone found.</CommandEmpty>
                <CommandGroup>
                  {formattedTimezones.map(({ value: itemValue, label }) => (
                    <CommandItem
                      key={itemValue}
                      value={itemValue}
                      onSelect={(currentValue) => {
                        field.handleChange(
                          currentValue === field.state.value ? '' : currentValue
                        );
                        setOpen(false);
                      }}
                    >
                      {label}
                      {field.state.value === itemValue && (
                        <CheckIcon size={16} className='ms-auto' />
                      )}
                    </CommandItem>
                  ))}
                </CommandGroup>
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>
      </div>
      <FieldErrors meta={field.state.meta} />
    </div>
  );
}
