'use client';

import React from 'react';

import { cn } from '@/lib/utils';

import { Label } from '@/components/ui/label';
import { Skeleton } from '@/components/ui/skeleton';
import { Textarea } from '@/components/ui/textarea';

import { useFieldContext } from '.';
import { FieldErrors } from './field-errors';

type TextareaFieldProps = {
  label: string;
  labelClassName?: string;
  className?: string;
  required?: boolean;
  isLoading?: boolean;
} & React.TextareaHTMLAttributes<HTMLTextAreaElement>;

export const TextareaField = ({
  label,
  labelClassName,
  className,
  required = true,
  isLoading = false,
  ...textareaProps
}: TextareaFieldProps) => {
  const field = useFieldContext<string>();

  return (
    <div className='space-y-2'>
      <div className='grid gap-2'>
        <Label htmlFor={field.name} className={cn(labelClassName)}>
          {label}
          {required && <span className='ms-0.5 text-destructive'>*</span>}
        </Label>

        {isLoading ? (
          <Skeleton className='h-28 w-full rounded-xl' />
        ) : (
          <Textarea
            id={field.name}
            value={field.state.value}
            className={cn(
              'rounded-xl border-[#E4E4E7] [resize:none] placeholder:text-sm placeholder:text-[#A1A1AA]',
              className
            )}
            onChange={(e) => field.handleChange(e.target.value)}
            onBlur={field.handleBlur}
            {...textareaProps}
          />
        )}
      </div>
      <FieldErrors meta={field.state.meta} />
    </div>
  );
};
