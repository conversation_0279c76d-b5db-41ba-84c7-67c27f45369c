'use client';

import { createF<PERSON><PERSON><PERSON>, createFormHookContexts } from '@tanstack/react-form';

import { CheckboxField } from '@/components/shared/form/checkbox-field';
import { ColorPickerField } from '@/components/shared/form/color-picker-field';
import { DatePickerField } from '@/components/shared/form/date-picker-field';
import { DualSliderField } from '@/components/shared/form/dual-slider-field';
import { FileUploadField } from '@/components/shared/form/file-upload-field';
import { MultiSelectGroupedOptionsField } from '@/components/shared/form/multi-select-grouped-options-field';
import { RadioGroupField } from '@/components/shared/form/radio-group-field';
import { SelectField } from '@/components/shared/form/select-field';
import { SliderWithInputField } from '@/components/shared/form/slider-with-input-field';
import { SocialMediaRadioField } from '@/components/shared/form/social-media-radio-field';
import { SubmitButton } from '@/components/shared/form/submit-button';
import { TabsField } from '@/components/shared/form/tabs-field';
import { TextField } from '@/components/shared/form/text-field';
import { TextFieldWithInnerTags } from '@/components/shared/form/text-field-with-inner-tags';
import { TextareaField } from '@/components/shared/form/textarea-field';
import { TimeZoneSelectField } from '@/components/shared/form/time-zone-select-field';

export const { fieldContext, useFieldContext, formContext, useFormContext } =
  createFormHookContexts();

export const { useAppForm, withForm } = createFormHook({
  fieldComponents: {
    TextField,
    TabsField,
    RadioGroupField,
    TextFieldWithInnerTags,
    TextareaField,
    SliderWithInputField,
    CheckboxField,
    FileUploadField,
    SelectField,
    DualSliderField,
    SocialMediaRadioField,
    ColorPickerField,
    DatePickerField,
    TimeZoneSelectField,
    MultiSelectGroupedOptionsField,
  },
  formComponents: {
    SubmitButton,
  },
  fieldContext,
  formContext,
});
