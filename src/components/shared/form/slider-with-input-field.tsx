'use client';

import React from 'react';

import { cn } from '@/lib/utils';

import { useSliderWithInput } from '@/hooks/use-slider-with-input';

import { useFieldContext } from '@/components/shared/form/index';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';

interface SliderWithInputProps {
  minValue?: number;
  maxValue?: number;
  initialValue?: number[];
  label?: string;
  inlineLabel?: boolean;
  labelClassName?: string;
  required?: boolean;
  unit?: string;
  disabled?: boolean;
}

export function SliderWithInputField({
  minValue = 0,
  maxValue = 100,
  initialValue = [25],
  label,
  labelClassName,
  required = true,
  unit = 'px',
  disabled,
}: SliderWithInputProps) {
  const field = useFieldContext<number>();

  const {
    inputValues,
    handleInputChange,
    validateAndUpdateValue,
    handleSliderChange,
  } = useSliderWithInput({ minValue, maxValue, initialValue });

  return (
    <div className='flex items-center gap-1'>
      <Label
        htmlFor={field.name}
        className={cn('w-14 truncate', labelClassName)}
      >
        {label}
        {required && <span className='ms-0.5 text-destructive'>*</span>}
      </Label>
      <div className='flex grow items-center gap-2'>
        <Slider
          value={[field.state.value]}
          onValueChange={(newValue) => {
            handleSliderChange(newValue);
            field.handleChange(newValue[0]);
          }}
          min={minValue}
          max={maxValue}
          disabled={disabled}
          aria-label='Slider with input'
        />
        <div className='relative flex items-center'>
          <Input
            className='h-8 w-[4.5rem] px-2 py-1'
            type='text'
            inputMode='numeric'
            min={minValue}
            max={maxValue}
            value={field.state.value}
            disabled={disabled}
            onChange={(e) => {
              handleInputChange(e, 0);
              field.handleChange(e.target.valueAsNumber);
            }}
            onBlur={() => {
              validateAndUpdateValue(inputValues[0], 0);
              field.handleBlur();
            }}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                validateAndUpdateValue(inputValues[0], 0);
              } else if (e.key === 'ArrowUp') {
                const newValue = Math.min(maxValue, Number(inputValues[0]) + 1);
                validateAndUpdateValue(newValue.toString(), 0);
                handleSliderChange([newValue]);
                field.handleChange(newValue);
                e.preventDefault();
              } else if (e.key === 'ArrowDown') {
                const newValue = Math.max(minValue, Number(inputValues[0]) - 1);
                validateAndUpdateValue(newValue.toString(), 0);
                handleSliderChange([newValue]);
                field.handleChange(newValue);
                e.preventDefault();
              }
            }}
            aria-label='Enter value'
          />
          <span
            className={cn('pointer-events-none absolute end-2 text-sm', {
              'text-muted-foreground': disabled,
            })}
          >
            {unit}
          </span>
        </div>
      </div>
    </div>
  );
}
