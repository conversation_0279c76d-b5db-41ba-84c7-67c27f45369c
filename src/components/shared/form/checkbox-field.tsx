'use client';

import React from 'react';

import { cn } from '@/lib/utils';

import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';

import { FieldErrors } from './field-errors';
import { useFieldContext } from './index';

type CheckboxFieldProps = {
  label: string | React.ReactNode;
  description?: string;
  labelClassName?: string;
  checkboxClassName?: string;
  required?: boolean;
};

export const CheckboxField = ({
  label,
  description,
  required = true,
  labelClassName,
  checkboxClassName,
}: CheckboxFieldProps) => {
  const field = useFieldContext<boolean>();

  return (
    <div className='space-y-2'>
      <div className='flex items-center gap-2'>
        <Checkbox
          id={field.name}
          checked={field.state.value}
          onCheckedChange={(checked) => {
            field.handleChange(checked === true);
          }}
          className={cn('', checkboxClassName)}
          onBlur={field.handleBlur}
        />
        <div className='grid gap-1.5 leading-none'>
          <Label
            htmlFor={field.name}
            className={cn('cursor-pointer', labelClassName)}
          >
            {label}
            {required && <span className='ms-0.5 text-destructive'>*</span>}
          </Label>
          {description && (
            <p className='text-sm text-muted-foreground'>{description}</p>
          )}
        </div>
      </div>
      <FieldErrors meta={field.state.meta} />
    </div>
  );
};
