'use client';

import React, { useMemo, useState } from 'react';

import { EyeIcon, EyeOffIcon } from 'lucide-react';

import { cn } from '@/lib/utils';

import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

import { useFieldContext } from '.';
import { FieldErrors } from './field-errors';

type TextFieldProps = {
  label?: string;
  labelClassName?: string;
  startAddOn?: React.ReactNode;
  wrapperClassName?: string;
  endAddOn?: React.ReactNode;
  required?: boolean;
} & React.InputHTMLAttributes<HTMLInputElement>;

export const TextField = ({
  label,
  labelClassName,
  startAddOn,
  endAddOn,
  wrapperClassName,
  type = 'text',
  required = true,
  ...inputProps
}: TextFieldProps) => {
  const field = useFieldContext<string>();
  const [isVisible, setIsVisible] = useState(false);

  const isPasswordType = type === 'password';
  const inputType = isPasswordType ? (isVisible ? 'text' : 'password') : type;

  const inputClassName = useMemo(
    () =>
      cn(
        'peer',
        startAddOn && 'ps-10',
        (endAddOn || isPasswordType) && 'pe-10',
        inputProps.className
      ),
    [startAddOn, endAddOn, isPasswordType, inputProps.className]
  );

  const startAddOnContent = useMemo(
    () =>
      startAddOn && (
        <span className='absolute inset-y-0 start-0 flex items-center p-2 text-muted-foreground'>
          {startAddOn}
        </span>
      ),
    [startAddOn]
  );

  const endContent = useMemo(() => {
    if (isPasswordType) {
      return (
        <span
          onClick={() => setIsVisible((v) => !v)}
          className='absolute inset-y-0 end-0 flex items-center p-2 text-muted-foreground'
          aria-label={isVisible ? 'Hide password' : 'Show password'}
        >
          {isVisible ? (
            <EyeOffIcon className='size-5' />
          ) : (
            <EyeIcon className='size-5' />
          )}
        </span>
      );
    }
    return (
      endAddOn && (
        <span className='absolute inset-y-0 end-0 flex items-center p-2 text-muted-foreground'>
          {endAddOn}
        </span>
      )
    );
  }, [isPasswordType, isVisible, endAddOn]);

  return (
    <div className={cn('space-y-2', wrapperClassName)}>
      <div className='grid gap-2'>
        {label && (
          <Label htmlFor={field.name} className={cn(labelClassName)}>
            {label}
            {required && <span className='ms-0.5 text-destructive'>*</span>}
          </Label>
        )}

        <div className='relative'>
          <Input
            id={field.name}
            value={field.state.value}
            onChange={(e) => field.handleChange(e.target.value)}
            onBlur={field.handleBlur}
            className={inputClassName}
            type={inputType}
            {...inputProps}
          />
          {startAddOnContent}
          {endContent}
        </div>
      </div>
      <FieldErrors meta={field.state.meta} />
    </div>
  );
};
