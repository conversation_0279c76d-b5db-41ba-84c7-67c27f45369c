import React from 'react';
import { SketchPicker } from 'react-color';

import { cn } from '@/lib/utils';

import { FieldErrors } from '@/components/shared/form/field-errors';
import { useFieldContext } from '@/components/shared/form/index';
import { Icons } from '@/components/shared/icons';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';

interface ColorPickerFieldProps {
  label?: string;
  required?: boolean;
  defaultPresets?: string[];
}

export function ColorPickerField({
  label,
  required = true,
  defaultPresets,
}: ColorPickerFieldProps) {
  const field = useFieldContext<string>();

  return (
    <div className='space-y-2'>
      <div className='flex flex-col gap-2'>
        {label && (
          <Label htmlFor={field.name}>
            {label}
            {required && <span className='ms-0.5 text-destructive'>*</span>}
          </Label>
        )}

        <div className='grid grid-cols-10 place-items-center rounded-xl border border-[#D4D4D8] p-1'>
          {defaultPresets?.map((option) => (
            <div
              key={option}
              style={{ backgroundColor: option }}
              className={cn('size-6 rounded-full border hover:cursor-pointer', {
                'border-2 border-black': field.state.value === option,
              })}
              onClick={() => field.handleChange(option)}
            />
          ))}
          <Popover>
            <PopoverTrigger asChild>
              <Button
                size='setting'
                variant='ghost'
                className='size-6 rounded-full'
              >
                <Icons.tear
                  className='text-[#3776DC]'
                  fill={field.state.value}
                />
              </Button>
            </PopoverTrigger>
            <PopoverContent className='w-fit rounded-full bg-transparent p-0 shadow-none'>
              <SketchPicker
                disableAlpha
                presetColors={[]}
                color={field.state.value}
                onChange={(color) => {
                  field.handleChange(color.hex);
                }}
                className='row-span-3'
                styles={{
                  default: {
                    picker: {
                      borderRadius: '10px',
                    },
                    color: {
                      display: 'none',
                    },
                    saturation: {
                      borderRadius: '8px',
                    },
                    sliders: {
                      marginTop: '5px',
                      overflow: 'visible',
                    },
                    hue: {
                      borderRadius: '20px',
                    },
                  },
                }}
              />
            </PopoverContent>
          </Popover>
        </div>
      </div>
      <FieldErrors meta={field.state.meta} />
    </div>
  );
}
