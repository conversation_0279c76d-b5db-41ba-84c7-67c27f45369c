'use client';

import * as React from 'react';
import { toast } from 'sonner';

import { X, CloudUpload } from 'lucide-react';

import { cn } from '@/lib/utils';

import { FieldErrors } from '@/components/shared/form/field-errors';
import { useFieldContext } from '@/components/shared/form/index';
import { Button } from '@/components/ui/button';
import {
  FileUpload,
  FileUploadItem,
  FileUploadList,
  FileUploadTrigger,
  FileUploadDropzone,
  FileUploadItemDelete,
  FileUploadItemPreview,
  FileUploadItemMetadata,
} from '@/components/ui/file-upload';
import { Label } from '@/components/ui/label';

type fileUploadFieldProps = {
  label: string;
  labelClassName?: string;
  className?: string;
  required?: boolean;
  labelAction?: React.ReactNode;
  maxFiles?: number;
  disabled?: boolean;
  accept?: string;
  dragAndDropText?: string;
  chooseFileLabel?: string;
  uploadText?: string;
  deleteText?: string;
} & React.HTMLAttributes<HTMLElement>;

export function FileUploadField({
  label,
  labelClassName,
  labelAction,
  disabled,
  maxFiles = 30,
  accept = 'image/*',
  required = true,
  dragAndDropText = 'Drag and drop or',
  chooseFileLabel = 'Choose files',
  uploadText = 'or upload',
  deleteText = 'Delete',
}: fileUploadFieldProps) {
  const field = useFieldContext<File[]>();

  const onFileReject = React.useCallback((file: File, message: string) => {
    toast.error(message, {
      description: `"${file.name.length > 20 ? `${file.name.slice(0, 20)}...` : file.name}" has been rejected`,
    });
  }, []);

  return (
    <div className='space-y-2'>
      <div className='grid gap-2'>
        {labelAction ? (
          <div className='flex items-center justify-between'>
            <Label htmlFor={field.name} className={cn(labelClassName)}>
              {label}
              {required && <span className='ms-0.5 text-destructive'>*</span>}
            </Label>
            {labelAction}
          </div>
        ) : (
          <Label htmlFor={field.name} className={cn(labelClassName)}>
            {label}
            {required && <span className='ms-0.5 text-destructive'>*</span>}
          </Label>
        )}
      </div>
      <FileUpload
        value={field.state.value}
        onValueChange={field.handleChange}
        accept={accept}
        maxFiles={maxFiles}
        maxSize={5 * 1024 * 1024}
        onFileReject={onFileReject}
        multiple={maxFiles > 1}
        disabled={disabled}
      >
        <FileUploadDropzone className='flex-row border-dotted'>
          <CloudUpload className='size-4' />
          {dragAndDropText}
          <FileUploadTrigger asChild>
            <Button
              variant='link'
              size='sm'
              className='p-0'
              disabled={disabled}
            >
              {chooseFileLabel}
            </Button>
          </FileUploadTrigger>
          {uploadText}
        </FileUploadDropzone>
        <FileUploadList>
          {field.state.value.map((file, index) => (
            <FileUploadItem key={index} value={file}>
              <FileUploadItemPreview />
              <FileUploadItemMetadata />
              <FileUploadItemDelete asChild>
                <Button
                  variant='ghost'
                  size='icon'
                  className='size-7'
                  onClick={() => field.removeValue(index)}
                >
                  <X />
                  <span className='sr-only'>{deleteText}</span>
                </Button>
              </FileUploadItemDelete>
            </FileUploadItem>
          ))}
        </FileUploadList>
      </FileUpload>
      <FieldErrors meta={field.state.meta} />
    </div>
  );
}
