'use client';

import React from 'react';

import { cn } from '@/lib/utils';

import { Tabs, Ta<PERSON><PERSON>ist, TabsTrigger, TabsContent } from '@/components/ui/tabs';

import { useFieldContext } from '.';
import { FieldErrors } from './field-errors';

type TabOption = {
  value: string | string[];
  label: string;
  icon?: React.ReactNode;
  content?: React.ReactNode;
  disabled?: boolean;
  fieldValue?: string;
};

type TabsFieldProps = {
  options: TabOption[];
  defaultValue?: string;
  className?: string;
  tabsListClassName?: string;
  tabsTriggerClassName?: string;
  tabsContentClassName?: string;
  required?: boolean;
} & React.HTMLAttributes<HTMLDivElement>;

export const TabsField = ({
  options,
  defaultValue,
  className,
  tabsListClassName,
  tabsTriggerClassName,
  tabsContentClassName,
  ...divProps
}: TabsFieldProps) => {
  const field = useFieldContext<string>();

  const getActiveTabValue = () => {
    const fieldValue =
      field.state.value ||
      defaultValue ||
      options[0]?.fieldValue ||
      (Array.isArray(options[0]?.value)
        ? options[0]?.value[0]
        : options[0]?.value);

    for (const option of options) {
      const optionValues = Array.isArray(option.value)
        ? option.value
        : [option.value];
      if (optionValues.includes(fieldValue)) {
        return Array.isArray(option.value) ? option.value[0] : option.value;
      }
    }

    return Array.isArray(options[0]?.value)
      ? options[0]?.value[0]
      : options[0]?.value;
  };

  const currentTabValue = getActiveTabValue();

  const handleTabChange = (tabValue: string) => {
    const selectedOption = options.find((option) => {
      const optionValues = Array.isArray(option.value)
        ? option.value
        : [option.value];
      return optionValues.includes(tabValue);
    });

    if (selectedOption) {
      const valueToSet =
        selectedOption.fieldValue ||
        (Array.isArray(selectedOption.value)
          ? selectedOption.value[0]
          : selectedOption.value);
      field.handleChange(valueToSet);
    }
  };

  return (
    <div className={cn('space-y-2', className)} {...divProps}>
      <Tabs
        value={currentTabValue}
        onValueChange={handleTabChange}
        className='w-full space-y-4'
      >
        <TabsList
          className={cn(
            'grid w-full',
            `grid-cols-${options.length}`,
            tabsListClassName
          )}
        >
          {options.map((option) => {
            const tabValue = Array.isArray(option.value)
              ? option.value[0]
              : option.value;

            return (
              <TabsTrigger
                key={tabValue}
                value={tabValue}
                disabled={option.disabled}
                className={cn(
                  'flex items-center gap-2 data-[state=active]:bg-primary data-[state=active]:text-background',
                  tabsTriggerClassName
                )}
                onClick={() => {
                  handleTabChange(tabValue);
                  field.handleBlur();
                }}
              >
                {option.icon && option.icon}
                <span>{option.label}</span>
              </TabsTrigger>
            );
          })}
        </TabsList>

        {options.map((option) => {
          const tabValue = Array.isArray(option.value)
            ? option.value[0]
            : option.value;

          return (
            <TabsContent
              key={tabValue}
              value={tabValue}
              className={cn('m-0', tabsContentClassName)}
            >
              {option.content}
            </TabsContent>
          );
        })}
      </Tabs>
      <FieldErrors meta={field.state.meta} />
    </div>
  );
};
