'use client';

import Image from 'next/image';
import React, { JSX, useRef, useState, DragEvent, ChangeEvent } from 'react';

import { cn } from '@/lib/utils';

import { Icons } from '@/components/shared/icons';

interface FileUploadProps {
  onFileChange?: (file: File | null) => void;
  className?: string;
  addImageText?: string;
  dropFilesText?: string;
  uploadFileText?: string;
}

export function ImageUpload({
  onFileChange,
  className,
  addImageText = 'Add Image',
  dropFilesText = 'Drop files here or click to upload',
  uploadFileText = 'Upload File',
}: FileUploadProps): JSX.Element {
  const [, setFile] = useState<File | null>(null);
  const [preview, setPreview] = useState<string | null>(null);
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const fileInputRef = useRef<HTMLInputElement | null>(null);

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>): void => {
    const selectedFile = e.target.files?.[0] ?? null;

    setFile(selectedFile);
    setPreview(selectedFile ? URL.createObjectURL(selectedFile) : null);
    onFileChange?.(selectedFile);
  };

  const handleDragOver = (e: DragEvent<HTMLDivElement>): void => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: DragEvent<HTMLDivElement>): void => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e: DragEvent<HTMLDivElement>): void => {
    e.preventDefault();
    setIsDragging(false);

    const droppedFile = e.dataTransfer.files?.[0];
    if (droppedFile) {
      setFile(droppedFile);
      setPreview(URL.createObjectURL(droppedFile));
      onFileChange?.(droppedFile);
    }
  };

  const handleClick = (): void => {
    fileInputRef.current?.click();
  };

  return (
    <div className={cn('w-full', className)}>
      <h3 className='mb-2 text-sm font-medium'>{uploadFileText}</h3>
      <div
        className={cn(
          'cursor-pointer rounded-md border border-dashed bg-blue-50/50 p-6',
          isDragging && 'border-blue-500 bg-blue-100/50',
          'transition-colors duration-200'
        )}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={handleClick}
      >
        <input
          type='file'
          ref={fileInputRef}
          onChange={(event) => handleFileChange(event)}
          className='hidden'
          accept='image/*'
        />

        {preview ? (
          <div className='relative h-40 w-full'>
            <Image
              src={preview}
              alt='Uploaded image'
              fill
              className='object-contain'
            />
          </div>
        ) : (
          <div className='flex items-center'>
            <div className='mr-4 flex h-10 w-10 items-center justify-center text-blue-500'>
              <Icons.uploadFile />
            </div>
            <div>
              <p className='text-sm font-medium text-blue-600'>
                {addImageText}
              </p>
              <p className='text-xs text-muted-foreground'>{dropFilesText}</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
