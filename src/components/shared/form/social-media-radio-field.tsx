import React from 'react';

import { cn } from '@/lib/utils';

import { FieldErrors } from '@/components/shared/form/field-errors';
import { useFieldContext } from '@/components/shared/form/index';
import { Icons } from '@/components/shared/icons';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';

interface SocialMediaRadioFieldProps {
  label?: string;
  required?: boolean;
  multiSelect?: boolean;
}

type SocialMediaPlatform = 'facebook' | 'instagram' | 'x' | 'linkedin';

interface SocialMediaOption {
  id: SocialMediaPlatform;
  icon: React.ComponentType<{ className?: string }>;
  iconColor: string;
  backgroundColor: string;
}

const socialMediaOptions: SocialMediaOption[] = [
  {
    id: 'facebook',
    icon: Icons.facebook,
    iconColor: 'text-primary',
    backgroundColor: 'bg-primary/20',
  },
  {
    id: 'instagram',
    icon: Icons.instagram,
    iconColor: 'text-rose',
    backgroundColor: 'bg-rose/20',
  },
  {
    id: 'x',
    icon: Icons.x,
    iconColor: 'text-slate-900',
    backgroundColor: 'bg-slate-900/20',
  },
  {
    id: 'linkedin',
    icon: Icons.linkedin,
    iconColor: 'text-sky-500',
    backgroundColor: 'bg-sky-500/20',
  },
];

export function SocialMediaRadioField({
  label,
  required = true,
  multiSelect = false,
}: SocialMediaRadioFieldProps) {
  const field = useFieldContext<string | string[]>();

  const handleValueChange = (value: string) => {
    if (multiSelect) {
      const currentValues = Array.isArray(field.state.value)
        ? field.state.value
        : [];
      const newValues = currentValues.includes(value)
        ? currentValues.filter((v) => v !== value)
        : [...currentValues, value];
      field.handleChange(newValues);
    } else {
      field.handleChange(value);
    }
  };

  const isSelected = (optionId: string): boolean => {
    if (multiSelect) {
      return (
        Array.isArray(field.state.value) && field.state.value.includes(optionId)
      );
    }
    return field.state.value === optionId;
  };

  const getCurrentValue = () => {
    if (multiSelect) {
      return Array.isArray(field.state.value) ? field.state.value[0] || '' : '';
    }
    return typeof field.state.value === 'string' ? field.state.value : '';
  };

  return (
    <div className='space-y-2'>
      {label && (
        <Label htmlFor={field.name}>
          {label}
          {required && <span className='ms-0.5 text-destructive'>*</span>}
        </Label>
      )}
      <div className='grid grid-cols-2 gap-3 sm:grid-cols-4'>
        {multiSelect ? (
          socialMediaOptions.map((option) => {
            const Icon = option.icon;
            const selected = isSelected(option.id);

            return (
              <div
                key={option.id}
                className={cn(
                  'relative flex w-full cursor-pointer flex-col items-center gap-3 rounded-md border border-input px-0 py-1.5 text-center shadow-sm outline-none',
                  {
                    [option.backgroundColor]: selected,
                  }
                )}
                onClick={() => handleValueChange(option.id)}
              >
                <label
                  htmlFor={option.id}
                  className='cursor-pointer after:absolute after:inset-0'
                >
                  <Icon className={cn('size-5', option.iconColor)} />
                </label>
              </div>
            );
          })
        ) : (
          <RadioGroup
            value={getCurrentValue()}
            onValueChange={handleValueChange}
            className='contents'
          >
            {socialMediaOptions.map((option) => {
              const Icon = option.icon;
              const selected = isSelected(option.id);

              return (
                <div
                  key={option.id}
                  className={cn(
                    'relative flex w-full cursor-pointer flex-col items-center gap-3 rounded-md border border-input px-0 py-1.5 text-center shadow-sm outline-none',
                    {
                      [option.backgroundColor]: selected,
                    }
                  )}
                >
                  <RadioGroupItem
                    id={option.id}
                    value={option.id}
                    className='sr-only'
                  />
                  <label
                    htmlFor={option.id}
                    className='cursor-pointer after:absolute after:inset-0'
                  >
                    <Icon className={cn('size-5', option.iconColor)} />
                  </label>
                </div>
              );
            })}
          </RadioGroup>
        )}
      </div>
      <FieldErrors meta={field.state.meta} />
    </div>
  );
}
