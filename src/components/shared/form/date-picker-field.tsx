import * as React from 'react';
import { DayPickerSingleProps } from 'react-day-picker';

import { CalendarIcon } from 'lucide-react';

import { cn } from '@/lib/utils';

import { useFormatDate } from '@/hooks/use-date-fns-locale';

import { FieldErrors } from '@/components/shared/form/field-errors';
import { useFieldContext } from '@/components/shared/form/index';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Label } from '@/components/ui/label';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';

interface DatePickerFieldProps {
  label: string;
  required?: boolean;
  datePickerProps?: Omit<DayPickerSingleProps, 'mode'>;
}

export function DatePickerField({
  label,
  required = true,
  datePickerProps,
}: DatePickerFieldProps) {
  const field = useFieldContext<Date | undefined>();

  const { format } = useFormatDate();

  return (
    <div className='space-y-2'>
      <div className='grid gap-2'>
        <Label htmlFor={field.name}>
          {label}
          {required && <span className='ms-0.5 text-destructive'>*</span>}
        </Label>
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant={'outline'}
              className={cn(
                'group w-full justify-between border-input bg-background px-3 font-normal outline-none outline-offset-0 hover:bg-background focus-visible:outline-[3px]',
                !field.state.value && 'text-muted-foreground'
              )}
            >
              <span
                className={cn(
                  'truncate',
                  !field.state.value && 'text-muted-foreground'
                )}
              >
                {field.state.value
                  ? format(field.state.value, 'PPP')
                  : 'Pick a date'}
              </span>
              <CalendarIcon
                size={16}
                className='shrink-0 text-muted-foreground/80 transition-colors group-hover:text-foreground'
                aria-hidden='true'
              />
            </Button>
          </PopoverTrigger>
          <PopoverContent className='w-auto p-2' align='start'>
            <Calendar
              mode='single'
              selected={field.state.value}
              onSelect={field.handleChange}
              {...datePickerProps}
            />
          </PopoverContent>
        </Popover>
      </div>
      <FieldErrors meta={field.state.meta} />
    </div>
  );
}
