'use client';

import React from 'react';

import { cn } from '@/lib/utils';

import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';

import { useFieldContext } from '.';
import { FieldErrors } from './field-errors';

type RadioOption = {
  value: string;
  label: string;
  description?: string;
  disabled?: boolean;
  icon?: React.ReactNode;
};

type RadioGroupFieldProps = {
  options: RadioOption[];
  label?: string;
  labelClassName?: string;
  className?: string;
  radioGroupClassName?: string;
  radioItemClassName?: string;
  orientation?: 'horizontal' | 'vertical';
  itemOptionOrientation?: 'horizontal' | 'vertical';
  required?: boolean;
  indicatorIconClassName?: string;
  labelItemClassName?: string;
  onItemSelect?: (value: string) => void;
  showIndicatorIcon?: boolean;
} & React.HTMLAttributes<HTMLDivElement>;

export const RadioGroupField = ({
  options,
  label,
  labelClassName,
  className,
  radioGroupClassName,
  radioItemClassName,
  orientation = 'horizontal',
  itemOptionOrientation = 'horizontal',
  required = true,
  indicatorIconClassName,
  labelItemClassName,
  onItemSelect,
  showIndicatorIcon = true,
  ...divProps
}: RadioGroupFieldProps) => {
  const field = useFieldContext<string>();

  return (
    <div className={cn('space-y-2', className)} {...divProps}>
      {label && (
        <Label htmlFor={field.name} className={cn(labelClassName)}>
          {label}
          {required && <span className='ms-0.5 text-destructive'>*</span>}
        </Label>
      )}

      <RadioGroup
        value={field.state.value}
        onValueChange={(value) => {
          field.handleChange(value);
          field.handleBlur();
          if (onItemSelect) {
            onItemSelect(value);
          }
        }}
        className={cn(
          orientation === 'horizontal'
            ? 'grid grid-cols-2 gap-2'
            : 'flex flex-col gap-2',
          radioGroupClassName
        )}
      >
        {options.map((option) => (
          <div
            key={option.value}
            className={cn(
              'flex items-center justify-start gap-2 rounded-lg border px-1 py-1',
              field.state.value === option.value && 'border-primary',
              radioItemClassName
            )}
          >
            <RadioGroupItem
              value={option.value}
              id={`${field.name}-${option.value}`}
              className='size-4'
              disabled={option.disabled}
              indicatorIconClassName={indicatorIconClassName}
              hidden={showIndicatorIcon ? false : true}
            />
            <div className='flex flex-1 flex-col items-start py-1'>
              <div
                className={cn('flex items-center gap-2', {
                  'flex-col': itemOptionOrientation === 'vertical',
                })}
              >
                {option.icon && option.icon}
                <Label
                  htmlFor={`${field.name}-${option.value}`}
                  className={cn(labelItemClassName)}
                >
                  {option.label}
                </Label>
              </div>
              {option.description && (
                <span className='text-xs text-muted-foreground'>
                  {option.description}
                </span>
              )}
            </div>
          </div>
        ))}
      </RadioGroup>
      <FieldErrors meta={field.state.meta} />
    </div>
  );
};
