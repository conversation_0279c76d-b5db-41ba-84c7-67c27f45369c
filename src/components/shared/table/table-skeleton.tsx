'use client';

import { Skeleton } from '@/components/ui/skeleton';

export default function TableSkeleton() {
  return (
    <div className='flex h-full w-full flex-col space-y-2 overflow-hidden rounded-lg border bg-background p-4'>
      {/* Header Skeleton */}
      <div className='flex w-full items-center justify-between'>
        <div className='flex w-full items-center gap-2'>
          <Skeleton className='h-10 w-1/3' />
          <Skeleton className='h-10 w-1/6' />
          <Skeleton className='h-10 w-1/6' />
        </div>
        <div className='flex w-full items-center justify-end gap-2'>
          <Skeleton className='h-10 w-1/3' />
          <Skeleton className='size-10' />
          <Skeleton className='size-10' />
        </div>
      </div>

      <div className='overflow-hidden rounded-md'>
        <table className='w-full border-collapse'>
          {/* Table Head */}
          <thead>
            <tr className='border-b border-border'>
              {Array.from({ length: 8 }).map((_, index) => (
                <th key={index} className='p-4'>
                  <Skeleton className='h-6 w-24' />
                </th>
              ))}
            </tr>
          </thead>

          {/* Table Body */}
          <tbody>
            {Array.from({ length: 10 }).map((_, index) => (
              <tr key={index} className='border-b border-border'>
                {Array.from({ length: 8 }).map((_, index) => (
                  <td key={index} className='p-4'>
                    <Skeleton className='h-6 w-24' />
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination Skeleton */}
      <div className='flex items-center justify-between'>
        <div className='flex items-center gap-2'>
          {Array.from({ length: 7 }).map((_, index) => (
            <Skeleton key={index} className='size-8 rounded-md' />
          ))}
        </div>

        <div className='flex items-center gap-2'>
          <Skeleton className='h-8 w-32' />
          <Skeleton className='h-8 w-32' />
        </div>
      </div>
    </div>
  );
}
