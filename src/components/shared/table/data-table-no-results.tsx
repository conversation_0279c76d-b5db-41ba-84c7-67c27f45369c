'use client';

import { TableRow, TableCell } from '@/components/ui/table';

import { ViewMode } from './data-table-view-mode-options';

export interface DataTableNoResultsProps {
  viewMode: ViewMode;
  columnsLength: number;
  message: string;
}

export function DataTableNoResults({
  viewMode,
  columnsLength,
  message,
}: DataTableNoResultsProps) {
  switch (viewMode) {
    case 'grid':
      return (
        <div className='col-span-full flex h-24 items-center justify-center text-center text-muted-foreground'>
          {message}
        </div>
      );

    case 'list':
      return (
        <TableRow>
          <TableCell colSpan={columnsLength} className='h-24 text-center'>
            {message}
          </TableCell>
        </TableRow>
      );
    default:
      return null;
  }
}
