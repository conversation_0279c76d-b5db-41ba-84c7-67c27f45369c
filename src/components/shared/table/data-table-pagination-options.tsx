'use client';

import { useTranslations } from 'next-intl';

import { ChevronLeft, ChevronRight } from 'lucide-react';

import { cn } from '@/lib/utils';

import { usePagination } from '@/hooks/use-pagination';

import { Table } from '@tanstack/react-table';

import { Button, buttonVariants } from '@/components/ui/button';
import {
  Pagination,
  PaginationItem,
  PaginationLink,
  PaginationContent,
  PaginationEllipsis,
} from '@/components/ui/pagination';

export interface DataTablePaginationOptionsProps<TData> {
  table: Table<TData>;
}

export function DataTablePaginationOptions<TData>({
  table,
}: DataTablePaginationOptionsProps<TData>) {
  const t = useTranslations();

  const { pages, showLeftEllipsis, showRightEllipsis } = usePagination({
    currentPage: table.getState().pagination.pageIndex + 1,
    totalPages: table.getPageCount(),
    paginationItemsToDisplay: 5,
  });

  const currentPageIndex = table.getState().pagination.pageIndex;

  return (
    <Pagination className='mx-0'>
      <PaginationContent className='shadow-xs flex w-full flex-wrap justify-between gap-0 -space-x-px rounded-md rtl:space-x-reverse'>
        <div className='flex items-center'>
          <PaginationItem className='[&:first-child>a]:rounded-s-md [&:last-child>a]:rounded-e-md'>
            <PaginationLink
              className={cn(
                buttonVariants({
                  variant: 'outline',
                }),
                'rounded-none shadow-none focus-visible:z-10 aria-disabled:pointer-events-none [&[aria-disabled]>svg]:opacity-50'
              )}
              href='#'
              onClick={(e) => {
                e.preventDefault();
                table.previousPage();
              }}
              aria-disabled={!table.getCanPreviousPage()}
              aria-label='Go to previous page'
            >
              <ChevronLeft
                className='h-4 w-4 rtl:rotate-180'
                aria-hidden='true'
              />
            </PaginationLink>
          </PaginationItem>

          {showLeftEllipsis && (
            <PaginationItem className='[&:first-child>a]:rounded-s-md [&:last-child>a]:rounded-e-md'>
              <PaginationEllipsis />
            </PaginationItem>
          )}

          {pages.map((page) => (
            <PaginationItem key={page}>
              <PaginationLink
                className={cn(
                  buttonVariants({
                    variant: 'outline',
                  }),
                  'rounded-none shadow-none focus-visible:z-10',
                  page === currentPageIndex + 1 && 'bg-primary text-white'
                )}
                href='#'
                onClick={(e) => {
                  e.preventDefault();
                  table.setPageIndex(page - 1);
                }}
                isActive={page === currentPageIndex + 1}
              >
                {page}
              </PaginationLink>
            </PaginationItem>
          ))}

          {showRightEllipsis && (
            <PaginationItem className='[&:first-child>a]:rounded-s-md [&:last-child>a]:rounded-e-md'>
              <PaginationEllipsis
                className={cn(
                  buttonVariants({
                    variant: 'outline',
                  }),
                  'pointer-events-none rounded-none shadow-none'
                )}
              />
            </PaginationItem>
          )}

          <PaginationItem className='[&:first-child>a]:rounded-s-md [&:last-child>a]:rounded-e-md'>
            <PaginationLink
              className={cn(
                buttonVariants({
                  variant: 'outline',
                }),
                'rounded-none shadow-none focus-visible:z-10 aria-disabled:pointer-events-none [&[aria-disabled]>svg]:opacity-50'
              )}
              href='#'
              onClick={(e) => {
                e.preventDefault();
                table.nextPage();
              }}
              aria-disabled={!table.getCanNextPage()}
              aria-label='Go to next page'
            >
              <ChevronRight
                className='h-4 w-4 rtl:rotate-180'
                aria-hidden='true'
              />
            </PaginationLink>
          </PaginationItem>
        </div>

        <div className='flex items-center gap-2'>
          <PaginationItem>
            <Button
              variant='outline'
              onClick={() => table.previousPage()}
              disabled={!table.getCanPreviousPage()}
            >
              {t('previous')}
            </Button>
          </PaginationItem>
          <PaginationItem>
            <Button
              variant='outline'
              onClick={() => table.nextPage()}
              disabled={!table.getCanNextPage()}
            >
              {t('next')}
            </Button>
          </PaginationItem>
        </div>
      </PaginationContent>
    </Pagination>
  );
}
