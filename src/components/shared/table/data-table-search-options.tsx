'use client';

import { Search } from 'lucide-react';

import { Input } from '@/components/ui/input';

export interface SearchConfig {
  columns: string[];
  placeholder?: string;
}

export interface DataTableSearchOptionsProps {
  searchConfig?: SearchConfig;
  searchTerm: string;
  onSearchTermChange: (searchTerm: string) => void;
}

export function DataTableSearchOptions({
  searchConfig,
  searchTerm,
  onSearchTermChange,
}: DataTableSearchOptionsProps) {
  if (!searchConfig) return null;

  return (
    <div className='relative w-[300px]'>
      <Input
        placeholder={searchConfig.placeholder || 'Search'}
        className='border-none bg-[#F4F4F5] focus-visible:ring-0 focus-visible:ring-offset-0'
        value={searchTerm}
        onChange={(e) => onSearchTermChange(e.target.value)}
      />
      <Search
        className='absolute end-3 top-1/2 -translate-y-1/2 text-gray-400'
        size={18}
      />
    </div>
  );
}
