'use client';

import { cn } from '@/lib/utils';

import { Icons } from '@/components/shared/icons';

export type ViewMode = 'list' | 'grid';

export interface DataTableViewModeOptionsProps {
  viewMode: ViewMode;
  onViewModeChange: (mode: ViewMode) => void;
}

export function DataTableViewModeOptions({
  viewMode,
  onViewModeChange,
}: DataTableViewModeOptionsProps) {
  return (
    <>
      <button
        className={cn(
          'rounded-md border-none px-1.5 py-1',
          viewMode === 'list'
            ? 'bg-[#D7E4F8] text-primary'
            : 'bg-[#F4F4F5] text-[#71717A] hover:bg-[#D7E4F8] hover:text-primary'
        )}
        onClick={() => onViewModeChange('list')}
        aria-label='Switch to list view'
      >
        <Icons.list className='h-7 w-6' />
      </button>
      <button
        className={cn(
          'rounded-md border-none px-2.5 py-2',
          viewMode === 'grid'
            ? 'bg-[#D7E4F8] text-primary'
            : 'bg-[#F4F4F5] text-[#71717A] hover:bg-[#D7E4F8] hover:text-primary'
        )}
        onClick={() => onViewModeChange('grid')}
        aria-label='Switch to grid view'
      >
        <Icons.listGrid className='h-5 w-4' />
      </button>
    </>
  );
}
