'use client';

import { format } from 'date-fns';
import { useTranslations } from 'next-intl';
import { useState } from 'react';

import { cn } from '@/lib/utils';

import { Table, ColumnFiltersState } from '@tanstack/react-table';

import { Icons } from '@/components/shared/icons';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Select,
  SelectItem,
  SelectValue,
  SelectContent,
  SelectTrigger,
} from '@/components/ui/select';

export interface FilterOption {
  label: string;
  value: string;
}

export interface FilterConfig {
  column: string;
  type: 'select' | 'date' | 'text';
  label: string;
  options?: FilterOption[];
}

export interface DataTableFilterOptionsProps<TData> {
  table: Table<TData>;
  filterConfig?: FilterConfig[];
  columnFilters: ColumnFiltersState;
}

export function DataTableFilterOptions<TData>({
  table,
  filterConfig,
  columnFilters,
}: DataTableFilterOptionsProps<TData>) {
  const t = useTranslations();
  const [selectedDate, setSelectedDate] = useState<Date | undefined>();

  const clearAllFilters = () => {
    table.resetColumnFilters();
    setSelectedDate(undefined);
  };

  if (!filterConfig || filterConfig.length === 0) return null;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant='ghost' className='bg-[#F4F4F5]'>
          <Icons.filter className='me-1 text-xs' />
          {t('filter')}
          {columnFilters.length > 0 && (
            <span className='ms-2 rounded-full bg-blue-100 px-2 py-1 text-xs text-blue-800'>
              {columnFilters.length}
            </span>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className='w-64 p-4'>
        <div className='space-y-4'>
          {filterConfig.map((filter) => (
            <div key={filter.column}>
              <label className='mb-2 block text-sm font-medium capitalize'>
                {filter.label}
              </label>
              {filter.type === 'select' && filter.options && (
                <Select
                  value={
                    (table
                      .getColumn(filter.column)
                      ?.getFilterValue() as string) ?? ''
                  }
                  onValueChange={(value) =>
                    table.getColumn(filter.column)?.setFilterValue(value)
                  }
                >
                  <SelectTrigger className='w-full'>
                    <SelectValue placeholder={filter.label.toLowerCase()} />
                  </SelectTrigger>
                  <SelectContent>
                    {filter.options.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
              {filter.type === 'date' && (
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant='ghost'
                      className={cn(
                        'w-full justify-between bg-[#F4F4F5] text-[#71717A]',
                        selectedDate && 'border-blue-200 bg-blue-50'
                      )}
                    >
                      <span className='text-xs text-gray-500'>
                        {selectedDate ? (
                          format(selectedDate, 'PPP')
                        ) : (
                          <span>Pick a date</span>
                        )}
                      </span>
                      <Icons.calendarInput className='h-4 w-4' />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className='w-auto p-0' align='end'>
                    <Calendar
                      mode='single'
                      selected={selectedDate}
                      onSelect={(date) => {
                        setSelectedDate(date);
                        table.getColumn(filter.column)?.setFilterValue(date);
                      }}
                      initialFocus
                    />
                    {selectedDate && (
                      <div className='border-t p-2'>
                        <Button
                          variant='ghost'
                          size='sm'
                          className='w-full'
                          onClick={() => {
                            setSelectedDate(undefined);
                            table
                              .getColumn(filter.column)
                              ?.setFilterValue(undefined);
                          }}
                        >
                          Clear
                        </Button>
                      </div>
                    )}
                  </PopoverContent>
                </Popover>
              )}
              {filter.type === 'text' && (
                <Input
                  placeholder={`Filter by ${filter.label.toLowerCase()}`}
                  value={
                    (table
                      .getColumn(filter.column)
                      ?.getFilterValue() as string) ?? ''
                  }
                  onChange={(e) =>
                    table
                      .getColumn(filter.column)
                      ?.setFilterValue(e.target.value)
                  }
                  className='w-full'
                />
              )}
            </div>
          ))}

          <Button
            variant='outline'
            size='sm'
            className='w-full'
            onClick={clearAllFilters}
          >
            {t('clear_all')}
          </Button>
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
