'use client';

import { useTranslations } from 'next-intl';
import { useState, useEffect } from 'react';
import { DateRange } from 'react-day-picker';

import {
  ColumnDef,
  flexRender,
  SortingState,
  useReactTable,
  getCoreRowModel,
  VisibilityState,
  PaginationState,
  getSortedRowModel,
  ColumnFiltersState,
  getFilteredRowModel,
  getPaginationRowModel,
} from '@tanstack/react-table';

import { ErrorCard } from '@/components/shared/error-card';
import { DataTableDateRangeSelectOptions } from '@/components/shared/table/data-table-date-range-filter';
import {
  FilterConfig,
  DataTableFilterOptions,
} from '@/components/shared/table/data-table-filter-options';
import { DataTableNoResults } from '@/components/shared/table/data-table-no-results';
import { DataTablePaginationOptions } from '@/components/shared/table/data-table-pagination-options';
import {
  SearchConfig,
  DataTableSearchOptions,
} from '@/components/shared/table/data-table-search-options';
import { DataTableSortingOptions } from '@/components/shared/table/data-table-sorting-options';
import {
  ViewMode,
  DataTableViewModeOptions,
} from '@/components/shared/table/data-table-view-mode-options';
import { GridViewSkeleton } from '@/components/shared/table/grid-view-skeleton';
import TableSkeleton from '@/components/shared/table/table-skeleton';
import {
  Card,
  CardHeader,
  CardFooter,
  CardContent,
} from '@/components/ui/card';
import {
  Table,
  TableRow,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
} from '@/components/ui/table';

export interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  isLoading: boolean;
  isError: boolean;
  error: Error | null;
  refetch: () => void;
  initialColumnVisibility?: VisibilityState;
  searchConfig?: SearchConfig;
  filterConfig?: FilterConfig[];
  gridView: (data: TData) => React.ReactNode;
  initialViewMode?: ViewMode;
  gridViewPageSize?: number;
  listViewPageSize?: number;
  dateColumn?: string;
  noResultsMessage?: (t: ReturnType<typeof useTranslations>) => string;
  dateRangePlaceholder?: (t: ReturnType<typeof useTranslations>) => string;
}

export function DataTable<TData, TValue>({
  columns,
  data,
  isLoading,
  isError,
  error,
  refetch,
  initialColumnVisibility = {},
  searchConfig,
  filterConfig,
  gridView,
  initialViewMode = 'grid',
  gridViewPageSize = 10,
  listViewPageSize = 10,
  dateColumn = 'CreationTimestamp',
  noResultsMessage = (t) => t('no_results_found'),
  dateRangePlaceholder = (t) => t('date_range_placeholder'),
}: DataTableProps<TData, TValue>) {
  const t = useTranslations();
  const [viewMode, setViewMode] = useState<ViewMode>(initialViewMode);
  const [searchTerm, setSearchTerm] = useState('');
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>(
    initialColumnVisibility
  );
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: viewMode === 'grid' ? gridViewPageSize : listViewPageSize,
  });

  useEffect(() => {
    setPagination((prev) => ({
      ...prev,
      pageIndex: 0,
      pageSize: viewMode === 'grid' ? gridViewPageSize : listViewPageSize,
    }));
  }, [viewMode, gridViewPageSize, listViewPageSize]);

  const [dateRange, setDateRange] = useState<DateRange | undefined>();

  const table = useReactTable({
    data,
    columns,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      pagination,
    },
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onPaginationChange: setPagination,
  });

  useEffect(() => {
    if (searchConfig?.columns && searchTerm) {
      const filters: ColumnFiltersState = searchConfig.columns.map(
        (column) => ({
          id: column,
          value: searchTerm,
        })
      );
      setColumnFilters(filters);
    } else {
      setColumnFilters([]);
    }
  }, [searchTerm, searchConfig]);

  if (isLoading) {
    switch (viewMode) {
      case 'grid':
        return <GridViewSkeleton />;
      case 'list':
        return <TableSkeleton />;
      default:
        return <div>{t('loading')}</div>;
    }
  }

  if (isError && error) {
    return <ErrorCard handleRetry={refetch} />;
  }

  return (
    <Card className='flex h-full flex-col overflow-hidden'>
      <CardHeader className='flex-row flex-wrap items-center justify-between space-y-0 px-6 py-4'>
        <div className='flex flex-wrap items-center gap-2'>
          <DataTableSearchOptions
            searchConfig={searchConfig}
            searchTerm={searchTerm}
            onSearchTermChange={setSearchTerm}
          />

          <DataTableFilterOptions
            table={table}
            filterConfig={filterConfig}
            columnFilters={columnFilters}
          />

          <DataTableSortingOptions
            sorting={sorting}
            onSortingChange={setSorting}
            columns={table.getAllColumns()}
          />
        </div>

        <div className='flex flex-wrap items-center gap-2'>
          <DataTableDateRangeSelectOptions
            table={table}
            dateColumn={dateColumn}
            dateRange={dateRange}
            onDateRangeChange={setDateRange}
            dateRangePlaceholder={dateRangePlaceholder(t)}
          />

          <DataTableViewModeOptions
            viewMode={viewMode}
            onViewModeChange={setViewMode}
          />
        </div>
      </CardHeader>

      <CardContent className='flex-1 overflow-auto py-0'>
        <div className='flex flex-1 flex-col'>
          {viewMode === 'grid' ? (
            <div className='grid grid-cols-1 gap-2 md:grid-cols-2 lg:grid-cols-4'>
              {table.getRowModel().rows.length ? (
                table
                  .getRowModel()
                  .rows.map((row) => (
                    <div key={row.id}>{gridView(row.original)}</div>
                  ))
              ) : (
                <DataTableNoResults
                  viewMode={viewMode}
                  columnsLength={columns.length}
                  message={noResultsMessage(t)}
                />
              )}
            </div>
          ) : (
            <Table>
              <TableHeader>
                {table.getHeaderGroups().map((headerGroup) => (
                  <TableRow key={headerGroup.id} className='border-b-0'>
                    {headerGroup.headers.map((header) => {
                      return (
                        <TableHead key={header.id}>
                          {flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                        </TableHead>
                      );
                    })}
                  </TableRow>
                ))}
              </TableHeader>

              <TableBody>
                {table.getRowModel().rows.length ? (
                  table.getRowModel().rows.map((row) => (
                    <TableRow
                      key={row.id}
                      data-state={row.getIsSelected() && 'selected'}
                    >
                      {row.getVisibleCells().map((cell) => (
                        <TableCell key={cell.id}>
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext()
                          )}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : (
                  <DataTableNoResults
                    viewMode={viewMode}
                    columnsLength={columns.length}
                    message={noResultsMessage(t)}
                  />
                )}
              </TableBody>
            </Table>
          )}
        </div>
      </CardContent>

      <CardFooter className='justify-between gap-2 py-3'>
        <DataTablePaginationOptions table={table} />
      </CardFooter>
    </Card>
  );
}
