'use client';

import { useTranslations } from 'next-intl';

import { Column, SortingState } from '@tanstack/react-table';

import { Icons } from '@/components/shared/icons';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuRadioItem,
  DropdownMenuRadioGroup,
} from '@/components/ui/dropdown-menu';

export interface DataTableSortingOptionsProps<TData> {
  sorting: SortingState;
  onSortingChange: (sorting: SortingState) => void;
  columns: Column<TData, unknown>[];
}

export function DataTableSortingOptions<TData>({
  sorting,
  onSortingChange,
  columns,
}: DataTableSortingOptionsProps<TData>) {
  const t = useTranslations();
  const sortableColumns = columns.filter((column) => column.getCanSort());

  const handleSortingChange = (value: string) => {
    const [id, direction] = value.split('-');
    onSortingChange([{ id, desc: direction === 'desc' }]);
  };

  const clearSorting = () => {
    onSortingChange([]);
  };

  const getCurrentSortValue = () => {
    if (sorting.length === 0) return '';
    return `${sorting[0].id}-${sorting[0].desc ? 'desc' : 'asc'}`;
  };

  const getSortingDisplayText = () => {
    if (sorting.length === 0) return null;
    return `${sorting[0].id} (${sorting[0].desc ? t('desc') : t('asc')})`;
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant='ghost'
          className='border-gray-200 bg-[#F4F4F5]'
          iconStart={<Icons.sort />}
        >
          {t('sort')}
          {sorting.length > 0 && (
            <span className='ms-2 rounded-full bg-blue-100 px-2 py-1 text-xs text-blue-800'>
              {getSortingDisplayText()}
            </span>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className='w-48'>
        <DropdownMenuRadioGroup
          value={getCurrentSortValue()}
          onValueChange={handleSortingChange}
        >
          {sortableColumns.map((column) => (
            <div key={column.id}>
              <DropdownMenuRadioItem
                value={`${column.id}-asc`}
                className='cursor-pointer'
              >
                {column.columnDef.header as string} ↑
              </DropdownMenuRadioItem>
              <DropdownMenuRadioItem
                value={`${column.id}-desc`}
                className='cursor-pointer'
              >
                {column.columnDef.header as string} ↓
              </DropdownMenuRadioItem>
            </div>
          ))}
        </DropdownMenuRadioGroup>

        {sorting.length > 0 && (
          <div className='border-t p-2'>
            <Button
              variant='ghost'
              size='sm'
              className='w-full'
              onClick={clearSorting}
            >
              {t('clear_sorting')}
            </Button>
          </div>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
