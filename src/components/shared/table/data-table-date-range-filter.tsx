'use client';

import { useTranslations } from 'next-intl';
import { DateRange } from 'react-day-picker';

import { cn } from '@/lib/utils';

import { useFormatDate } from '@/hooks/use-date-fns-locale';

import { Table } from '@tanstack/react-table';

import { Icons } from '@/components/shared/icons';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';

export interface DataTableDateRangeSelectOptionsProps<TData> {
  table: Table<TData>;
  dateRange: DateRange | undefined;
  onDateRangeChange: (range: DateRange | undefined) => void;
  dateColumn: string;
  dateRangePlaceholder: string;
}

export function DataTableDateRangeSelectOptions<TData>({
  table,
  dateRange,
  onDateRangeChange,
  dateColumn,
  dateRangePlaceholder,
}: DataTableDateRangeSelectOptionsProps<TData>) {
  const t = useTranslations();

  const { format } = useFormatDate();

  const handleDateRangeSelect = (range: DateRange | undefined) => {
    onDateRangeChange(range);
    table.getColumn(dateColumn)?.setFilterValue(range);
  };

  const clearDateRange = () => {
    onDateRangeChange(undefined);
    table.getColumn(dateColumn)?.setFilterValue(undefined);
  };

  const formatDateRange = () => {
    if (dateRange?.from && dateRange?.to) {
      return `${format(dateRange.from, 'yyyy-MM-dd')} / ${format(
        dateRange.to,
        'yyyy-MM-dd'
      )}`;
    }
    return dateRangePlaceholder;
  };

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant='ghost'
          className={cn(
            'w-[240px] justify-between bg-[#F4F4F5] text-[#71717A]',
            dateRange && 'border-blue-200 bg-blue-50'
          )}
          iconEnd={<Icons.calendarInput className='h-4 w-4' />}
        >
          <span className='text-xs text-gray-500'>{formatDateRange()}</span>
        </Button>
      </PopoverTrigger>
      <PopoverContent className='w-auto p-0' align='end'>
        <Calendar
          mode='range'
          disabled={(date) => date > new Date()}
          numberOfMonths={2}
          selected={dateRange}
          onSelect={handleDateRangeSelect}
        />
        {dateRange && (
          <div className='border-t p-2'>
            <Button
              variant='ghost'
              size='sm'
              className='w-full'
              onClick={clearDateRange}
            >
              {t('clear')}
            </Button>
          </div>
        )}
      </PopoverContent>
    </Popover>
  );
}
