'use client';

import { Skeleton } from '@/components/ui/skeleton';

export function GridViewSkeleton() {
  return (
    <div className='flex h-full w-full flex-col space-y-2 overflow-hidden rounded-lg border bg-background p-4'>
      <div className='flex w-full items-center justify-between'>
        <div className='flex w-full items-center gap-2'>
          <Skeleton className='h-10 w-1/3' />
          <Skeleton className='h-10 w-1/6' />
          <Skeleton className='h-10 w-1/6' />
        </div>
        <div className='flex w-full items-center justify-end gap-2'>
          <Skeleton className='h-10 w-1/3' />
          <Skeleton className='size-10' />
          <Skeleton className='size-10' />
        </div>
      </div>

      <div className='grid grid-cols-1 gap-4 overflow-hidden sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4'>
        {Array.from({ length: 8 }).map((_, index) => (
          <Skeleton key={index} className='h-64 w-full rounded-xl' />
        ))}
      </div>

      <div className='flex items-center justify-between'>
        <div className='flex items-center gap-2'>
          {Array.from({ length: 7 }).map((_, index) => (
            <Skeleton key={index} className='size-8 rounded-md' />
          ))}
        </div>

        <div className='flex items-center gap-2'>
          <Skeleton className='h-8 w-32' />
          <Skeleton className='h-8 w-32' />
        </div>
      </div>
    </div>
  );
}
