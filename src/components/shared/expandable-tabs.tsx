'use client';

import { motion, AnimatePresence } from 'framer-motion';
import * as React from 'react';

import { cn } from '@/lib/utils';

export type TabItem = {
  id: string;
  title: string;
  icon: React.ReactNode;
};

interface ExpandableTabsProps {
  tabs: TabItem[];
  className?: string;
  activeColor?: string;
  onChange?: (index: number | null) => void;
}

const buttonVariants = {
  initial: { gap: 0, paddingLeft: '.5rem', paddingRight: '.5rem' },
  animate: (isSelected: boolean) => ({
    gap: isSelected ? '.5rem' : 0,
    paddingLeft: isSelected ? '1rem' : '.5rem',
    paddingRight: isSelected ? '1rem' : '.5rem',
  }),
};

const spanVariants = {
  initial: { width: 0, opacity: 0 },
  animate: { width: 'auto', opacity: 1 },
  exit: { width: 0, opacity: 0 },
};

const transition = { delay: 0.1, type: 'spring', bounce: 0, duration: 0.6 };

export function ExpandableTabs({
  tabs,
  className,
  activeColor = 'text-primary',
  onChange,
}: ExpandableTabsProps) {
  const [selectedIndex, setSelectedIndex] = React.useState<number>(0);
  const containerRef = React.useRef<HTMLDivElement>(null);

  const handleSelect = (index: number) => {
    setSelectedIndex(index);
    onChange?.(index);
  };

  return (
    <div
      ref={containerRef}
      className={cn(
        'flex items-center gap-0.5 overflow-hidden rounded-2xl border outline-none',
        className
      )}
    >
      {tabs.map((tab, index) => {
        const isActive = selectedIndex === index;

        return (
          <motion.button
            key={tab.id}
            variants={buttonVariants}
            initial={false}
            animate='animate'
            custom={isActive}
            onClick={() => handleSelect(index)}
            transition={transition}
            className={cn(
              'relative flex items-center rounded-xl px-4 py-2 text-sm font-medium outline-none transition-colors duration-300',
              isActive
                ? cn('bg-muted', activeColor)
                : 'text-muted-foreground hover:bg-muted hover:text-foreground'
            )}
          >
            {tab.icon}
            <AnimatePresence initial={false}>
              {isActive && (
                <motion.span
                  variants={spanVariants}
                  initial='initial'
                  animate='animate'
                  exit='exit'
                  transition={transition}
                  className='overflow-hidden truncate'
                >
                  {tab.title}
                </motion.span>
              )}
            </AnimatePresence>
          </motion.button>
        );
      })}
    </div>
  );
}
