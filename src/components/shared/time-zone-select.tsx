'use client';

import { useId, useMemo, useState } from 'react';

import { CheckIcon, ChevronDownIcon } from 'lucide-react';

import { cn } from '@/lib/utils';

import { useDirection } from '@/hooks/use-direction';

import { Button } from '@/components/ui/button';
import {
  Command,
  CommandItem,
  CommandList,
  CommandEmpty,
  CommandGroup,
  CommandInput,
} from '@/components/ui/command';
import { Label } from '@/components/ui/label';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';

export function TimeZoneSelect() {
  const id = useId();
  const [value, setValue] = useState<string>('');
  const [open, setOpen] = useState<boolean>(false);
  const { locale } = useDirection();

  const timezones = Intl.supportedValuesOf('timeZone');

  const formattedTimezones = useMemo(() => {
    return timezones
      .map((timezone) => {
        const formatter = new Intl.DateTimeFormat(locale, {
          timeZone: timezone,
          timeZoneName: 'shortOffset',
        });
        const parts = formatter.formatToParts(new Date());
        const offset =
          parts.find((part) => part.type === 'timeZoneName')?.value || '';
        const modifiedOffset = offset === 'GMT' ? 'GMT+0' : offset;

        return {
          value: timezone,
          label: `(${modifiedOffset}) ${timezone.replace(/_/g, ' ')}`,
          numericOffset: parseInt(
            offset.replace('GMT', '').replace('+', '') || '0'
          ),
        };
      })
      .sort((a, b) => a.numericOffset - b.numericOffset);
  }, [locale, timezones]);

  return (
    <div>
      <Label htmlFor={id}>Time Zone</Label>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            id={id}
            variant='outline'
            role='combobox'
            aria-expanded={open}
            className='w-full justify-between border-input bg-background px-3 font-normal outline-none outline-offset-0 hover:bg-background focus-visible:outline-[3px]'
          >
            <span className={cn('truncate', !value && 'text-muted-foreground')}>
              {value
                ? formattedTimezones.find(
                    (timezone) => timezone.value === value
                  )?.label
                : 'Select timezone'}
            </span>
            <ChevronDownIcon
              size={16}
              className='shrink-0 text-muted-foreground/80'
              aria-hidden='true'
            />
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className='w-full min-w-[var(--radix-popper-anchor-width)] border-input p-0'
          align='start'
        >
          <Command
            filter={(value, search) => {
              const normalizedValue = value.toLowerCase();
              const normalizedSearch = search.toLowerCase().replace(/\s+/g, '');
              return normalizedValue.includes(normalizedSearch) ? 1 : 0;
            }}
          >
            <CommandInput placeholder='Search timezone...' />
            <CommandList>
              <CommandEmpty>No timezone found.</CommandEmpty>
              <CommandGroup>
                {formattedTimezones.map(({ value: itemValue, label }) => (
                  <CommandItem
                    key={itemValue}
                    value={itemValue}
                    onSelect={(currentValue) => {
                      setValue(currentValue === value ? '' : currentValue);
                      setOpen(false);
                    }}
                  >
                    {label}
                    {value === itemValue && (
                      <CheckIcon size={16} className='ms-auto' />
                    )}
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
}
