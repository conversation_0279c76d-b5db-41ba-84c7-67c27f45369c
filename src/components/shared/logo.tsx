'use client';

import { useTheme } from 'next-themes';

import { cn } from '@/lib/utils';

import { Link } from '@/i18n/routing';

import { Icons } from '@/components/shared/icons';

interface LogoProps {
  linkClassName?: string;
  classNameDark?: string;
  classNameLight?: string;
  showText?: boolean;
  textClassName?: string;
  mode?: 'dark' | 'light';
}

export default function Logo({
  linkClassName,
  classNameDark,
  classNameLight,
  showText = true,
  textClassName,
  mode,
}: LogoProps) {
  const { resolvedTheme } = useTheme();
  const isDarkMode = resolvedTheme === 'dark';

  const logoMode = mode ?? (isDarkMode ? 'light' : 'dark');
  const LogoIcon = logoMode === 'light' ? Icons.logoLight : Icons.logoDark;
  const logoClassName = logoMode === 'light' ? classNameLight : classNameDark;

  return (
    <Link
      href='/'
      aria-label='Home'
      className={cn(
        'flex items-center gap-2 text-[#34315a] dark:text-white',
        linkClassName
      )}
      dir='ltr'
    >
      <LogoIcon className={cn('size-12', logoClassName)} />
      {showText && (
        <span
          className={cn('text-lg font-bold', textClassName)}
          aria-hidden='true'
        >
          KWORE
        </span>
      )}
    </Link>
  );
}
