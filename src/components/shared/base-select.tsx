'use client';

import React from 'react';

import { Ban } from 'lucide-react';

import { cn } from '@/lib/utils';

import { Label } from '@/components/ui/label';
import {
  Select,
  SelectItem,
  SelectValue,
  SelectContent,
  SelectTrigger,
} from '@/components/ui/select';

type SelectOption = {
  value: string;
  label?: string;
  icon?: React.ReactNode;
};

interface BaseSelectProps extends React.HTMLProps<HTMLSelectElement> {
  label?: string;
  labelClassName?: string;
  wrapperClassName?: string;
  options: SelectOption[];
  showOptionLabel?: boolean;
  showOptionIcon?: boolean;
  placeholder?: string;
  required?: boolean;
  hideTooltip?: boolean;
  isLoading?: boolean;
  value?: string;
  onValueChange?: (value: string) => void;
  onBlur?: () => void;
  name?: string;
  error?: string | null;
}

export const BaseSelect = ({
  label,
  labelClassName,
  wrapperClassName,
  options,
  showOptionLabel = true,
  showOptionIcon = false,
  hideTooltip = true,
  required = true,
  isLoading,
  disabled,
  value,
  onValueChange,
  onBlur,
  name,
  error,
  ...props
}: BaseSelectProps) => {
  return (
    <div className={cn('space-y-2', wrapperClassName)}>
      <div className='grid gap-2'>
        {label && (
          <Label htmlFor={name} className={cn(labelClassName)}>
            {label}
            {required && <span className='ms-0.5 text-destructive'>*</span>}
          </Label>
        )}
        <Select
          value={value}
          onValueChange={(value) => {
            if (!disabled && onValueChange) {
              onValueChange(value);
            }
          }}
          disabled={disabled}
        >
          <SelectTrigger
            id={name}
            onBlur={onBlur}
            className={cn(
              '[&>span]:flex [&>span]:items-center [&>span]:gap-2',
              props.className
            )}
            isLoading={isLoading}
          >
            <SelectValue placeholder={props.placeholder} />
          </SelectTrigger>

          <SelectContent className='[&_*[role=option]>span>svg]:shrink-0 [&_*[role=option]>span>svg]:text-muted-foreground/80 [&_*[role=option]>span]:end-2 [&_*[role=option]>span]:start-auto [&_*[role=option]>span]:flex [&_*[role=option]>span]:items-center [&_*[role=option]>span]:gap-2 [&_*[role=option]]:pe-8 [&_*[role=option]]:ps-2'>
            {options.map((option) => (
              <SelectItem
                key={option.value}
                value={option.value}
                className='[&>span]:flex [&>span]:items-center'
                tooltip={!hideTooltip ? option.label : undefined}
              >
                {showOptionIcon && <span>{option.icon ?? <Ban />}</span>}
                {showOptionLabel && <span>{option.label}</span>}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      {error && <p className='text-sm text-destructive'>{error}</p>}
    </div>
  );
};
