'use client';

import { useTranslations } from 'next-intl';
import * as React from 'react';

import { Plus, ChevronsUpDown } from 'lucide-react';

import { getFallbackInitials } from '@/lib/get-fallback-initials';
import { cn } from '@/lib/utils';

import { useBrandById, useBrandsByCompanyId } from '@/api/hooks/brand/queries';
import { useCurrentUserStore } from '@/stores/current-user-store';

import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  useSidebar,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
} from '@/components/ui/sidebar';

export function BrandSwitcher() {
  const t = useTranslations();
  const { isMobile, state } = useSidebar();

  const { getUser, setUser } = useCurrentUserStore();
  const user = getUser();

  const { data: brands } = useBrandsByCompanyId();
  const { data: currentBrand } = useBrandById(user?.brandId);

  return (
    <SidebarMenu className={cn({ 'w-fit': state === 'collapsed' })}>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              variant='outline'
              size='lg'
              className={cn(
                'border-none bg-transparent hover:bg-[#3776dc]/20 hover:text-background data-[state=open]:bg-[#3776dc]/20 data-[state=open]:text-background',
                {
                  'shadow-none hover:rounded-full': state === 'collapsed',
                }
              )}
            >
              <div className='flex aspect-square size-8 items-center justify-center rounded-lg text-sidebar-primary-foreground'>
                <Avatar className='h-8 w-8 rounded-full'>
                  <AvatarImage
                    src={
                      currentBrand?.GcsLinkPublic &&
                      currentBrand?.GcsLinkPublic.length > 0
                        ? currentBrand?.GcsLinkPublic
                        : '/placeholder.svg?height=40&width=40'
                    }
                    alt={currentBrand?.Description}
                  />

                  <AvatarFallback className='rounded-lg bg-gray-100 text-black'>
                    {getFallbackInitials(currentBrand?.Name ?? '')}
                  </AvatarFallback>
                </Avatar>
              </div>
              <div className='grid flex-1 text-left text-sm leading-tight'>
                <span className='truncate font-semibold'>
                  {currentBrand?.Name || 'loading...'}
                </span>
              </div>
              <ChevronsUpDown className='ml-auto' />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className='w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg'
            align='start'
            side={isMobile ? 'bottom' : 'right'}
            sideOffset={4}
          >
            <DropdownMenuLabel className='text-xs text-muted-foreground'>
              {t('brands')}
            </DropdownMenuLabel>
            <ScrollArea className='h-48 w-full'>
              {brands &&
                brands?.map((brand) => (
                  <DropdownMenuItem
                    key={brand.Id}
                    onClick={() => {
                      setUser({ ...user, brandId: brand.Id });
                    }}
                    className='gap-2 p-2'
                  >
                    <Avatar className='h-8 w-8 rounded-full border'>
                      <AvatarImage
                        src={
                          brand?.GcsLinkPublic ??
                          '/placeholder.svg?height=40&width=40'
                        }
                        alt={brand?.Description}
                      />

                      <AvatarFallback className='rounded-lg'>
                        {getFallbackInitials(brand?.Name ?? '')}
                      </AvatarFallback>
                    </Avatar>
                    <span className='truncate'>{brand.Name}</span>
                  </DropdownMenuItem>
                ))}
            </ScrollArea>

            <DropdownMenuSeparator />

            <DropdownMenuItem className='gap-2 p-2'>
              <div className='flex size-6 items-center justify-center rounded-md border bg-background'>
                <Plus className='size-4' />
              </div>
              <div className='font-medium text-muted-foreground first-letter:uppercase'>
                {t('add_brand')}
              </div>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}
