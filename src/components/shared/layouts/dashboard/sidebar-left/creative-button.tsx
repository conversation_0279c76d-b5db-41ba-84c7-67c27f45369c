'use client';

import { useTranslations } from 'next-intl';

import { cn } from '@/lib/utils';

import { useDirection } from '@/hooks/use-direction';

import { Link, usePathname } from '@/i18n/routing';

import { Icons } from '@/components/shared/icons';
import { Button } from '@/components/ui/button';
import { useSidebar } from '@/components/ui/sidebar';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';

export function CreativeButton() {
  const { state } = useSidebar();
  const t = useTranslations();
  const { langDir } = useDirection();
  const pathname = usePathname();

  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <Link
          href='/dashboard/creative'
          className={cn('w-full', {
            'w-fit': state === 'collapsed',
          })}
        >
          <Button
            variant='outline'
            className={cn(
              'w-full justify-start gap-3 rounded-lg border-[#3776DC] bg-[#3776DC]/20 px-4 py-[1.3rem] hover:bg-[#3776DC]/10 hover:text-background',
              {
                'p-2': state === 'collapsed',
                'bg-[#3776DC] hover:bg-[#3776DC]':
                  pathname === '/dashboard/creative',
              }
            )}
            iconStart={<Icons.sparkles className='!size-5' />}
          >
            {state === 'expanded' && t('quick_nav.creative')}
          </Button>
        </Link>
      </TooltipTrigger>
      <TooltipContent
        className={cn('px-2 py-1 capitalize', { hidden: state === 'expanded' })}
        side={langDir === 'ltr' ? 'right' : 'left'}
      >
        {t('quick_nav.creative')}
      </TooltipContent>
    </Tooltip>
  );
}
