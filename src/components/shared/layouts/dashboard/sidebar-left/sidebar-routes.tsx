'use client';

import type { LucideIcon } from 'lucide-react';

import { cn } from '@/lib/utils';

import { Link, usePathname } from '@/i18n/routing';

import { Separator } from '@/components/ui/separator';
import {
  useSidebar,
  SidebarMenu,
  SidebarGroup,
  SidebarMenuItem,
  SidebarGroupLabel,
  SidebarMenuButton,
  SidebarGroupContent,
} from '@/components/ui/sidebar';

interface SidebarRoutesProps {
  routes: {
    groupName: string;
    children: {
      name: string;
      url: string;
      icon: LucideIcon | React.FC<React.SVGProps<SVGSVGElement>>;
    }[];
  }[];
}

export function SidebarRoutes({ routes, ...props }: SidebarRoutesProps) {
  const pathname = usePathname();
  const { state } = useSidebar();

  return (
    <>
      {routes.map((route, index) => (
        <SidebarGroup
          key={index}
          {...props}
          className={cn('py-1', { 'items-center p-0': state === 'collapsed' })}
        >
          {state === 'collapsed' ? (
            <Separator className='my-4 w-10 rounded-full opacity-25' />
          ) : (
            <SidebarGroupLabel className='font-light capitalize text-background'>
              {route.groupName}
            </SidebarGroupLabel>
          )}

          <SidebarGroupContent>
            <SidebarMenu
              className={cn({
                'flex w-full items-center justify-center gap-2':
                  state === 'collapsed',
              })}
            >
              {route.children.map((subRoute) => {
                const isActive = pathname === subRoute.url;

                return (
                  <SidebarMenuItem key={subRoute.name}>
                    <SidebarMenuButton
                      asChild
                      tooltip={
                        subRoute.name.charAt(0).toUpperCase() +
                        subRoute.name.slice(1)
                      }
                      isActive={isActive}
                      className={cn(
                        'gap-3 rounded-lg px-4 py-[1.3rem] text-sm group-data-[collapsible=icon]:!justify-center group-data-[collapsible=icon]:!p-[1.15rem]',
                        {
                          '!bg-background !text-primary': isActive,
                          'hover:bg-background hover:text-primary': !isActive,
                        }
                      )}
                    >
                      <Link href={subRoute.url}>
                        <subRoute.icon className={cn('!size-5')} />
                        {state === 'expanded' && (
                          <span className='capitalize'>{subRoute.name}</span>
                        )}
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                );
              })}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      ))}
    </>
  );
}
