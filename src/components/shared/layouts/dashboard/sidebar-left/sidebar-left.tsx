'use client';

import { useTranslations } from 'next-intl';

import { SIDEBAR_KEYBOARD_SHORTCUT } from '@/constants/sidebar';
import { DashboardTestIds } from '@/constants/test-ids';

import { createSidebarConfig } from '@/config/sidebar-routes-config';

import { SidebarTriggerTooltip } from '@/components/shared/layouts/dashboard/header/sidebar-trigger-tooltip';
import { BottomNav } from '@/components/shared/layouts/dashboard/sidebar-left/bottom-nav';
import { BrandSwitcher } from '@/components/shared/layouts/dashboard/sidebar-left/brand-switcher';
import { CreativeButton } from '@/components/shared/layouts/dashboard/sidebar-left/creative-button';
import { SidebarRoutes } from '@/components/shared/layouts/dashboard/sidebar-left/sidebar-routes';
import Logo from '@/components/shared/logo';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Sidebar,
  useSidebar,
  SidebarRail,
  SidebarHeader,
  SidebarFooter,
  SidebarContent,
} from '@/components/ui/sidebar';

export function SidebarLeft({
  ...props
}: React.ComponentProps<typeof Sidebar>) {
  const t = useTranslations();
  const { state } = useSidebar();
  const { mainRoutes, bottomNav } = createSidebarConfig(t);
  const isMac = navigator.userAgent.includes('Mac');

  return (
    <Sidebar
      className='!border-0 text-background dark:text-sidebar-foreground'
      {...props}
    >
      <SidebarHeader className='relative items-center justify-center gap-4'>
        <span className='absolute -end-5 top-5 z-50'>
          <SidebarTriggerTooltip />
        </span>
        <Logo
          mode='light'
          showText={state === 'expanded'}
          linkClassName='gap-3 py-2'
          classNameLight='size-10'
          textClassName='text-[2rem] text-background'
        />
        <BrandSwitcher />
        <CreativeButton />
      </SidebarHeader>
      <SidebarContent>
        <ScrollArea>
          <SidebarRoutes routes={mainRoutes} />
        </ScrollArea>
      </SidebarContent>
      <SidebarFooter>
        <BottomNav items={bottomNav} className='mt-auto' />
      </SidebarFooter>
      <SidebarRail
        data-testid={DashboardTestIds.sidebar_rail}
        title={
          isMac
            ? `⌘+${SIDEBAR_KEYBOARD_SHORTCUT}`
            : `Ctrl+${SIDEBAR_KEYBOARD_SHORTCUT}`
        }
      />
    </Sidebar>
  );
}
