import { useTranslations } from 'next-intl';
import React from 'react';

import { type LucideIcon } from 'lucide-react';

import { cn } from '@/lib/utils';

import { useDirection } from '@/hooks/use-direction';

import { Link, usePathname } from '@/i18n/routing';

import { Icons } from '@/components/shared/icons';
import { Button } from '@/components/ui/button';
import {
  useSidebar,
  SidebarMenu,
  SidebarGroup,
  SidebarMenuItem,
  SidebarMenuButton,
} from '@/components/ui/sidebar';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';

export function BottomNav({
  items,
}: {
  items: {
    title: string;
    url: string;
    icon: LucideIcon | React.FC<React.SVGProps<SVGSVGElement>>;
    badge?: React.ReactNode;
  }[];
} & React.ComponentPropsWithoutRef<typeof SidebarGroup>) {
  const { langDir } = useDirection();
  const pathname = usePathname();
  const { state } = useSidebar();
  const t = useTranslations();

  return (
    <SidebarMenu
      className={cn({
        'flex w-full items-center justify-center gap-2': state === 'collapsed',
      })}
    >
      {items.map((item) => {
        const isActive = pathname === item.url;

        return (
          <SidebarMenuItem key={item.title}>
            <SidebarMenuButton
              asChild
              tooltip={item.title}
              className={cn(
                'gap-3 rounded-lg px-4 py-[1.3rem] text-sm group-data-[collapsible=icon]:!justify-center group-data-[collapsible=icon]:!p-[1.15rem]',
                {
                  '!bg-background !text-primary': isActive,
                  'hover:bg-background hover:text-primary': !isActive,
                }
              )}
            >
              <Link href={item.url}>
                <item.icon className={cn('!size-5')} />
                {state === 'expanded' && (
                  <span className='capitalize'>{item.title}</span>
                )}
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        );
      })}
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant='ghost'
            className={cn(
              'w-full justify-start gap-3 border-[#3776DC] bg-transparent py-[1.3rem] pe-4 ps-3 text-sm font-normal hover:bg-background hover:text-primary',
              {
                'w-fit justify-center p-2': state === 'collapsed',
              }
            )}
            iconStart={<Icons.loginIcon className={cn('!size-6')} />}
          >
            {state === 'expanded' && (
              <Link href='/creative'>{t('logout')}</Link>
            )}
          </Button>
        </TooltipTrigger>
        <TooltipContent
          className={cn('px-2 py-1', { hidden: state === 'expanded' })}
          side={langDir === 'ltr' ? 'right' : 'left'}
        >
          {t('logout')}
        </TooltipContent>
      </Tooltip>
    </SidebarMenu>
  );
}
