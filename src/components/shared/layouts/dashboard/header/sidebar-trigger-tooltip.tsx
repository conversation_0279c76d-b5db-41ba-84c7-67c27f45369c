'use client';

import { useTranslations } from 'next-intl';
import React from 'react';

import { useSidebar, SidebarTrigger } from '@/components/ui/sidebar';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';

export function SidebarTriggerTooltip() {
  const { open } = useSidebar();
  const t = useTranslations();

  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <SidebarTrigger
          size='setting'
          variant='default'
          className='rounded-full bg-sidebar hover:bg-background hover:text-sidebar'
        />
      </TooltipTrigger>
      <TooltipContent className='px-2 py-1 text-xs' side='bottom'>
        {open ? t('close') : t('open')} {t('sidebar')}
      </TooltipContent>
    </Tooltip>
  );
}
