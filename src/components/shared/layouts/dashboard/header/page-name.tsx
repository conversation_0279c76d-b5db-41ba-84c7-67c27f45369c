'use client';

import React from 'react';

import { Link, usePathname } from '@/i18n/routing';

import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';

interface BreadcrumbItemType {
  href: string;
  label: string;
}

/**
 * Transforms a URL segment into a readable label
 * @param segment - The URL segment to transform
 * @returns Formatted string with proper capitalization
 */
const formatSegmentLabel = (segment: string): string => {
  return segment.replace(/-/g, ' ').replace(/\b\w/g, (c) => c.toUpperCase());
};

/**
 * Handles special case for numeric endpoints in breadcrumb links
 * @param href - The original href
 * @returns Modified href with numeric endpoints removed if present
 */
const sanitizeHref = (href: string): string => {
  return href.replace(/\/\d+$/, '');
};

/**
 * Generates breadcrumb items from the current pathname
 * @param pathname - Current URL pathname
 * @returns Array of breadcrumb items with href and label
 */
const generateBreadcrumbItems = (pathname: string): BreadcrumbItemType[] => {
  const segments = pathname.split('/').filter(Boolean);

  return [
    { href: '/dashboard', label: 'Dashboard' },
    ...segments.slice(1).map((segment, index) => ({
      href: `/${segments.slice(0, index + 2).join('/')}`,
      label: formatSegmentLabel(segment),
    })),
  ];
};

/**
 * BreadcrumbLink component with proper link handling
 */
const BreadcrumbLinkComponent: React.FC<{
  href: string;
  label: string;
  isSecondLast: boolean;
}> = ({ href, label, isSecondLast }) => {
  const finalHref = isSecondLast ? sanitizeHref(href) : href;
  const finalLabel = label.replace(/\d+$/, '').trim() || label;

  const handleClick = (e: React.MouseEvent) => {
    if (/\d+$/.test(href)) {
      e.preventDefault();
      window.location.href = sanitizeHref(href);
    }
  };

  return (
    <BreadcrumbLink asChild>
      <Link href={finalHref} onClick={handleClick}>
        {finalLabel}
      </Link>
    </BreadcrumbLink>
  );
};

export function PageName() {
  const pathname = usePathname();
  const breadcrumbItems = generateBreadcrumbItems(pathname);

  return (
    <Breadcrumb>
      <BreadcrumbList>
        {breadcrumbItems.map((item, index) => {
          const isLast = index === breadcrumbItems.length - 1;
          const isSecondLast = index === breadcrumbItems.length - 2;

          return (
            <React.Fragment key={item.href}>
              <BreadcrumbItem className={!isLast ? 'hidden md:block' : ''}>
                {!isLast ? (
                  <BreadcrumbLinkComponent
                    href={item.href}
                    label={item.label}
                    isSecondLast={isSecondLast}
                  />
                ) : (
                  <BreadcrumbPage>{item.label}</BreadcrumbPage>
                )}
              </BreadcrumbItem>
              {!isLast && <BreadcrumbSeparator className='hidden md:block' />}
            </React.Fragment>
          );
        })}
      </BreadcrumbList>
    </Breadcrumb>
  );
}
