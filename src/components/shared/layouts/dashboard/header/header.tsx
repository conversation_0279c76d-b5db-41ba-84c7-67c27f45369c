import React from 'react';

import { HeaderOptions } from '@/components/shared/layouts/dashboard/header/header-options';
import { PageName } from '@/components/shared/layouts/dashboard/header/page-name';

export function Header({ ...props }: React.HTMLAttributes<HTMLElement>) {
  return (
    <header
      className='sticky top-0 flex shrink-0 items-center gap-2 border-0 bg-background px-4 py-5'
      {...props}
    >
      <div className='flex flex-1 items-center'>
        <PageName />
      </div>

      <HeaderOptions />
    </header>
  );
}
