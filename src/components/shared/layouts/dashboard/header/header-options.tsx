import React from 'react';

import { Settings } from 'lucide-react';

import { <PERSON> } from '@/i18n/routing';

import { LanguageSwitcher } from '@/components/shared/language-switcher';
import { Button } from '@/components/ui/button';

export function HeaderOptions() {
  return (
    <div className='flex items-center overflow-hidden rounded-full bg-[#F4F4F5]'>
      <LanguageSwitcher align='center' variant='ghost' size='icon' />
      <Link href='/settings'>
        <Button variant='ghost' size='icon'>
          <Settings className='text-[#71717A]' />
        </Button>
      </Link>
    </div>
  );
}
