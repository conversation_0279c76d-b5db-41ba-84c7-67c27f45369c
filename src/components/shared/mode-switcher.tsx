'use client';

import { useTranslations } from 'next-intl';
import * as React from 'react';

import { Sun, Moon } from 'lucide-react';

import { cn } from '@/lib/utils';

import { useLocalTheme } from '@/providers/local-theme-provider';

import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';

interface ModeToggleProps {
  align?: 'start' | 'end' | 'center';
  variant?:
    | 'outline'
    | 'link'
    | 'default'
    | 'destructive'
    | 'secondary'
    | 'ghost';
  className?: string;
}

export function ModeSwitcher({
  align = 'end',
  variant = 'outline',
  className,
}: ModeToggleProps) {
  const { setTheme, theme } = useLocalTheme();
  const t = useTranslations();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <div>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant={variant}
                size='setting'
                className={cn(
                  'size-fit p-1',
                  {
                    'bg-black text-background': theme === 'dark',
                  },
                  className
                )}
              >
                {theme === 'dark' ? (
                  <Moon className='size-4' />
                ) : (
                  <Sun className='size-4' />
                )}
              </Button>
            </TooltipTrigger>
            <TooltipContent className='px-2 py-1 text-xs' side='bottom'>
              {t('change_mode')}
            </TooltipContent>
          </Tooltip>
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent align={align}>
        <DropdownMenuItem onClick={() => setTheme('light')}>
          {t('light_mode')}
        </DropdownMenuItem>
        <DropdownMenuItem className='group' onClick={() => setTheme('dark')}>
          {t('dark_mode')}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
