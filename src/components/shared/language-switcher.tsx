'use client';

import { useLocale, useTranslations } from 'next-intl';
import Image from 'next/image';
import React from 'react';
import { toast } from 'sonner';

import { Globe, ChevronDown } from 'lucide-react';

import { cn } from '@/lib/utils';

import { usePopover } from '@/hooks/use-popover';

import { Locale, useRouter, usePathname } from '@/i18n/routing';

import { languageConfig } from '@/config/language-config';

import { Button } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';

type LanguageSwitcherProps = {
  align?: 'start' | 'end' | 'center';
  variant?:
    | 'outline'
    | 'link'
    | 'default'
    | 'destructive'
    | 'secondary'
    | 'ghost';
  size?: 'default' | 'sm' | 'lg' | 'icon' | 'setting';
  className?: string;
  showCurrentLanguageName?: boolean;
  showIcon?: boolean;
};

export function LanguageSwitcher({
  align = 'center',
  size = 'setting',
  variant = 'outline',
  className,
  showCurrentLanguageName = false,
  showIcon = false,
}: LanguageSwitcherProps) {
  const { open, onOpenChange, close } = usePopover();
  const router = useRouter();
  const pathname = usePathname();

  const locale = useLocale();
  const t = useTranslations();
  const languages = languageConfig(t);
  const currentLang = languages.find((lang) => lang.code === locale);

  async function changeLanguage(nextLocale: Locale) {
    if (locale === nextLocale) {
      toast.info(t('language_current'));
      return;
    }

    router.replace({ pathname }, { locale: nextLocale });

    // I don't know why this is needed, but it is used to show the right toast message when the language changes
    // But it must change
    const messages = await import(`@/i18n/locales/${nextLocale}/common.json`);
    setTimeout(() => {
      toast.success(messages.language_changed);
    }, 1000);
  }

  return (
    <Popover open={open} onOpenChange={onOpenChange}>
      <PopoverTrigger asChild>
        <div>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                size={size}
                variant={variant}
                onClick={() => onOpenChange(!open)}
                className={cn('', className)}
              >
                {currentLang ? (
                  <div className='flex items-center gap-2'>
                    <Image
                      src={currentLang.image}
                      alt={currentLang.title}
                      width={20}
                      height={20}
                      className='size-4 rounded-full object-cover'
                    />
                    {showCurrentLanguageName && (
                      <span className='text-xs'>{currentLang.title}</span>
                    )}
                    {showIcon && (
                      <ChevronDown className='ms-3 text-muted-foreground' />
                    )}
                  </div>
                ) : (
                  <Globe />
                )}
              </Button>
            </TooltipTrigger>
            <TooltipContent className='px-2 py-1' side='bottom'>
              {t('change_language')}
            </TooltipContent>
          </Tooltip>
        </div>
      </PopoverTrigger>

      <PopoverContent
        className='flex w-auto flex-col gap-0.5 px-1 py-2'
        align={align}
        onMouseLeave={() => onOpenChange(false)}
      >
        {languages.map((lang) => (
          <div
            key={lang.code}
            className={cn(
              'flex cursor-pointer items-center gap-3 rounded-md p-2 hover:bg-accent',
              locale === lang.code && 'bg-accent'
            )}
            onClick={async () => {
              close();
              await changeLanguage(lang.code as Locale);
            }}
          >
            <Image
              src={lang.image}
              alt={lang.title}
              width={20}
              height={20}
              className='rounded-sm object-cover'
            />
            <span className='flex-1 text-sm font-medium'>{lang.title}</span>
          </div>
        ))}
      </PopoverContent>
    </Popover>
  );
}
