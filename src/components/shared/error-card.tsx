import { RefreshCw, AlertCircle } from 'lucide-react';

import { useRouter } from '@/i18n/routing';

import { Button } from '@/components/ui/button';
import {
  Card,
  CardTitle,
  CardHeader,
  CardContent,
  CardDescription,
} from '@/components/ui/card';

interface ErrorCardProps {
  handleRetry: () => void;
}

export function ErrorCard({ handleRetry }: ErrorCardProps) {
  const router = useRouter();

  const handleRefresh = () => {
    router.refresh();
    handleRetry();
  };

  return (
    <Card className='border-red-200 bg-red-50/50'>
      <CardHeader className='pb-4 text-center'>
        <div className='mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100'>
          <AlertCircle className='h-6 w-6 text-red-600' />
        </div>
        <CardTitle className='text-red-900'>Failed to Load Data</CardTitle>
        <CardDescription className='text-red-700'>
          We couldn't retrieve the information from our servers. Please check
          your connection and try again.
        </CardDescription>
      </CardHeader>

      <CardContent className='space-y-3'>
        <Button
          onClick={handleRetry}
          variant='destructive'
          className='w-full'
          iconStart={<RefreshCw />}
        >
          Try Again
        </Button>
        <Button
          variant='outline'
          onClick={handleRefresh}
          className='w-full border-red-200 text-red-700 hover:bg-red-50'
        >
          Refresh Page
        </Button>
      </CardContent>
    </Card>
  );
}
