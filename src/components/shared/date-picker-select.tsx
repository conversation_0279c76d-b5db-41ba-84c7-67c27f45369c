import { useState } from 'react';

import { CalendarIcon } from 'lucide-react';

import { cn } from '@/lib/utils';

import { useFormatDate } from '@/hooks/use-date-fns-locale';

import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Label } from '@/components/ui/label';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';

export function DatePickerSelect() {
  const { format } = useFormatDate();
  const [date, setDate] = useState<Date | undefined>();

  return (
    <div>
      <Label>Date & time</Label>
      <Popover>
        <PopoverTrigger asChild>
          <Button
            variant={'outline'}
            className={cn(
              'group w-full justify-between border-input bg-background px-3 font-normal outline-none outline-offset-0 hover:bg-background focus-visible:outline-[3px]',
              !date && 'text-muted-foreground'
            )}
          >
            <span className={cn('truncate', !date && 'text-muted-foreground')}>
              {date ? format(date, 'PPP') : 'Pick a date'}
            </span>
            <CalendarIcon
              size={16}
              className='shrink-0 text-muted-foreground/80 transition-colors group-hover:text-foreground'
              aria-hidden='true'
            />
          </Button>
        </PopoverTrigger>
        <PopoverContent className='w-auto p-2' align='start'>
          <Calendar mode='single' selected={date} onSelect={setDate} />
        </PopoverContent>
      </Popover>
    </div>
  );
}
