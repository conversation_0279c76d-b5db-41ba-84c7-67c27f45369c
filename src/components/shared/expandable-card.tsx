import { useState } from 'react';

import { ChevronDown } from 'lucide-react';

import { cn } from '@/lib/utils';

import {
  Card,
  CardTitle,
  CardHeader,
  CardContent,
  CardDescription,
} from '@/components/ui/card';

interface ExpandableCardProps {
  title?: string;
  description?: string;
  required?: boolean;
  initialOpen?: boolean;
  children: React.ReactNode;
}

export function ExpandableCard({
  title,
  description,
  required = false,
  initialOpen = false,
  children,
}: ExpandableCardProps) {
  const [isCardExpanded, setIsCardExpanded] = useState(initialOpen);

  return (
    <Card>
      <CardHeader
        className={cn('space-y-0 p-0 px-2 pt-3', { 'pb-3': !isCardExpanded })}
      >
        <CardTitle className='relative'>
          <span className='text-sm'>{title}</span>
          {required && <span className='ms-0.5 text-destructive'>*</span>}
          <div
            className='absolute end-0 top-1/2 -translate-y-1/2 rounded-full p-1 transition-colors hover:bg-muted-foreground/20'
            onClick={() => setIsCardExpanded(!isCardExpanded)}
          >
            <ChevronDown
              className={cn('size-5 text-muted-foreground', {
                'rotate-180': isCardExpanded,
              })}
            />
          </div>
        </CardTitle>
        <CardDescription className='w-[90%] text-xs'>
          {description}
        </CardDescription>
      </CardHeader>
      {isCardExpanded && (
        <CardContent className='px-2 py-3'>{children}</CardContent>
      )}
    </Card>
  );
}
