'use client';

import { useAllInsights } from '@/api/hooks/social media/queries';
import { useCurrentUserStore } from '@/stores/current-user-store';

import { StatCard } from '@/components/dashboard/stat-card';
import { Skeleton } from '@/components/ui/skeleton';

export default function SocialMediaInsights() {
  const { getUser } = useCurrentUserStore();
  const user = getUser();

  const {
    data: stats,
    isPending,
    isError,
    error,
  } = useAllInsights(user?.brandId, user?.companyId);

  const totalLikes = [stats?.facebook, stats?.instagram, stats?.linkedin]
    .filter(Boolean)
    .reduce((total, platform) => {
      const platformTotal =
        platform?.LifeTime?.reduce(
          (acc, curr) => acc + (curr.LikeTotal || 0),
          0
        ) || 0;
      return total + platformTotal;
    }, 0);

  const averageTrend =
    [stats?.facebook, stats?.instagram, stats?.linkedin]
      .filter(Boolean)
      .reduce(
        (sum, platform) => sum + (platform?.DailyImpressionPercentage ?? 0),
        0
      ) / 3;

  if (isPending) {
    return (
      <div className='grid auto-rows-min gap-4 md:grid-cols-4'>
        {[...Array(4)].map((_, i) => (
          <Skeleton key={i} className='aspect-video rounded-xl' />
        ))}
      </div>
    );
  }

  if (isError) {
    return <div>{error.message}</div>;
  }

  return (
    <div className='grid auto-rows-min gap-4 sm:grid-cols-2 sm:gap-6 md:grid-cols-4'>
      <StatCard
        title='Total Likes'
        total={totalLikes}
        trend={averageTrend ?? 0}
        isPositiveTrend={true}
      />
      <StatCard
        title='Total Comments'
        total={4000}
        trend={8}
        isPositiveTrend={true}
      />
      <StatCard
        title='Total Shares'
        total={1200000}
        trend={52.25}
        isPositiveTrend={true}
      />
      <StatCard
        title='Links Clicked'
        total={54}
        trend={2}
        isPositiveTrend={false}
      />
    </div>
  );
}
