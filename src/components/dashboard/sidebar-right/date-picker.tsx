'use client';

import { useDateFnsLocale } from '@/hooks/use-date-fns-locale';

import { useDateRangeStore } from '@/stores/date-range-store';

import { Calendar } from '@/components/ui/calendar';

export function DatePicker() {
  const { dateRange, setDateRange, currentMonth, setCurrentMonth } =
    useDateRangeStore();
  const dateFnsLocale = useDateFnsLocale();

  return (
    <Calendar
      mode='range'
      weekStartsOn={0}
      locale={dateFnsLocale}
      showOutsideDays={true}
      month={currentMonth}
      onMonthChange={setCurrentMonth}
      selected={dateRange}
      onSelect={setDateRange}
      // className='[&_[role=gridcell].bg-accent]:bg-primary/70 [&_[role=gridcell].bg-accent]:text-sidebar-primary-foreground'
    />
  );
}
