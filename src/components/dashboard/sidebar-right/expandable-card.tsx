'use client';

import { motion, AnimatePresence } from 'framer-motion';
import React, { useState } from 'react';

// import { Clock8, ScanEye } from 'lucide-react';
import ProfileCard from '@/components/demo/profile-card';
// import { Badge } from '@/components/ui/badge';

interface ExpandableCardProps {
  id: number;
  title: string;
  description: string;
  thumbnail: string;
  createdAt: string;
}

export const ExpandableCard = ({
  title,
  description,
  thumbnail,
  id,
  // createdAt,
}: ExpandableCardProps) => {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <div className='relative z-50 mx-auto overflow-hidden'>
      <ProfileCard />
      {/*<motion.div*/}
      {/*  layoutId={`card-${id}`}*/}
      {/*  onClick={() => setIsExpanded(true)}*/}
      {/*  className='relative z-50 h-36 w-full cursor-pointer overflow-hidden rounded-xl'*/}
      {/*>*/}
      {/*  <motion.img*/}
      {/*    src={thumbnail}*/}
      {/*    layoutId={`image-${id}`}*/}
      {/*    className='h-full w-full rounded-xl object-cover'*/}
      {/*    alt='thumbnail'*/}
      {/*  />*/}

      {/*  <div className='group absolute inset-0 z-10 grid place-items-center transition-all duration-500 hover:bg-accent/30'>*/}
      {/*    <ScanEye className='text-white/80 opacity-0 transition-all duration-500 group-hover:opacity-100' />*/}
      {/*  </div>*/}

      {/*  <div*/}
      {/*    dir='ltr'*/}
      {/*    className='absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent px-3 py-3 text-white'*/}
      {/*  >*/}
      {/*    <h3 className='font-bold leading-loose'>{title}</h3>*/}
      {/*    <p className='truncate text-xs leading-tight'>{description}</p>*/}
      {/*  </div>*/}
      {/*  <div className='absolute right-3 top-3'>*/}
      {/*    <Badge*/}
      {/*      variant='secondary'*/}
      {/*      className='flex cursor-auto items-center gap-1 hover:bg-secondary'*/}
      {/*    >*/}
      {/*      <Clock8 className='size-4' />*/}
      {/*      {createdAt}*/}
      {/*    </Badge>*/}
      {/*  </div>*/}
      {/*</motion.div>*/}

      <AnimatePresence>
        {isExpanded ? (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 0.5 }}
            exit={{ opacity: 0 }}
            className='fixed inset-0 z-40 bg-accent-foreground'
            onClick={() => setIsExpanded(false)}
          />
        ) : null}
      </AnimatePresence>

      <AnimatePresence>
        {isExpanded ? (
          <motion.div
            layoutId={`card-${id}`}
            className='fixed inset-0 z-50 flex cursor-pointer items-center justify-center'
            onClick={() => setIsExpanded(false)}
          >
            <div className='mx-4 w-full max-w-2xl overflow-hidden rounded-xl'>
              <motion.img
                src={thumbnail}
                layoutId={`image-${id}`}
                className='h-64 w-full object-cover'
                alt='thumbnail'
              />
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className='bg-gray-800 p-6 text-white'
              >
                <div dir='ltr'>
                  <p className='text-xl font-bold text-white md:text-4xl'>
                    {title}
                  </p>
                  <p className='my-4 max-w-lg text-base font-normal text-neutral-200'>
                    {description}
                  </p>
                </div>
              </motion.div>
            </div>
          </motion.div>
        ) : null}
      </AnimatePresence>
    </div>
  );
};
