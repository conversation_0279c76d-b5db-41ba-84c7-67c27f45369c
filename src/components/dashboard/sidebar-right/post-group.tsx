import { isToday } from 'date-fns';

import { ChevronRight, CalendarSearch } from 'lucide-react';

import { TPost } from '@/types/TPost';

import { formatGroupTime } from '@/lib/format-date';

import { useFormatDate, useDateFnsLocale } from '@/hooks/use-date-fns-locale';

import { ExpandableCard } from '@/components/dashboard/sidebar-right/expandable-card';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import {
  SidebarGroup,
  SidebarSeparator,
  SidebarGroupLabel,
  SidebarGroupContent,
} from '@/components/ui/sidebar';

interface PostGroupProps {
  date: Date;
  posts: TPost[];
}

export function PostGroup({ date, posts }: PostGroupProps) {
  const dateFnsLocale = useDateFnsLocale();
  const { format } = useFormatDate();

  const isTodayGroup = isToday(date);
  const formattedDate = format(date, 'PPP');

  return (
    <>
      <SidebarGroup className='px-0 py-0'>
        <Collapsible defaultOpen={true} className='group/collapsible'>
          <SidebarGroupLabel
            asChild
            className='group/label w-full text-sm text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground'
          >
            <CollapsibleTrigger className='flex items-center justify-between'>
              <div className='flex items-center gap-2'>
                <CalendarSearch className='size-4' />
                {formattedDate}
              </div>
              <ChevronRight className='transition-transform group-data-[state=open]/collapsible:rotate-90 rtl:rotate-180' />
            </CollapsibleTrigger>
          </SidebarGroupLabel>
          <CollapsibleContent className='py-2'>
            <SidebarGroupContent className='space-y-4'>
              {posts.map((post) => (
                <ExpandableCard
                  key={post.id}
                  id={post.id}
                  title={post.text}
                  description={post.description}
                  thumbnail={post.thumbnail}
                  createdAt={formatGroupTime(
                    post.createdAt,
                    isTodayGroup,
                    dateFnsLocale
                  )}
                />
              ))}
            </SidebarGroupContent>
          </CollapsibleContent>
        </Collapsible>
      </SidebarGroup>
      <SidebarSeparator className='mx-0' />
    </>
  );
}
