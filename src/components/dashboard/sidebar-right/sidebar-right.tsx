'use client';

import type * as React from 'react';

import { useTranslations } from 'next-intl';

import { TPost } from '@/types/TPost';

import { DatePicker } from '@/components/dashboard/sidebar-right/date-picker';
import { RecentPosts } from '@/components/dashboard/sidebar-right/recent-posts';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Sidebar,
  SidebarGroup,
  SidebarHeader,
  SidebarContent,
  SidebarGroupLabel,
  SidebarGroupContent,
} from '@/components/ui/sidebar';

export function SidebarRight({
  data,
  ...props
}: React.ComponentProps<typeof Sidebar> & { data: TPost[] }) {
  const t = useTranslations();

  return (
    <div className='h-full overflow-auto'>
      <Sidebar
        collapsible='none'
        className='hidden bg-background lg:flex'
        style={
          {
            '--sidebar-width': '16.5rem',
          } as React.CSSProperties
        }
        {...props}
      >
        <div className='flex h-full flex-col'>
          <SidebarHeader className='mx-auto p-0'>
            <SidebarGroup className='pt-0.5'>
              <SidebarGroupLabel className='text-base text-[#0C2144]'>
                Calendar
              </SidebarGroupLabel>
              <SidebarGroupContent>
                <DatePicker />
              </SidebarGroupContent>
            </SidebarGroup>
          </SidebarHeader>

          <SidebarContent className='flex-1 overflow-auto'>
            <ScrollArea>
              <SidebarGroup>
                <SidebarGroupLabel className='text-base text-[#0C2144]'>
                  {t('recent_posts')}
                </SidebarGroupLabel>
                <SidebarGroupContent className='space-y-2'>
                  <RecentPosts posts={data} />
                </SidebarGroupContent>
              </SidebarGroup>
            </ScrollArea>
          </SidebarContent>
        </div>
      </Sidebar>
    </div>
  );
}
