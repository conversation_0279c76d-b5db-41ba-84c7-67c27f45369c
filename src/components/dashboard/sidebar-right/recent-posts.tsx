import {
  format,
  parseISO,
  startOfDay,
  endOfMonth,
  startOfMonth,
} from 'date-fns';
import { useTranslations } from 'next-intl';

import { TPost } from '@/types/TPost';

import { formatDateRange } from '@/lib/format-date';

import { useDateFnsLocale } from '@/hooks/use-date-fns-locale';

import { useDateRangeStore } from '@/stores/date-range-store';

import { PostGroup } from '@/components/dashboard/sidebar-right/post-group';

interface RecentPostsProps {
  posts: TPost[];
}

export function RecentPosts({ posts }: RecentPostsProps) {
  const { dateRange, currentMonth } = useDateRangeStore();
  const dateFnsLocale = useDateFnsLocale();
  const t = useTranslations();

  const filterRange = dateRange?.from
    ? {
        from: dateRange.from,
        to: dateRange.to || dateRange.from,
      }
    : {
        from: startOfMonth(currentMonth),
        to: endOfMonth(currentMonth),
      };

  const filteredPosts = posts.filter((post) => {
    const postDate = new Date(post.createdAt);
    const postStartOfDay = startOfDay(postDate);
    const fromStartOfDay = startOfDay(filterRange.from);
    const toStartOfDay = startOfDay(filterRange.to);

    return postStartOfDay >= fromStartOfDay && postStartOfDay <= toStartOfDay;
  });

  const groupedPosts = filteredPosts.reduce<Record<string, TPost[]>>(
    (acc, post) => {
      const postDate = new Date(post.createdAt);
      const dateKey = startOfDay(postDate).toISOString();

      if (!acc[dateKey]) acc[dateKey] = [];
      acc[dateKey].push(post);
      return acc;
    },
    {}
  );

  const sortedGroups = Object.entries(groupedPosts)
    .map(([dateKey, posts]) => ({
      date: parseISO(dateKey),
      posts: posts.sort((a, b) => b.createdAt - a.createdAt),
    }))
    .sort((a, b) => b.date.getTime() - a.date.getTime());

  if (sortedGroups.length === 0) {
    const message = dateRange?.from
      ? formatDateRange(dateRange.from, dateRange.to, dateFnsLocale, t)
      : t('no_posts_in_month', {
          month: format(currentMonth, 'MMM yyyy', { locale: dateFnsLocale }),
        });

    return (
      <div className='p-4 text-center text-muted-foreground'>{message}</div>
    );
  }

  return (
    <div>
      {sortedGroups.map((group) => (
        <PostGroup
          key={group.date.toISOString()}
          date={group.date}
          posts={group.posts}
        />
      ))}
    </div>
  );
}
