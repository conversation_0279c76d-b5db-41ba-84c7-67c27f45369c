'use client';

import { useTranslations } from 'next-intl';
import React, { useMemo, useState } from 'react';
import { Area, XAxis, AreaChart, CartesianGrid } from 'recharts';

import { Twitter, Columns4, Facebook, Linkedin, Instagram } from 'lucide-react';

import { useDirection } from '@/hooks/use-direction';

import { TabItem, ExpandableTabs } from '@/components/shared/expandable-tabs';
import { Card, CardTitle, CardHeader, CardContent } from '@/components/ui/card';
import {
  ChartConfig,
  ChartTooltip,
  ChartContainer,
  ChartTooltipContent,
} from '@/components/ui/chart';

// Types
interface PlatformData {
  dataKey: keyof TData;
  color: string;
  gradientId: string;
  colorClass: string;
}

interface SocialMediaChartProps {
  data: TData[];
  title: string;
}

export interface TData {
  month: string;
  facebook: number;
  instagram: number;
  linkedin: number;
  twitter: number;
}

// Constants
const PLATFORM_IDS = ['facebook', 'instagram', 'linkedin', 'twitter'] as const;
type PlatformId = (typeof PLATFORM_IDS)[number];

const platformConfig: Record<PlatformId, PlatformData> = {
  facebook: {
    dataKey: 'facebook',
    color: 'hsl(var(--primary))',
    gradientId: 'fillFacebook',
    colorClass: 'text-primary',
  },
  instagram: {
    dataKey: 'instagram',
    color: 'hsl(var(--rose))',
    gradientId: 'fillInstagram',
    colorClass: 'text-rose',
  },
  linkedin: {
    dataKey: 'linkedin',
    color: '#0ea5e9',
    gradientId: 'fillLinkedIn',
    colorClass: 'text-sky-500',
  },
  twitter: {
    dataKey: 'twitter',
    color: 'hsl(var(--violet))',
    gradientId: 'fillTwitter',
    colorClass: 'text-violet',
  },
};

const monthNameToNumber: Record<string, number> = {
  january: 0,
  february: 1,
  march: 2,
  april: 3,
  may: 4,
  june: 5,
  july: 6,
  august: 7,
  september: 8,
  october: 9,
  november: 10,
  december: 11,
};

export default function SocialMediaChart({
  data,
  title,
}: SocialMediaChartProps) {
  const t = useTranslations('');
  const { isRtlLanguage, locale } = useDirection();
  const [activePlatform, setActivePlatform] = useState<PlatformId | 'all'>(
    'all'
  );

  // Translated content
  const { translatedTabs, chartConfig } = useMemo(
    () => ({
      translatedTabs: [
        {
          id: 'all',
          title: t('platform.all'),
          icon: <Columns4 className='size-4' />,
        },
        ...PLATFORM_IDS.map((id) => ({
          id,
          title: t(`platform.${id}`),
          icon: getPlatformIcon(id),
        })),
      ] satisfies TabItem[],

      chartConfig: PLATFORM_IDS.reduce(
        (config, id) => ({
          ...config,
          [id]: { label: t(`platform.${id}`) },
        }),
        {} as ChartConfig
      ),
    }),
    [t]
  );

  const activeTabColor = useMemo(
    () =>
      activePlatform === 'all'
        ? 'text-yellow-500'
        : platformConfig[activePlatform].colorClass,
    [activePlatform]
  );

  const visiblePlatforms = useMemo(
    () => (activePlatform === 'all' ? PLATFORM_IDS : [activePlatform]),
    [activePlatform]
  );

  const handleTabChange = (index: number | null) => {
    const platform = translatedTabs[index ?? 0]?.id;
    if (platform) setActivePlatform(platform as PlatformId | 'all');
  };

  const formatMonth = (month: string, format: 'short' | 'long') => {
    const monthName = month.toLowerCase();
    const monthNumber = monthNameToNumber[monthName];

    return new Intl.DateTimeFormat(locale, { month: format }).format(
      new Date(2027, monthNumber, 1)
    );
  };

  return (
    <Card>
      <CardHeader className='px-0 py-2 sm:px-6'>
        <CardTitle>
          <div className='flex flex-col flex-wrap items-center gap-3 sm:flex-row sm:justify-between sm:gap-2'>
            <h1 className='text-lg font-semibold capitalize text-popover-foreground'>
              {t(`social_media.${title}`)}
            </h1>
            <ExpandableTabs
              tabs={translatedTabs}
              onChange={handleTabChange}
              activeColor={activeTabColor}
            />
          </div>
        </CardTitle>
      </CardHeader>

      <CardContent className='px-0 sm:px-1 sm:py-3'>
        <ChartContainer
          config={chartConfig}
          className='aspect-[3/2] sm:aspect-[5/2]'
        >
          <AreaChart data={data} margin={{ left: 15, right: 15, top: 15 }}>
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey='month'
              tickLine={false}
              axisLine={false}
              reversed={isRtlLanguage}
              tickMargin={1}
              tickFormatter={(value) => formatMonth(value, 'short')}
            />
            <ChartTooltip
              content={
                <ChartTooltipContent
                  labelFormatter={(label) => formatMonth(label, 'long')}
                />
              }
            />
            {Object.entries(platformConfig).map(([, { color, gradientId }]) => (
              <defs key={gradientId}>
                <linearGradient id={gradientId} x1='0' y1='0' x2='0' y2='1'>
                  <stop offset='5%' stopColor={color} stopOpacity={0.8} />
                  <stop offset='95%' stopColor={color} stopOpacity={0.1} />
                </linearGradient>
              </defs>
            ))}
            {visiblePlatforms.map((platformId) => {
              const { dataKey, color } = platformConfig[platformId];
              return (
                <Area
                  key={platformId}
                  dataKey={dataKey}
                  type='natural'
                  fill={`url(#${platformConfig[platformId].gradientId})`}
                  stroke={color}
                />
              );
            })}
          </AreaChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}

// Helper function
function getPlatformIcon(id: PlatformId): React.ReactNode {
  switch (id) {
    case 'facebook':
      return <Facebook className='size-4' />;
    case 'instagram':
      return <Instagram className='size-4' />;
    case 'linkedin':
      return <Linkedin className='size-4' />;
    case 'twitter':
      return <Twitter className='size-4' />;
    default:
      return null;
  }
}
