'use client';

import { useTranslations } from 'next-intl';
import React from 'react';

import { TrendingUp, TrendingDown } from 'lucide-react';

import { formatNumber } from '@/lib/format-number';
import { cn } from '@/lib/utils';

import { useDirection } from '@/hooks/use-direction';

import { StatCardKeys, useStatCardsConfig } from '@/config/stat-cards.config';

import { Card, CardContent } from '@/components/ui/card';

export interface StatCardProps {
  title: string;
  total: number;
  trend: number;
  isPositiveTrend: boolean;
}

export function StatCard({
  title,
  total,
  trend,
  isPositiveTrend,
}: StatCardProps) {
  const { langDir } = useDirection();

  const statCardsConfig = useStatCardsConfig(useTranslations());
  const TrendingIcon =
    isPositiveTrend === (langDir === 'ltr') ? TrendingUp : TrendingDown;

  const { bgColor, icon, textColor, name } =
    statCardsConfig[title as StatCardKeys] || statCardsConfig.default;
  const Icon = icon;

  return (
    <Card>
      <CardContent className='flex flex-wrap-reverse items-center justify-between pt-6'>
        <div className='space-y-1.5'>
          <p className='text-xs font-semibold uppercase tracking-wide text-muted-foreground'>
            {name}
          </p>
          <div className='flex items-center gap-1.5'>
            <h3 className='text-xl font-medium sm:text-2xl'>
              {formatNumber(total)}
            </h3>
            <div className='flex items-center gap-0.5'>
              <TrendingIcon
                className={cn(
                  'size-4',
                  isPositiveTrend ? 'text-success' : 'text-destructive',
                  langDir === 'rtl' && 'rotate-180'
                )}
              />
              <span
                className={cn(
                  'text-sm',
                  isPositiveTrend ? 'text-success' : 'text-destructive'
                )}
              >
                {trend.toFixed(1)}%
              </span>
            </div>
          </div>
        </div>

        <div
          className={cn(
            'flex items-center justify-center rounded-lg p-3',
            bgColor,
            textColor
          )}
        >
          <Icon className='size-4' />
        </div>
      </CardContent>
    </Card>
  );
}
