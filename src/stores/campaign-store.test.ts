import { test, expect, describe, beforeEach } from 'vitest';

import {
  CampaignResponse,
  AICampaignProposal,
} from '@/api/models/dtos/compaign.dto';
import { SocialMediaMetrics } from '@/api/models/dtos/social-media-insights.dto';

import { GenerateNewPostSuggestionsSchema } from '@/api/models/schemas/creative.schema';
import { useCampaignStore } from '@/stores/campaign-store';

// Mock data for testing
const mockProposal1: AICampaignProposal = {
  Id: 'proposal-1',
  CampaignId: 'campaign-1',
  ResponseId: 'response-1',
  Name: 'Test Proposal 1',
  Description: 'Test Description 1',
  MarketingStrategy: 'Test Strategy 1',
  ContentCreaForm: {} as GenerateNewPostSuggestionsSchema,
  DateTime: new Date(),
  TimeZoneRegion: 'UTC',
  TimeZoneOffsetString: '+00:00',
  SocialMedia: 'Facebook',
  PageId: 'page-1',
  PageName: 'Test Page 1',
  PostId: 'post-1',
};

const mockProposal2: AICampaignProposal = {
  Id: 'proposal-2',
  CampaignId: 'campaign-1',
  ResponseId: 'response-1',
  Name: 'Test Proposal 2',
  Description: 'Test Description 2',
  MarketingStrategy: 'Test Strategy 2',
  ContentCreaForm: {} as GenerateNewPostSuggestionsSchema,
  DateTime: new Date(),
  TimeZoneRegion: 'UTC',
  TimeZoneOffsetString: '+00:00',
  SocialMedia: 'Instagram',
  PageId: 'page-2',
  PageName: 'Test Page 2',
  PostId: 'post-2',
};

const mockCampaignResponse: CampaignResponse = {
  Id: 'response-1',
  CompanyId: 'company-1',
  BrandId: 'brand-1',
  UserEmail: '<EMAIL>',
  CampaignType: 'ai',
  Name: 'Test Campaign',
  Description: 'Test Campaign Description',
  Status: 'Pending',
  Platforms: ['facebook', 'instagram'],
  PlateformsPageIds: null,
  Objective: 'Test Objective',
  StartDate: new Date(),
  EndDate: new Date(),
  Knowledges: [],
  Audiences: null,
  AICampaignFromId: 'ai-campaign-1',
  Proposal: [mockProposal1, mockProposal2],
  PostsId: [],
  Posts: null,
  Favorite: false,
  FbInsights: {} as SocialMediaMetrics,
  InstaInsights: {} as SocialMediaMetrics,
  LinkedinInsights: {} as SocialMediaMetrics,
  XInsights: {} as SocialMediaMetrics,
  ViewsStat: {} as CampaignResponse['ViewsStat'],
  ReachStat: {} as CampaignResponse['ReachStat'],
  InteractionStat: {} as CampaignResponse['InteractionStat'],
  LastModifTimestamp: new Date(),
  CreationTimestamp: new Date(),
};

describe('CampaignStore', () => {
  beforeEach(() => {
    // Reset store state before each test
    const store = useCampaignStore.getState();
    store.setAiCampaignResponseData(undefined);
    store.setAiCampaignProposals(null);
  });

  test('initial state should be correct', () => {
    const state = useCampaignStore.getState();
    expect(state.aiCampaignResponseData).toBeUndefined();
    expect(state.aiCampaignProposals).toBeNull();
  });

  test('insertNewAiCampaignProposal should add proposal to selected list and remove from response data', () => {
    const store = useCampaignStore.getState();

    // Set initial response data
    store.setAiCampaignResponseData([mockCampaignResponse]);

    // Insert a single proposal
    store.insertNewAiCampaignProposal(mockProposal1);

    const state = useCampaignStore.getState();

    // Check that proposal was added to selected list
    expect(state.aiCampaignProposals).toHaveLength(1);
    expect(state.aiCampaignProposals?.[0].Id).toBe('proposal-1');

    // Check that proposal was removed from response data
    expect(state.aiCampaignResponseData?.[0].Proposal).toHaveLength(1);
    expect(state.aiCampaignResponseData?.[0].Proposal?.[0].Id).toBe(
      'proposal-2'
    );
  });

  test('insertMultiAiCampaignProposals should add multiple proposals and remove from response data', () => {
    const store = useCampaignStore.getState();

    // Set initial response data
    store.setAiCampaignResponseData([mockCampaignResponse]);

    // Insert multiple proposals
    store.insertMultiAiCampaignProposals([mockProposal1, mockProposal2]);

    const state = useCampaignStore.getState();

    // Check that proposals were added to selected list
    expect(state.aiCampaignProposals).toHaveLength(2);
    expect(state.aiCampaignProposals?.map((p) => p.Id)).toEqual([
      'proposal-1',
      'proposal-2',
    ]);

    // Check that proposals were removed from response data
    expect(state.aiCampaignResponseData?.[0].Proposal).toHaveLength(0);
  });

  test('deleteAiCampaignProposal should remove proposal from selected list and add back to response data', () => {
    const store = useCampaignStore.getState();

    // Set initial state with selected proposals and empty response data
    const responseDataWithoutProposals = {
      ...mockCampaignResponse,
      Proposal: [],
    };
    store.setAiCampaignResponseData([responseDataWithoutProposals]);
    store.setAiCampaignProposals([mockProposal1, mockProposal2]);

    // Delete a proposal
    store.deleteAiCampaignProposal(mockProposal1);

    const state = useCampaignStore.getState();

    // Check that proposal was removed from selected list
    expect(state.aiCampaignProposals).toHaveLength(1);
    expect(state.aiCampaignProposals?.[0].Id).toBe('proposal-2');

    // Check that proposal was added back to response data
    expect(state.aiCampaignResponseData?.[0].Proposal).toHaveLength(1);
    expect(state.aiCampaignResponseData?.[0].Proposal?.[0].Id).toBe(
      'proposal-1'
    );
  });

  test('deleteAiCampaignProposal should set aiCampaignProposals to null when last proposal is removed', () => {
    const store = useCampaignStore.getState();

    // Set initial state with one selected proposal
    const responseDataWithoutProposals = {
      ...mockCampaignResponse,
      Proposal: [],
    };
    store.setAiCampaignResponseData([responseDataWithoutProposals]);
    store.setAiCampaignProposals([mockProposal1]);

    // Delete the last proposal
    store.deleteAiCampaignProposal(mockProposal1);

    const state = useCampaignStore.getState();

    // Check that aiCampaignProposals is set to null
    expect(state.aiCampaignProposals).toBeNull();

    // Check that proposal was added back to response data
    expect(state.aiCampaignResponseData?.[0].Proposal).toHaveLength(1);
    expect(state.aiCampaignResponseData?.[0].Proposal?.[0].Id).toBe(
      'proposal-1'
    );
  });

  test('should handle duplicate proposals correctly', () => {
    const store = useCampaignStore.getState();

    // Set initial response data
    store.setAiCampaignResponseData([mockCampaignResponse]);

    // Insert the same proposal twice
    store.insertNewAiCampaignProposal(mockProposal1);
    store.insertNewAiCampaignProposal(mockProposal1);

    const state = useCampaignStore.getState();

    // Should only have one instance of the proposal
    expect(state.aiCampaignProposals).toHaveLength(1);
    expect(state.aiCampaignProposals?.[0].Id).toBe('proposal-1');
  });

  describe('Application Integration Scenarios', () => {
    test('Generate AI campaign proposals using the form - should populate response data', () => {
      const store = useCampaignStore.getState();

      // Simulate API response after generating proposals
      const campaignResponses = [mockCampaignResponse];
      store.setAiCampaignResponseData(campaignResponses);
      store.setIsAICampaignProposalsSuccess(true);

      const state = useCampaignStore.getState();

      // Verify proposals are available in response data
      expect(state.aiCampaignResponseData).toHaveLength(1);
      expect(state.aiCampaignResponseData?.[0].Proposal).toHaveLength(2);
      expect(state.isAICampaignProposalsSuccess).toBe(true);
      expect(state.aiCampaignProposals).toBeNull(); // No proposals selected yet
    });

    test('Click "Choose" on individual proposals - should move from suggestions to selected list', () => {
      const store = useCampaignStore.getState();

      // Setup: Generate proposals first
      store.setAiCampaignResponseData([mockCampaignResponse]);

      // User clicks "Choose" on first proposal
      store.insertNewAiCampaignProposal(mockProposal1);

      let state = useCampaignStore.getState();

      // Verify proposal moved from suggestions to selected
      expect(state.aiCampaignProposals).toHaveLength(1);
      expect(state.aiCampaignProposals?.[0].Id).toBe('proposal-1');
      expect(state.aiCampaignResponseData?.[0].Proposal).toHaveLength(1);
      expect(state.aiCampaignResponseData?.[0].Proposal?.[0].Id).toBe('proposal-2');

      // User clicks "Choose" on second proposal
      store.insertNewAiCampaignProposal(mockProposal2);

      state = useCampaignStore.getState();

      // Verify both proposals are now selected and suggestions list is empty
      expect(state.aiCampaignProposals).toHaveLength(2);
      expect(state.aiCampaignResponseData?.[0].Proposal).toHaveLength(0);
    });

    test('Click "Choose all" - should move all proposals from suggestions to selected', () => {
      const store = useCampaignStore.getState();

      // Setup: Generate proposals first
      store.setAiCampaignResponseData([mockCampaignResponse]);

      // User clicks "Choose all"
      const allProposals = mockCampaignResponse.Proposal || [];
      store.insertMultiAiCampaignProposals(allProposals);

      const state = useCampaignStore.getState();

      // Verify all proposals moved to selected list
      expect(state.aiCampaignProposals).toHaveLength(2);
      expect(state.aiCampaignProposals?.map(p => p.Id)).toEqual(['proposal-1', 'proposal-2']);

      // Verify suggestions list is now empty
      expect(state.aiCampaignResponseData?.[0].Proposal).toHaveLength(0);
    });

    test('Click trash icon on selected proposals - should move back to suggestions list', () => {
      const store = useCampaignStore.getState();

      // Setup: Start with all proposals selected
      const responseDataWithoutProposals = {
        ...mockCampaignResponse,
        Proposal: [],
      };
      store.setAiCampaignResponseData([responseDataWithoutProposals]);
      store.setAiCampaignProposals([mockProposal1, mockProposal2]);

      // User clicks trash on first proposal
      store.deleteAiCampaignProposal(mockProposal1);

      let state = useCampaignStore.getState();

      // Verify proposal moved back to suggestions
      expect(state.aiCampaignProposals).toHaveLength(1);
      expect(state.aiCampaignProposals?.[0].Id).toBe('proposal-2');
      expect(state.aiCampaignResponseData?.[0].Proposal).toHaveLength(1);
      expect(state.aiCampaignResponseData?.[0].Proposal?.[0].Id).toBe('proposal-1');

      // User clicks trash on second proposal
      store.deleteAiCampaignProposal(mockProposal2);

      state = useCampaignStore.getState();

      // Verify all proposals are back in suggestions and selected list is empty
      expect(state.aiCampaignProposals).toBeNull();
      expect(state.aiCampaignResponseData?.[0].Proposal).toHaveLength(2);
    });

    test('UI updates correctly - verify state consistency throughout workflow', () => {
      const store = useCampaignStore.getState();

      // Step 1: Generate AI campaign proposals
      store.setAiCampaignResponseData([mockCampaignResponse]);
      store.setIsAICampaignProposalsSuccess(true);

      let state = useCampaignStore.getState();
      expect(state.aiCampaignResponseData?.[0].Proposal).toHaveLength(2);
      expect(state.aiCampaignProposals).toBeNull();

      // Step 2: Select one proposal
      store.insertNewAiCampaignProposal(mockProposal1);

      state = useCampaignStore.getState();
      expect(state.aiCampaignResponseData?.[0].Proposal).toHaveLength(1); // One left in suggestions
      expect(state.aiCampaignProposals).toHaveLength(1); // One in selected

      // Step 3: Select remaining proposal
      store.insertNewAiCampaignProposal(mockProposal2);

      state = useCampaignStore.getState();
      expect(state.aiCampaignResponseData?.[0].Proposal).toHaveLength(0); // None left in suggestions
      expect(state.aiCampaignProposals).toHaveLength(2); // Both in selected

      // Step 4: Remove one proposal
      store.deleteAiCampaignProposal(mockProposal1);

      state = useCampaignStore.getState();
      expect(state.aiCampaignResponseData?.[0].Proposal).toHaveLength(1); // One back in suggestions
      expect(state.aiCampaignProposals).toHaveLength(1); // One still selected

      // Step 5: Remove last proposal
      store.deleteAiCampaignProposal(mockProposal2);

      state = useCampaignStore.getState();
      expect(state.aiCampaignResponseData?.[0].Proposal).toHaveLength(2); // Both back in suggestions
      expect(state.aiCampaignProposals).toBeNull(); // None selected
    });

    test('Multiple campaigns with proposals - should handle correctly', () => {
      const store = useCampaignStore.getState();

      // Create second campaign with different proposals
      const mockProposal3: AICampaignProposal = {
        ...mockProposal1,
        Id: 'proposal-3',
        CampaignId: 'campaign-2',
        ResponseId: 'response-2',
        Name: 'Test Proposal 3',
      };

      const mockCampaignResponse2: CampaignResponse = {
        ...mockCampaignResponse,
        Id: 'response-2',
        Name: 'Test Campaign 2',
        Proposal: [mockProposal3],
      };

      // Setup: Multiple campaigns
      store.setAiCampaignResponseData([mockCampaignResponse, mockCampaignResponse2]);

      // Select proposal from first campaign
      store.insertNewAiCampaignProposal(mockProposal1);

      // Select proposal from second campaign
      store.insertNewAiCampaignProposal(mockProposal3);

      const state = useCampaignStore.getState();

      // Verify proposals from both campaigns are selected
      expect(state.aiCampaignProposals).toHaveLength(2);
      expect(state.aiCampaignProposals?.map(p => p.Id)).toEqual(['proposal-1', 'proposal-3']);

      // Verify proposals were removed from their respective campaigns
      expect(state.aiCampaignResponseData?.[0].Proposal).toHaveLength(1); // First campaign has 1 left
      expect(state.aiCampaignResponseData?.[1].Proposal).toHaveLength(0); // Second campaign empty

      // Delete proposal from first campaign
      store.deleteAiCampaignProposal(mockProposal1);

      const updatedState = useCampaignStore.getState();

      // Verify proposal went back to correct campaign
      expect(updatedState.aiCampaignResponseData?.[0].Proposal).toHaveLength(2); // Back to original
      expect(updatedState.aiCampaignResponseData?.[1].Proposal).toHaveLength(0); // Still empty
      expect(updatedState.aiCampaignProposals).toHaveLength(1); // Only proposal-3 left
    });
  });
});
