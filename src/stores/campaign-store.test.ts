import { test, expect, describe, beforeEach } from 'vitest';

import { useCampaignStore } from '@/stores/campaign-store';
import { CampaignResponse, AICampaignProposal } from '@/api/models/dtos/compaign.dto';

// Mock data for testing
const mockProposal1: AICampaignProposal = {
  Id: 'proposal-1',
  CampaignId: 'campaign-1',
  ResponseId: 'response-1',
  Name: 'Test Proposal 1',
  Description: 'Test Description 1',
  MarketingStrategy: 'Test Strategy 1',
  ContentCreaForm: {} as any,
  DateTime: new Date(),
  TimeZoneRegion: 'UTC',
  TimeZoneOffsetString: '+00:00',
  SocialMedia: 'Facebook',
  PageId: 'page-1',
  PageName: 'Test Page 1',
  PostId: 'post-1',
};

const mockProposal2: AICampaignProposal = {
  Id: 'proposal-2',
  CampaignId: 'campaign-1',
  ResponseId: 'response-1',
  Name: 'Test Proposal 2',
  Description: 'Test Description 2',
  MarketingStrategy: 'Test Strategy 2',
  ContentCreaForm: {} as any,
  DateTime: new Date(),
  TimeZoneRegion: 'UTC',
  TimeZoneOffsetString: '+00:00',
  SocialMedia: 'Instagram',
  PageId: 'page-2',
  PageName: 'Test Page 2',
  PostId: 'post-2',
};

const mockCampaignResponse: CampaignResponse = {
  Id: 'response-1',
  CompanyId: 'company-1',
  BrandId: 'brand-1',
  UserEmail: '<EMAIL>',
  CampaignType: 'ai',
  Name: 'Test Campaign',
  Description: 'Test Campaign Description',
  Status: 'Pending',
  Platforms: ['facebook', 'instagram'],
  PlateformsPageIds: null,
  Objective: 'Test Objective',
  StartDate: new Date(),
  EndDate: new Date(),
  Knowledges: [],
  Audiences: null,
  AICampaignFromId: 'ai-campaign-1',
  Proposal: [mockProposal1, mockProposal2],
  PostsId: [],
  Posts: null,
  Favorite: false,
  FbInsights: {} as any,
  InstaInsights: {} as any,
  LinkedinInsights: {} as any,
  XInsights: {} as any,
  ViewsStat: {} as any,
  ReachStat: {} as any,
  InteractionStat: {} as any,
  LastModifTimestamp: new Date(),
  CreationTimestamp: new Date(),
};

describe('CampaignStore', () => {
  beforeEach(() => {
    // Reset store state before each test
    const store = useCampaignStore.getState();
    store.setAiCampaignResponseData(undefined);
    store.setAiCampaignProposals(null);
  });

  test('initial state should be correct', () => {
    const state = useCampaignStore.getState();
    expect(state.aiCampaignResponseData).toBeUndefined();
    expect(state.aiCampaignProposals).toBeNull();
  });

  test('insertNewAiCampaignProposal should add proposal to selected list and remove from response data', () => {
    const store = useCampaignStore.getState();
    
    // Set initial response data
    store.setAiCampaignResponseData([mockCampaignResponse]);
    
    // Insert a single proposal
    store.insertNewAiCampaignProposal(mockProposal1);
    
    const state = useCampaignStore.getState();
    
    // Check that proposal was added to selected list
    expect(state.aiCampaignProposals).toHaveLength(1);
    expect(state.aiCampaignProposals?.[0].Id).toBe('proposal-1');
    
    // Check that proposal was removed from response data
    expect(state.aiCampaignResponseData?.[0].Proposal).toHaveLength(1);
    expect(state.aiCampaignResponseData?.[0].Proposal?.[0].Id).toBe('proposal-2');
  });

  test('insertMultiAiCampaignProposals should add multiple proposals and remove from response data', () => {
    const store = useCampaignStore.getState();
    
    // Set initial response data
    store.setAiCampaignResponseData([mockCampaignResponse]);
    
    // Insert multiple proposals
    store.insertMultiAiCampaignProposals([mockProposal1, mockProposal2]);
    
    const state = useCampaignStore.getState();
    
    // Check that proposals were added to selected list
    expect(state.aiCampaignProposals).toHaveLength(2);
    expect(state.aiCampaignProposals?.map(p => p.Id)).toEqual(['proposal-1', 'proposal-2']);
    
    // Check that proposals were removed from response data
    expect(state.aiCampaignResponseData?.[0].Proposal).toHaveLength(0);
  });

  test('deleteAiCampaignProposal should remove proposal from selected list and add back to response data', () => {
    const store = useCampaignStore.getState();
    
    // Set initial state with selected proposals and empty response data
    const responseDataWithoutProposals = {
      ...mockCampaignResponse,
      Proposal: [],
    };
    store.setAiCampaignResponseData([responseDataWithoutProposals]);
    store.setAiCampaignProposals([mockProposal1, mockProposal2]);
    
    // Delete a proposal
    store.deleteAiCampaignProposal(mockProposal1);
    
    const state = useCampaignStore.getState();
    
    // Check that proposal was removed from selected list
    expect(state.aiCampaignProposals).toHaveLength(1);
    expect(state.aiCampaignProposals?.[0].Id).toBe('proposal-2');
    
    // Check that proposal was added back to response data
    expect(state.aiCampaignResponseData?.[0].Proposal).toHaveLength(1);
    expect(state.aiCampaignResponseData?.[0].Proposal?.[0].Id).toBe('proposal-1');
  });

  test('deleteAiCampaignProposal should set aiCampaignProposals to null when last proposal is removed', () => {
    const store = useCampaignStore.getState();
    
    // Set initial state with one selected proposal
    const responseDataWithoutProposals = {
      ...mockCampaignResponse,
      Proposal: [],
    };
    store.setAiCampaignResponseData([responseDataWithoutProposals]);
    store.setAiCampaignProposals([mockProposal1]);
    
    // Delete the last proposal
    store.deleteAiCampaignProposal(mockProposal1);
    
    const state = useCampaignStore.getState();
    
    // Check that aiCampaignProposals is set to null
    expect(state.aiCampaignProposals).toBeNull();
    
    // Check that proposal was added back to response data
    expect(state.aiCampaignResponseData?.[0].Proposal).toHaveLength(1);
    expect(state.aiCampaignResponseData?.[0].Proposal?.[0].Id).toBe('proposal-1');
  });

  test('should handle duplicate proposals correctly', () => {
    const store = useCampaignStore.getState();
    
    // Set initial response data
    store.setAiCampaignResponseData([mockCampaignResponse]);
    
    // Insert the same proposal twice
    store.insertNewAiCampaignProposal(mockProposal1);
    store.insertNewAiCampaignProposal(mockProposal1);
    
    const state = useCampaignStore.getState();
    
    // Should only have one instance of the proposal
    expect(state.aiCampaignProposals).toHaveLength(1);
    expect(state.aiCampaignProposals?.[0].Id).toBe('proposal-1');
  });
});
