'use client';

import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';

import { AuthResponse } from '@/api/models/dtos/auth.dto';

export type StoredUserInfos = Partial<
  Omit<AuthResponse, 'company_InfosSet' | 'message' | 'token'>
>;

interface UserStore {
  user: StoredUserInfos | null;
  setUser: (updateStoredUserSchema: Partial<StoredUserInfos>) => void;
  clearUser: () => void;
  getUser: () => Omit<
    AuthResponse,
    'company_InfosSet' | 'message' | 'token'
  > | null;
}

export const useCurrentUserStore = create<UserStore>()(
  persist(
    (set, get) => ({
      user: null,

      setUser: (updateStoredUserSchema) => {
        set({ user: updateStoredUserSchema });
      },

      clearUser: () => {
        set({ user: null });
      },

      getUser: () => {
        if (typeof window !== 'undefined') {
          const stored = localStorage.getItem('current-user');

          if (stored) {
            try {
              const parsed = JSON.parse(stored);
              return parsed.state?.user;
            } catch (e) {
              console.error('Failed to parse user from localStorage:', e);
            }
          }
          return get().user;
        }
      },
    }),
    {
      name: 'current-user',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({ user: state.user }),
    }
  )
);
