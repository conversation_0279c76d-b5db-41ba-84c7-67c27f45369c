'use client';

import { create } from 'zustand';

import { PrepareTextModelSchema } from '@/api/models/schemas/private-model.schema';

interface PrivateModelTextStore {
  textFormValues: PrepareTextModelSchema | null;
  setTextFormValues: (update: PrepareTextModelSchema | null) => void;
  getTextFormValues: () => PrepareTextModelSchema | null;
  clearTextFormValues: () => void;
}

export const usePrivateModelTextStore = create<PrivateModelTextStore>()(
  (set, get) => ({
    textFormValues: null,

    setTextFormValues: (update) => {
      set({ textFormValues: update });
    },

    clearTextFormValues: () => {
      set({ textFormValues: null });
    },

    getTextFormValues: () => {
      return get().textFormValues;
    },
  })
);
