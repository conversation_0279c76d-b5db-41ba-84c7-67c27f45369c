import { create } from 'zustand';

import { PrepareUrlContentKnowledgeResponse } from '@/api/models/dtos/knowledge.dto';

interface TWebsiteScrapedResults extends PrepareUrlContentKnowledgeResponse {
  url: string;
  title: string;
  isLoading: boolean;
}

interface KnowledgeScrapedWebsiteStore {
  showResults: boolean;
  toggleShowResults: () => void;
  setShowResults: (show: boolean) => void;
  getShowResults: () => boolean;

  websiteScrapedResults: TWebsiteScrapedResults | null;
  setWebsiteScrapedResults: (update: TWebsiteScrapedResults | null) => void;
  getWebsiteScrapedResults: () => TWebsiteScrapedResults | null;
  clearWebsiteScrapedResults: () => void;
}

export const useKnowledgeScrapedWebsiteStore =
  create<KnowledgeScrapedWebsiteStore>()((set, get) => ({
    showResults: false,
    toggleShowResults: () => {
      set({ showResults: !get().showResults });
    },
    setShowResults: (show) => {
      set({ showResults: show });
    },
    getShowResults: () => {
      return get().showResults;
    },

    websiteScrapedResults: null,
    setWebsiteScrapedResults: (update) => {
      set({ websiteScrapedResults: update });
    },
    getWebsiteScrapedResults: () => {
      return get().websiteScrapedResults;
    },
    clearWebsiteScrapedResults: () => {
      set({ websiteScrapedResults: null });
    },
  }));
