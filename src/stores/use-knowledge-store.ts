import { create } from 'zustand';

import { KnowledgeResponse } from '@/api/models/dtos/knowledge.dto';

interface KnowledgeStore {
  isDetailOpen: boolean;
  isUpdating: boolean;
  knowledge: KnowledgeResponse | null;

  setDetailOpen: (open: boolean) => void;
  setIsUpdating: (update: boolean) => void;
  setKnowledgeItem: (knowledgeItem: KnowledgeResponse) => void;
}

export const useKnowledgeStore = create<KnowledgeStore>((set) => ({
  isDetailOpen: false,
  isUpdating: false,
  knowledge: null,

  setDetailOpen: (open: boolean) => {
    set({ isDetailOpen: open });
  },
  setIsUpdating: (update: boolean) => {
    set({ isUpdating: update });
  },
  setKnowledgeItem: (knowledgeItem: KnowledgeResponse) => {
    set({ knowledge: knowledgeItem });
  },
}));
