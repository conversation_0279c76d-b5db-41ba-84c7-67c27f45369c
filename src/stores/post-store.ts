import { create } from 'zustand';

interface TPosts {
  previous: string;
  current: string;
  next: string;
}

interface PostStore {
  openPostDetail: boolean;
  setOpenPostDetail: (open: boolean) => void;
  posts: TPosts | null;
  setPosts: (posts: TPosts) => void;
  clearSelectedPost: () => void;

  goToPrevious: () => void;
  goToNext: () => void;
  updateNavigation: (
    allPosts: Array<{ Id: string }>,
    currentId: string
  ) => void;
}

export const usePostStore = create<PostStore>((set, get) => ({
  openPostDetail: false,
  setOpenPostDetail: (open) => {
    set({ openPostDetail: open });
  },

  posts: null,
  setPosts: (posts) => {
    set({ posts });
  },

  clearSelectedPost: () => {
    set({ posts: null });
  },

  goToPrevious: () => {
    const { posts } = get();
    if (posts?.previous) {
      set({
        posts: {
          ...posts,
          current: posts.previous,
        },
      });
    }
  },

  goToNext: () => {
    const { posts } = get();
    if (posts?.next) {
      set({
        posts: {
          ...posts,
          current: posts.next,
        },
      });
    }
  },

  updateNavigation: (allPosts, currentId) => {
    const currentIndex = allPosts.findIndex((post) => post.Id === currentId);
    const previous =
      currentIndex > 0 ? allPosts[currentIndex - 1]?.Id || '' : '';
    const next =
      currentIndex < allPosts.length - 1
        ? allPosts[currentIndex + 1]?.Id || ''
        : '';

    set((state) => ({
      posts: state.posts
        ? {
            ...state.posts,
            previous,
            next,
          }
        : null,
    }));
  },
}));
