import { create } from 'zustand';

import {
  PostSuggestion,
  PostsSuggestionsResponse,
} from '@/api/models/dtos/post.dto';

interface CreativePostStore {
  selectedIndex: number;
  postSuggestions: PostsSuggestionsResponse | null;
  currentPostSuggestion: PostSuggestion | null;
  editPostSuggestion: PostSuggestion | null;
  isGeneratingNewPostSuggestions: boolean;
  previousPage: '/dashboard/creative' | '/dashboard/posts';

  setSelectedIndex: (index: number) => void;
  setPostSuggestions: (update: PostsSuggestionsResponse | null) => void;
  setCurrentPostSuggestion: (update: PostSuggestion | null) => void;
  setEditPostSuggestion: (update: PostSuggestion | null) => void;
  setIsGeneratingNewPostSuggestions: (update: boolean) => void;
  setPreviousPage: (update: '/dashboard/creative' | '/dashboard/posts') => void;

  getSelectedIndex: () => number;
  getPostSuggestions: () => PostsSuggestionsResponse | null;
  getCurrentPostSuggestion: () => PostSuggestion | null;
  getEditPostSuggestion: () => PostSuggestion | null;
  getIsGeneratingNewPostSuggestions: () => boolean;

  resetSelectedIndex: () => void;
  clearPostSuggestions: () => void;
  clearCurrentPostSuggestion: () => void;
  clearEditPostSuggestion: () => void;
  clearIsGeneratingNewPostSuggestions: () => void;
}

export const useCreativePostStore = create<CreativePostStore>((set, get) => ({
  selectedIndex: 1,
  currentPostSuggestion: null,
  postSuggestions: null,
  editPostSuggestion: null,
  isGeneratingNewPostSuggestions: false,
  previousPage: '/dashboard/creative',

  setSelectedIndex: (index) => {
    set({ selectedIndex: index });
  },
  getSelectedIndex: () => {
    return get().selectedIndex;
  },
  resetSelectedIndex: () => {
    set({ selectedIndex: 1 });
  },

  setCurrentPostSuggestion: (update) => {
    set({ currentPostSuggestion: update });
  },
  getCurrentPostSuggestion: () => {
    return get().currentPostSuggestion;
  },
  clearCurrentPostSuggestion: () => {
    set({ currentPostSuggestion: null });
  },

  setPostSuggestions: (update) => {
    set({ postSuggestions: update });
  },
  getPostSuggestions: () => {
    return get().postSuggestions;
  },
  clearPostSuggestions: () => {
    set({ postSuggestions: null });
  },

  setEditPostSuggestion: (update) => {
    set({ editPostSuggestion: update });
  },
  getEditPostSuggestion: () => {
    return get().editPostSuggestion;
  },
  clearEditPostSuggestion: () => {
    set({ editPostSuggestion: null });
  },

  setIsGeneratingNewPostSuggestions: (update) => {
    set({ isGeneratingNewPostSuggestions: update });
  },
  getIsGeneratingNewPostSuggestions: () => {
    return get().isGeneratingNewPostSuggestions;
  },
  clearIsGeneratingNewPostSuggestions: () => {
    set({ isGeneratingNewPostSuggestions: false });
  },

  setPreviousPage: (update) => {
    set({ previousPage: update });
  },
}));
