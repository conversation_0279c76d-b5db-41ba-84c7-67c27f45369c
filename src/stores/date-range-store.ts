import { startOfMonth } from 'date-fns';
import { DateRange } from 'react-day-picker';
import { create } from 'zustand';

type DateRangeState = {
  dateRange?: DateRange;
  currentMonth: Date;
  setDateRange: (range?: DateRange) => void;
  setCurrentMonth: (month: Date) => void;
};

export const useDateRangeStore = create<DateRangeState>((set) => ({
  dateRange: undefined,
  currentMonth: startOfMonth(new Date()),
  setDateRange: (range) => set({ dateRange: range }),
  setCurrentMonth: (month) => set({ currentMonth: startOfMonth(month) }),
}));
