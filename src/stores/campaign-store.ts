import { create } from 'zustand';

import {
  CampaignResponse,
  AICampaignProposal,
} from '@/api/models/dtos/compaign.dto';

interface CampaignStore {
  isAICampaignCardOpen: boolean;
  setIsAICampaignCardOpen: (isAICampaign: boolean) => void;

  isAICampaignProposalsSuccess: boolean;
  setIsAICampaignProposalsSuccess: (isSuccess: boolean) => void;

  aiCampaignResponseData: CampaignResponse[] | undefined;
  setAiCampaignResponseData: (data: CampaignResponse[] | undefined) => void;

  aiCampaignProposals: AICampaignProposal[] | null;
  setAiCampaignProposals: (proposals: AICampaignProposal[] | null) => void;
  insertMultiAiCampaignProposals: (proposals: AICampaignProposal[]) => void;
  insertNewAiCampaignProposal: (proposal: AICampaignProposal) => void;
  deleteAiCampaignProposal: (proposal: AICampaignProposal) => void;
}

export const useCampaignStore = create<CampaignStore>((set, get) => ({
  isAICampaignCardOpen: false,
  setIsAICampaignCardOpen: (state) => {
    set({ isAICampaignCardOpen: state });
  },

  isAICampaignProposalsSuccess: false,
  setIsAICampaignProposalsSuccess: (state) => {
    set({ isAICampaignProposalsSuccess: state });
  },

  aiCampaignResponseData: undefined,
  setAiCampaignResponseData: (data) => {
    set({ aiCampaignResponseData: data });
  },

  aiCampaignProposals: null,
  insertMultiAiCampaignProposals: (proposals) => {
    const { aiCampaignProposals, aiCampaignResponseData } = get();

    const existing = aiCampaignProposals || [];

    const incomingIds = new Set(proposals.map((p) => p.Id));
    const filteredExisting = existing.filter((p) => !incomingIds.has(p.Id));

    const updatedProposals = [...filteredExisting, ...proposals];

    // Remove the selected proposals from the response data
    const updatedResponseData = aiCampaignResponseData?.map((campaign) => ({
      ...campaign,
      Proposal:
        campaign.Proposal?.filter((p) => !incomingIds.has(p.Id)) || null,
    }));

    set({
      aiCampaignProposals: updatedProposals,
      aiCampaignResponseData: updatedResponseData,
    });
  },
  insertNewAiCampaignProposal: (proposal) => {
    const { aiCampaignProposals, aiCampaignResponseData } = get();

    const filteredProposals = (aiCampaignProposals || []).filter(
      (p) => p.Id !== proposal.Id
    );

    const updatedProposals = [...filteredProposals, proposal];

    // Remove the selected proposal from the response data
    const updatedResponseData = aiCampaignResponseData?.map((campaign) => ({
      ...campaign,
      Proposal: campaign.Proposal?.filter((p) => p.Id !== proposal.Id) || null,
    }));

    set({
      aiCampaignProposals: updatedProposals,
      aiCampaignResponseData: updatedResponseData,
    });
  },
  deleteAiCampaignProposal: (proposal) => {
    const { aiCampaignProposals, aiCampaignResponseData } = get();

    const updatedProposals = (aiCampaignProposals || []).filter(
      (p) => p.Id !== proposal.Id
    );

    // Add the proposal back to the response data
    const updatedResponseData = aiCampaignResponseData?.map((campaign) => {
      // Find the campaign that this proposal belongs to based on CampaignId or ResponseId
      if (
        campaign.Id === proposal.CampaignId ||
        campaign.Id === proposal.ResponseId
      ) {
        return {
          ...campaign,
          Proposal: [...(campaign.Proposal || []), proposal],
        };
      }
      return campaign;
    });

    set({
      aiCampaignProposals:
        updatedProposals.length > 0 ? updatedProposals : null,
      aiCampaignResponseData: updatedResponseData,
    });
  },
  setAiCampaignProposals: (proposals) => {
    set({ aiCampaignProposals: proposals });
  },
}));
