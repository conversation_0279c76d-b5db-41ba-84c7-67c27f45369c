import { create } from 'zustand';

import {
  CampaignResponse,
  AICampaignProposal,
} from '@/api/models/dtos/compaign.dto';

interface CampaignStore {
  // UI State
  isAICampaignCardOpen: boolean;
  setIsAICampaignCardOpen: (isAICampaign: boolean) => void;

  isAICampaignProposalsSuccess: boolean;
  setIsAICampaignProposalsSuccess: (isSuccess: boolean) => void;

  // Data State
  aiCampaignResponseData: CampaignResponse[] | undefined;
  setAiCampaignResponseData: (data: CampaignResponse[] | undefined) => void;

  aiCampaignProposals: AICampaignProposal[] | null;
  setAiCampaignProposals: (proposals: AICampaignProposal[] | null) => void;

  // Proposal Management Actions
  selectProposal: (proposal: AICampaignProposal) => void;
  selectMultipleProposals: (proposals: AICampaignProposal[]) => void;
  unselectProposal: (proposal: AICampaignProposal) => void;
  removeProposalFromSuggestions: (proposal: AICampaignProposal) => void;

  // Utility Actions
  resetCampaignFlow: () => void;
  getSelectedProposalsCount: () => number;
  getSuggestionsCount: () => number;
}

export const useCampaignStore = create<CampaignStore>((set, get) => ({
  // UI State
  isAICampaignCardOpen: false,
  setIsAICampaignCardOpen: (state) => {
    set({ isAICampaignCardOpen: state });
  },

  isAICampaignProposalsSuccess: false,
  setIsAICampaignProposalsSuccess: (state) => {
    set({ isAICampaignProposalsSuccess: state });
  },

  // Data State
  aiCampaignResponseData: undefined,
  setAiCampaignResponseData: (data) => {
    set({ aiCampaignResponseData: data });
  },

  aiCampaignProposals: null,
  setAiCampaignProposals: (proposals) => {
    set({ aiCampaignProposals: proposals });
  },

  // New Proposal Management Actions
  selectProposal: (proposal) => {
    const { aiCampaignProposals, aiCampaignResponseData } = get();

    // Add to selected proposals (avoid duplicates)
    const existing = aiCampaignProposals || [];
    const isAlreadySelected = existing.some((p) => p.Id === proposal.Id);

    if (isAlreadySelected) {
      return; // Already selected, do nothing
    }

    const updatedProposals = [...existing, proposal];

    // Remove from suggestions
    const updatedResponseData = aiCampaignResponseData?.map((campaign) => ({
      ...campaign,
      Proposal: campaign.Proposal?.filter((p) => p.Id !== proposal.Id) || null,
    }));

    set({
      aiCampaignProposals: updatedProposals,
      aiCampaignResponseData: updatedResponseData,
    });
  },

  selectMultipleProposals: (proposals) => {
    const { aiCampaignProposals, aiCampaignResponseData } = get();

    const existing = aiCampaignProposals || [];
    const incomingIds = new Set(proposals.map((p) => p.Id));

    // Filter out already selected proposals
    const newProposals = proposals.filter(
      (p) => !existing.some((existing) => existing.Id === p.Id)
    );

    const updatedProposals = [...existing, ...newProposals];

    // Remove selected proposals from suggestions
    const updatedResponseData = aiCampaignResponseData?.map((campaign) => ({
      ...campaign,
      Proposal:
        campaign.Proposal?.filter((p) => !incomingIds.has(p.Id)) || null,
    }));

    set({
      aiCampaignProposals: updatedProposals,
      aiCampaignResponseData: updatedResponseData,
    });
  },

  unselectProposal: (proposal) => {
    const { aiCampaignProposals, aiCampaignResponseData } = get();

    // Remove from selected proposals
    const updatedProposals = (aiCampaignProposals || []).filter(
      (p) => p.Id !== proposal.Id
    );

    // Add back to appropriate campaign in suggestions
    const updatedResponseData = aiCampaignResponseData?.map((campaign) => {
      // Find the campaign this proposal belongs to
      if (
        campaign.Id === proposal.CampaignId ||
        campaign.Id === proposal.ResponseId
      ) {
        // Check if proposal is already in suggestions (avoid duplicates)
        const isAlreadyInSuggestions = campaign.Proposal?.some(
          (p) => p.Id === proposal.Id
        );

        if (!isAlreadyInSuggestions) {
          return {
            ...campaign,
            Proposal: [...(campaign.Proposal || []), proposal],
          };
        }
      }
      return campaign;
    });

    set({
      aiCampaignProposals:
        updatedProposals.length > 0 ? updatedProposals : null,
      aiCampaignResponseData: updatedResponseData,
    });
  },

  removeProposalFromSuggestions: (proposal) => {
    const { aiCampaignResponseData } = get();

    // Remove proposal from suggestions without adding to selected
    const updatedResponseData = aiCampaignResponseData?.map((campaign) => ({
      ...campaign,
      Proposal: campaign.Proposal?.filter((p) => p.Id !== proposal.Id) || null,
    }));

    set({
      aiCampaignResponseData: updatedResponseData,
    });
  },

  // Utility Actions
  resetCampaignFlow: () => {
    set({
      isAICampaignCardOpen: false,
      isAICampaignProposalsSuccess: false,
      aiCampaignResponseData: undefined,
      aiCampaignProposals: null,
    });
  },

  getSelectedProposalsCount: () => {
    const { aiCampaignProposals } = get();
    return aiCampaignProposals?.length || 0;
  },

  getSuggestionsCount: () => {
    const { aiCampaignResponseData } = get();
    return (
      aiCampaignResponseData?.reduce(
        (total, campaign) => total + (campaign.Proposal?.length || 0),
        0
      ) || 0
    );
  },
}));
