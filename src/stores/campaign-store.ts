import { create } from 'zustand';

import {
  CampaignResponse,
  AICampaignProposal,
} from '@/api/models/dtos/compaign.dto';

interface CampaignStore {
  isAICampaignCardOpen: boolean;
  setIsAICampaignCardOpen: (isAICampaign: boolean) => void;

  isAICampaignProposalsSuccess: boolean;
  setIsAICampaignProposalsSuccess: (isSuccess: boolean) => void;

  aiCampaignResponseData: CampaignResponse[] | undefined;
  setAiCampaignResponseData: (data: CampaignResponse[] | undefined) => void;

  aiCampaignProposals: AICampaignProposal[] | null;
  setAiCampaignProposals: (proposals: AICampaignProposal[] | null) => void;
  insertMultiAiCampaignProposals: (proposals: AICampaignProposal[]) => void;
  insertNewAiCampaignProposal: (proposal: AICampaignProposal) => void;
  deleteAiCampaignProposal: (proposal: AICampaignProposal) => void;
}

export const useCampaignStore = create<CampaignStore>((set, get) => ({
  isAICampaignCardOpen: false,
  setIsAICampaignCardOpen: (state) => {
    set({ isAICampaignCardOpen: state });
  },

  isAICampaignProposalsSuccess: false,
  setIsAICampaignProposalsSuccess: (state) => {
    set({ isAICampaignProposalsSuccess: state });
  },

  aiCampaignResponseData: undefined,
  setAiCampaignResponseData: (data) => {
    set({ aiCampaignResponseData: data });
  },

  aiCampaignProposals: null,
  insertMultiAiCampaignProposals: (proposals) => {
    const { aiCampaignProposals } = get();

    const existing = aiCampaignProposals || [];

    const incomingIds = new Set(proposals.map((p) => p.Id));
    const filteredExisting = existing.filter((p) => !incomingIds.has(p.Id));

    const updatedProposals = [...filteredExisting, ...proposals];

    set({ aiCampaignProposals: updatedProposals });
  },
  insertNewAiCampaignProposal: (proposal) => {
    const { aiCampaignProposals } = get();

    const filteredProposals = (aiCampaignProposals || []).filter(
      (p) => p.Id !== proposal.Id
    );

    const updatedProposals = [...filteredProposals, proposal];

    set({ aiCampaignProposals: updatedProposals });
  },
  deleteAiCampaignProposal: (proposal) => {
    const { aiCampaignProposals } = get();

    const updatedProposals = (aiCampaignProposals || []).filter(
      (p) => p.Id !== proposal.Id
    );

    set({ aiCampaignProposals: updatedProposals || null });
  },
  setAiCampaignProposals: (proposals) => {
    set({ aiCampaignProposals: proposals });
  },
}));
