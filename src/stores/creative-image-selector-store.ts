import { create } from 'zustand';

import { imageStyles } from '@/config/creative-post/options-config';
import { imageTypes } from '@/config/creative-post/options-config';
import { imageEffects } from '@/config/creative-post/options-config';

export type ImageContent = {
  id: string;
  title: string;
  src: string;
};

export interface ImageSelectorConfig<C extends string> {
  storeKey: 'type' | 'style' | 'effect';
  availableItems: {
    category: C;
    content: ImageContent[];
  }[];
  multiSelect?: boolean;
}

const createImageSelectorStore = <C extends string>({
  storeKey,
  availableItems,
  multiSelect = false,
}: ImageSelectorConfig<C>) => {
  interface ImageSelectorState {
    availableItems: {
      category: C;
      content: ImageContent[];
    }[];
    selectedItem: ImageContent | null;
    selectedItems: { [category: string]: ImageContent };
    isSelectorOpen: boolean;
    selectItem: (item: ImageContent, category: string) => void;
    isItemSelected: (itemId: string) => boolean;
    toggleSelector: () => void;
    closeSelector: () => void;
    clearSelection: () => void;
    getSelectedItemsArray: () => { category: string; item: ImageContent }[];
  }

  return create<ImageSelectorState>((set, get) => ({
    storeKey,
    availableItems,
    selectedItem: null,
    selectedItems: {},
    isSelectorOpen: false,
    selectItem: (item, category) => {
      if (multiSelect) {
        set((state) => {
          const currentSelectedItems = state.selectedItems;

          const isSameItem = currentSelectedItems[category]?.id === item.id;

          const newSelectedItems = Object.fromEntries(
            Object.entries(currentSelectedItems).filter(
              ([key]) => !(isSameItem && key === category)
            )
          );

          if (!isSameItem) {
            newSelectedItems[category] = item;
          }

          return {
            selectedItems: newSelectedItems,
            selectedItem: item,
          };
        });
      } else {
        set({ selectedItem: item, isSelectorOpen: false });
      }
    },
    isItemSelected: (itemId) => {
      if (multiSelect) {
        const selectedItems = get().selectedItems;
        return Object.values(selectedItems).some((item) => item.id === itemId);
      }
      return get().selectedItem?.id === itemId;
    },
    toggleSelector: () =>
      set((state) => ({ isSelectorOpen: !state.isSelectorOpen })),
    closeSelector: () => set({ isSelectorOpen: false }),
    clearSelection: () => set({ selectedItem: null, selectedItems: {} }),
    getSelectedItemsArray: () => {
      const selectedItems = get().selectedItems;
      return Object.entries(selectedItems).map(([category, item]) => ({
        category,
        item,
      }));
    },
  }));
};

export const useImageTypeStore = createImageSelectorStore({
  storeKey: 'type',
  availableItems: imageTypes,
});

export const useImageStyleStore = createImageSelectorStore({
  storeKey: 'style',
  availableItems: imageStyles,
});

export const useImageEffectsStore = createImageSelectorStore({
  storeKey: 'effect',
  availableItems: imageEffects,
  multiSelect: true,
});

export const useImageSelector = (selectorType: 'type' | 'style' | 'effect') => {
  switch (selectorType) {
    case 'type':
      return useImageTypeStore;
    case 'style':
      return useImageStyleStore;
    case 'effect':
      return useImageEffectsStore;
  }
};
