export function toBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.readAsDataURL(file);

    reader.onloadend = () => {
      if (reader.error) {
        reject(reader.error);
        return;
      }

      if (reader.result === null) {
        reject(new Error('File Reader result is null'));
        return;
      }

      try {
        const result = reader.result as string;
        const base64 = result.split(',')[1];
        resolve(base64);
      } catch (err) {
        reject(err);
      }
    };

    reader.onabort = () => {
      reject(new Error('File reading was aborted'));
    };
  });
}
