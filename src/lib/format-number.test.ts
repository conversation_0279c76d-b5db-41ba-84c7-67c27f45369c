import { it, expect, describe } from 'vitest';

import { formatNumber } from '@/lib/format-number';

describe('formatNumber', () => {
  it('formats numbers in millions correctly', () => {
    expect(formatNumber(1000000)).toBe('1M');
    expect(formatNumber(2500000)).toBe('2.5M');
    expect(formatNumber(9999999)).toBe('9.9M');
  });

  it('formats numbers in thousands correctly', () => {
    expect(formatNumber(1000)).toBe('1K');
    expect(formatNumber(2500)).toBe('2.5K');
    expect(formatNumber(9999)).toBe('9.9K');
  });

  it('returns numbers below 1,000 as is', () => {
    expect(formatNumber(999)).toBe('999');
    expect(formatNumber(100)).toBe('100');
    expect(formatNumber(5)).toBe('5');
    expect(formatNumber(0)).toBe('0');
  });
});
