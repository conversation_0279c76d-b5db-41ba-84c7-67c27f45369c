import { format } from 'date-fns';
import { enUS } from 'date-fns/locale';
import { vi, test, expect, describe } from 'vitest';

import { formatDateRange, formatGroupTime } from '@/lib/format-date';

describe('formatGroupTime', () => {
  test('returns relative time if within the same day and less than 1 hour ago', () => {
    const now = Date.now();
    vi.setSystemTime(now);

    const pastTime = now - 30 * 60 * 1000;
    expect(formatGroupTime(pastTime, true, enUS)).toBe('30 minutes ago');

    vi.restoreAllMocks();
  });

  test('returns formatted time if exactly 1 hour ago', () => {
    const now = new Date();
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);

    expect(formatGroupTime(oneHourAgo.getTime(), true, enUS)).toBe(
      format(oneHourAgo, 'HH:mm', { locale: enUS })
    );
  });

  test('returns formatted time if not today', () => {
    const pastDate = new Date('2024-01-01T12:00:00Z');
    expect(formatGroupTime(pastDate.getTime(), false, enUS)).toBe(
      format(pastDate, 'HH:mm', { locale: enUS })
    );
  });
});

describe('formatDateRange', () => {
  test('returns message when no posts were created on the same day', () => {
    const date = new Date('2024-02-20');
    expect(formatDateRange(date)).toBe('No post was created on Feb 20, 2024');
  });

  test('returns message when no posts were created in a date range', () => {
    const from = new Date('2024-02-01');
    const to = new Date('2024-02-10');

    expect(formatDateRange(from, to)).toBe(
      'No posts created between Feb 01, 2024 and Feb 10, 2024'
    );
  });
});
