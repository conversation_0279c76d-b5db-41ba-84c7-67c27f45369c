import type { FilterFn } from '@tanstack/react-table';

import { endOfDay, startOfDay, isWithinInterval } from 'date-fns';

export function filterByDate<T>(): FilterFn<T> {
  return function (
    row,
    columnId,
    filterValue: { from?: Date; to?: Date }
  ): boolean {
    if (!filterValue) return true;

    const cellDate = new Date(row.getValue(columnId));

    if (filterValue.from && filterValue.to) {
      return isWithinInterval(cellDate, {
        start: startOfDay(filterValue.from),
        end: endOfDay(filterValue.to),
      });
    }

    if (filterValue.from) {
      return isWithinInterval(cellDate, {
        start: startOfDay(filterValue.from),
        end: endOfDay(filterValue.from),
      });
    }

    return true;
  };
}
