import { format, Locale, formatDistanceToNowStrict } from 'date-fns';
import { useTranslations } from 'next-intl';

export function formatGroupTime(
  createdAt: number,
  isTodayGroup: boolean,
  locale: Locale
) {
  const date = new Date(createdAt);

  if (isTodayGroup) {
    const diffInMs = Date.now() - date.getTime();
    const diffInHours = diffInMs / (1000 * 60 * 60);

    if (diffInHours < 1 || diffInHours > 1) {
      return formatDistanceToNowStrict(date, {
        addSuffix: true,
        locale: locale,
      });
    } else {
      return format(date, 'HH:mm', { locale });
    }
  }
  return format(date, 'HH:mm', { locale });
}

export function formatDateRange(
  from: Date,
  to?: Date,
  locale?: Locale,
  t?: ReturnType<typeof useTranslations>
) {
  if (!to || from.toISOString() === to.toISOString()) {
    return (
      t?.('date_range.no_post_on_date', {
        date: format(from, 'MMM dd, yyyy', { locale }),
      }) ?? `No post was created on ${format(from, 'MMM dd, yyyy', { locale })}`
    );
  }
  return (
    t?.('date_range.no_posts_between_dates', {
      startDate: format(from, 'MMM dd, yyyy', { locale }),
      endDate: format(to, 'MMM dd, yyyy', { locale }),
    }) ??
    `No posts created between ${format(from, 'MMM dd, yyyy', { locale })} and ${format(to, 'MMM dd, yyyy', { locale })}`
  );
}
