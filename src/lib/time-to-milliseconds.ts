/**
 * Converts a time string (e.g., "50min", "1h", "24sec", "3.5d") into milliseconds.
 *
 * @param timeString The time string to convert. It should follow the format
 * of a number followed by a unit (e.g., "min", "h", "sec", "d").
 * @returns The equivalent time in milliseconds, or undefined if the input
 * string is invalid.
 */
export function timeStringToMilliseconds({
  timeString,
}: {
  timeString: string;
}) {
  const match = timeString.match(/^(\d+(\.\d+)?)(min|h|sec|d)$/);

  if (!match) {
    return undefined;
  }

  const value = parseFloat(match[1]);
  const unit = match[3];

  switch (unit) {
    case 'sec':
      return value * 1000;
    case 'min':
      return value * 1000 * 60;
    case 'h':
      return value * 1000 * 60 * 60;
    case 'd':
      return value * 1000 * 60 * 60 * 24;
    default:
      return undefined;
  }
}
