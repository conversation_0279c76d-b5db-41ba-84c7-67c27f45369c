import { PrivateModelResponse } from '@/api/models/dtos/ai-model.dto';

/**
 * Filters a list of private AI models by a specified model type and maps them to a format suitable for select inputs.
 *
 * @param params - The parameters for filtering models.
 * @param params.modelType - The type of model to filter by. Must match `PrivateModelResponse['ModelType']`.
 * @param params.privateModelsDataList - An optional list of private models to filter.
 *
 * @returns An array of objects with `label` and `value` properties:
 * - `label`: The name of the model.
 * - `value`: The ID of the model.
 * Returns an empty array if the list is undefined or no matches are found.
 *
 * @example
 * ```ts
 * const models = getFilteredPrivateModelsByModelType({
 *   modelType: 'classification',
 *   privateModelsDataList: fetchedModels,
 * });
 * // models => [{ label: 'Model A', value: 1 }, ...]
 * ```
 */

export const getFilteredPrivateModelsByModelType = ({
  modelType,
  privateModelsDataList,
}: {
  modelType: PrivateModelResponse['ModelType'];
  privateModelsDataList?: PrivateModelResponse[];
}) => {
  return (
    privateModelsDataList
      ?.filter((model) => model.ModelType === modelType)
      .map((model) => ({
        label: model.ModelName,
        value: model.Id,
      })) ?? []
  );
};
