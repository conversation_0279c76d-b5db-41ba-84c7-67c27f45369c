'use client';

import { useTranslations } from 'next-intl';
import { useContext, createContext } from 'react';
import { toast } from 'sonner';

import {
  useUpdateFileKnowledge,
  useUpdateLogoKnowledge,
  useUpdateTextKnowledge,
  useUpdateColorKnowledge,
  useUpdateWebsiteKnowledge,
  useUpdateAudienceKnowledge,
} from '@/api/hooks/knowledge/mutations';
import {
  UpdateKnowledgeFileSchema,
  UpdateKnowledgeLogoSchema,
  UpdateKnowledgeTextSchema,
  updateKnowledgeLogoSchema,
  updateKnowledgeFileSchema,
  updateKnowledgeTextSchema,
  UpdateKnowledgeColorSchema,
  updateKnowledgeColorSchema,
  UpdateKnowledgeWebsiteSchema,
  updateKnowledgeWebsiteSchema,
  UpdateKnowledgeAudienceSchema,
  updateKnowledgeAudienceSchema,
} from '@/api/models/schemas/knowledge.schema';
import { useCurrentUserStore } from '@/stores/current-user-store';
import { useKnowledgeStore } from '@/stores/use-knowledge-store';

import {
  logoKnowledgeFormUpdateOpts,
  fileKnowledgeFormUpdateOpts,
  textKnowledgeFormUpdateOpts,
  colorKnowledgeFormUpdateOpts,
  websiteKnowledgeFormUpdateOpts,
  audienceKnowledgeFormUpdateOpts,
} from '@/config/knowledge/forms-config';

import { UpdateAudienceForm } from '@/components/knowledge/molecules/forms/update/update-audience-form';
import { UpdateContentFileForm } from '@/components/knowledge/molecules/forms/update/update-content-file-form';
import { UpdateContentTextForm } from '@/components/knowledge/molecules/forms/update/update-content-text-form';
import { UpdateContentUrlForm } from '@/components/knowledge/molecules/forms/update/update-content-url-form';
import { UpdateMediaColorForm } from '@/components/knowledge/molecules/forms/update/update-media-color-form';
import { UpdateMediaLogoForm } from '@/components/knowledge/molecules/forms/update/update-media-logo-form';
import { useAppForm } from '@/components/shared/form';

interface KnowledgeUpdateFormsContextType {
  handleFormUpdateAction: () => Promise<void>;
  renderRelatedUpdateForm: () => React.JSX.Element;
}

const KnowledgeUpdateFormStateContext = createContext<
  KnowledgeUpdateFormsContextType | undefined
>(undefined);

export function KnowledgeUpdateFormsProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const t = useTranslations();

  const { knowledge } = useKnowledgeStore();
  const user = useCurrentUserStore().getUser();

  const { mutateAsync: updateTextKnowledge } = useUpdateTextKnowledge();
  const { mutateAsync: updateFileKnowledge } = useUpdateFileKnowledge();
  const { mutateAsync: updateLogoKnowledge } = useUpdateLogoKnowledge();
  const { mutateAsync: updateColorKnowledge } = useUpdateColorKnowledge();
  const { mutateAsync: updateAudienceKnowledge } = useUpdateAudienceKnowledge();
  const { mutateAsync: updateWebsiteKnowledge } = useUpdateWebsiteKnowledge();

  const textFormUpdate = useAppForm({
    defaultValues: {
      knowledge: {
        ...textKnowledgeFormUpdateOpts.defaultValues.knowledge,
        CompanyId: user?.companyId ?? '',
        BrandId: user?.brandId ?? '',
        UserEmail: user?.email ?? '',
        Id: knowledge?.Id,
        Name: knowledge?.Name ?? '',
        Text: {
          Title: knowledge?.Text.Title ?? '',
          Description: knowledge?.Text.Description ?? '',
          Labels: knowledge?.Text.Labels ?? [],
        },
        CreationTimestamp: knowledge?.CreationTimestamp,
      },
    } as UpdateKnowledgeTextSchema,
    validators: {
      onChange: updateKnowledgeTextSchema(t),
    },
    onSubmit: async ({ value }) => {
      await updateTextKnowledge(value);
    },
  });

  const websiteFormUpdate = useAppForm({
    defaultValues: {
      knowledge: {
        ...websiteKnowledgeFormUpdateOpts.defaultValues.knowledge,
        CompanyId: user?.companyId ?? '',
        BrandId: user?.brandId ?? '',
        UserEmail: user?.email ?? '',
        Id: knowledge?.Id,
        Name: knowledge?.Name ?? '',
        Website: {
          Title: knowledge?.Website.Title ?? '',
          Link: knowledge?.Website.Link ?? '',
          Description: knowledge?.Website.Description ?? '',
        },
        CreationTimestamp: knowledge?.CreationTimestamp,
      },
    } as UpdateKnowledgeWebsiteSchema,
    validators: {
      onChange: updateKnowledgeWebsiteSchema(t),
    },
    onSubmit: async ({ value }) => {
      await updateWebsiteKnowledge(value);
    },
  });

  const fileFormUpdate = useAppForm({
    defaultValues: {
      ...fileKnowledgeFormUpdateOpts.defaultValues,
      knowledge: {
        ...fileKnowledgeFormUpdateOpts.defaultValues.knowledge,
        UserEmail: user?.email ?? '',
        CompanyId: user?.companyId ?? '',
        BrandId: user?.brandId ?? '',
        Id: knowledge?.Id,
        Name: knowledge?.Name ?? '',
        Doc: {
          Title: knowledge?.Doc.Title ?? '',
          Type: knowledge?.Doc.Type ?? '',
        },
        CreationTimestamp: knowledge?.CreationTimestamp,
      },
    } as UpdateKnowledgeFileSchema,
    validators: {
      onChange: updateKnowledgeFileSchema(t),
    },
    onSubmit: async ({ value }) => {
      await updateFileKnowledge(value);
    },
  });

  const logoFormUpdate = useAppForm({
    defaultValues: {
      ...logoKnowledgeFormUpdateOpts.defaultValues,
      knowledge: {
        ...logoKnowledgeFormUpdateOpts.defaultValues.knowledge,
        CompanyId: user?.companyId ?? '',
        UserEmail: user?.email ?? '',
        BrandId: user?.brandId ?? '',
        Id: knowledge?.Id,
        Logo: {
          Name: knowledge?.Logo.Name,
        },
        CreationTimestamp: knowledge?.CreationTimestamp,
      },
    } as UpdateKnowledgeLogoSchema,
    validators: {
      onChange: updateKnowledgeLogoSchema(t),
    },

    onSubmit: async ({ value }) => {
      await updateLogoKnowledge(value);
    },
  });

  const colorFormUpdate = useAppForm({
    defaultValues: {
      knowledge: {
        ...colorKnowledgeFormUpdateOpts.defaultValues.knowledge,
        UserEmail: user?.email ?? '',
        CompanyId: user?.companyId ?? '',
        BrandId: user?.brandId ?? '',
        Id: knowledge?.Id,
        Name: knowledge?.Name,
        Color: knowledge?.Color?.map((color) => ({
          Index: color.Index,
          ColorHex: color.ColorHex,
        })),
        CreationTimestamp: knowledge?.CreationTimestamp,
      },
    } as UpdateKnowledgeColorSchema,
    validators: {
      onChange: updateKnowledgeColorSchema(t),
    },
    onSubmit: async ({ value }) => {
      await updateColorKnowledge(value);
    },
  });

  const audienceFormUpdate = useAppForm({
    defaultValues: {
      knowledge: {
        ...audienceKnowledgeFormUpdateOpts.defaultValues.knowledge,
        UserEmail: user?.email ?? '',
        CompanyId: user?.companyId ?? '',
        BrandId: user?.brandId ?? '',
        Id: knowledge?.Id,
        Audience: {
          ...knowledge?.Audience,
          Gender: knowledge?.Audience.Gender ?? 'male',
          InterestAndPref: knowledge?.Audience.InterestAndPref ?? [],
          SpendingBehavior: knowledge?.Audience.SpendingBehavior ?? [],
          BusinessIndustry: knowledge?.Audience.BusinessIndustry ?? [],
          AgeRange: [knowledge?.Audience.AgeMin, knowledge?.Audience.AgeMax],
        },
        CreationTimestamp: knowledge?.CreationTimestamp,
      },
    } as UpdateKnowledgeAudienceSchema,
    validators: {
      onChange: updateKnowledgeAudienceSchema(t),
    },
    onSubmit: async ({ value }) => {
      await updateAudienceKnowledge(value);
    },
  });

  function renderRelatedUpdateForm() {
    switch (knowledge?.Type) {
      case 'text_plain':
        return <UpdateContentTextForm form={textFormUpdate} />;
      case 'text_doc':
        return <UpdateContentFileForm form={fileFormUpdate} />;
      case 'text_website':
        return <UpdateContentUrlForm form={websiteFormUpdate} />;
      case 'audience':
        return <UpdateAudienceForm form={audienceFormUpdate} />;
      case 'media_logo':
        return <UpdateMediaLogoForm form={logoFormUpdate} />;
      case 'media_color':
        return <UpdateMediaColorForm form={colorFormUpdate} />;
      default:
        return <h1>Problem went getting the content</h1>;
    }
  }

  async function handleFormUpdateAction() {
    switch (knowledge?.Type) {
      case 'text_plain':
        return await textFormUpdate.handleSubmit();
      case 'text_doc':
        return await fileFormUpdate.handleSubmit();
      case 'text_website':
        return await websiteFormUpdate.handleSubmit();
      case 'audience':
        return await audienceFormUpdate.handleSubmit();
      case 'media_logo':
        return await logoFormUpdate.handleSubmit();
      case 'media_color':
        return await colorFormUpdate.handleSubmit();
      default:
        toast.error(
          `This ${knowledge?.Type} is not handled yet check KnowledgeDetail component.`
        );
        return;
    }
  }

  const contextValue: KnowledgeUpdateFormsContextType = {
    handleFormUpdateAction,
    renderRelatedUpdateForm,
  };

  return (
    <KnowledgeUpdateFormStateContext.Provider value={contextValue}>
      {children}
    </KnowledgeUpdateFormStateContext.Provider>
  );
}

export function useKnowledgeUpdateForms() {
  const context = useContext(KnowledgeUpdateFormStateContext);

  if (context === undefined) {
    throw new Error(
      'useKnowledgeUpdateFormState must be used within a KnowledgeUpdateFormStateProvider'
    );
  }

  return context;
}
