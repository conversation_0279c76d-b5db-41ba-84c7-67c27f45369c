import React, { useState, ReactNode, useContext, createContext } from 'react';

type Theme = 'light' | 'dark';

interface LocalThemeContextType {
  theme: Theme;
  toggleTheme: () => void;
  setTheme: (theme: Theme) => void;
}

const LocalThemeContext = createContext<LocalThemeContextType | undefined>(
  undefined
);

export const useLocalTheme = () => {
  const ctx = useContext(LocalThemeContext);
  if (!ctx)
    throw new Error('useLocalTheme must be used within a LocalThemeProvider');
  return ctx;
};

export const LocalThemeProvider = ({ children }: { children: ReactNode }) => {
  const [theme, setTheme] = useState<Theme>('light');

  const toggleTheme = () =>
    setTheme((prev) => (prev === 'light' ? 'dark' : 'light'));

  return (
    <LocalThemeContext.Provider value={{ theme, toggleTheme, setTheme }}>
      {children}
    </LocalThemeContext.Provider>
  );
};
