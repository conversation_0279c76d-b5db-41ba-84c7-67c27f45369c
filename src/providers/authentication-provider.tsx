'use client';

import { useEffect } from 'react';

import { useDirection } from '@/hooks/use-direction';

import { redirect, usePathname } from '@/i18n/routing';
import { useCurrentUserStore } from '@/stores/current-user-store';

interface AuthenticationProviderProps {
  children: React.ReactNode;
}

export function AuthenticationProvider({
  children,
}: AuthenticationProviderProps) {
  const { locale } = useDirection();
  const pathname = usePathname();

  const getUser = useCurrentUserStore((s) => s.getUser);
  const currentUser = getUser();

  useEffect(() => {
    if (
      currentUser &&
      (pathname.startsWith('/login') || pathname.startsWith('/signup'))
    ) {
      redirect({ locale, href: '/dashboard' });
    }

    if (!currentUser && pathname.startsWith('/dashboard')) {
      redirect({ locale, href: '/login' });
    }
  }, [locale, currentUser, pathname]);

  return <>{children}</>;
}
