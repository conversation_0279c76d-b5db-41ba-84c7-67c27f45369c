import {
  vi,
  test,
  expect,
  afterAll,
  describe,
  beforeAll,
  beforeEach,
} from 'vitest';

import { useIsMobile } from '@/hooks/use-mobile';

import { act, renderHook } from '@testing-library/react';

describe('useIsMobile', () => {
  let mediaQueryListeners: Array<EventListener> = [];
  let originalInnerWidth: number;

  beforeAll(() => {
    originalInnerWidth = window.innerWidth;
  });

  const createMatchMedia = (width: number) => {
    return (query: string): MediaQueryList => {
      return {
        matches: width < 768,
        media: query,
        onchange: null,
        addEventListener: (event: string, listener: EventListener) => {
          mediaQueryListeners.push(listener);
        },
        removeEventListener: (event: string, listener: EventListener) => {
          mediaQueryListeners = mediaQueryListeners.filter(
            (l) => l !== listener
          );
        },
        addListener: (listener: EventListener) => {
          mediaQueryListeners.push(listener);
        },
        removeListener: (listener: EventListener) => {
          mediaQueryListeners = mediaQueryListeners.filter(
            (l) => l !== listener
          );
        },
        dispatchEvent: vi.fn(),
      } as MediaQueryList;
    };
  };

  beforeEach(() => {
    mediaQueryListeners = [];
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 1024,
    });

    vi.spyOn(window, 'matchMedia').mockImplementation(
      createMatchMedia(window.innerWidth)
    );
  });

  test('should return false for desktop screens', () => {
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 1024,
    });

    vi.spyOn(window, 'matchMedia').mockImplementation(
      createMatchMedia(window.innerWidth)
    );

    const { result } = renderHook(() => useIsMobile());
    expect(result.current).toBe(false);
  });

  test('should return true for mobile screens', () => {
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 500,
    });

    vi.spyOn(window, 'matchMedia').mockImplementation(
      createMatchMedia(window.innerWidth)
    );

    const { result } = renderHook(() => useIsMobile());
    expect(result.current).toBe(true);
  });

  test('should update when screen size changes', () => {
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 1024,
    });

    vi.spyOn(window, 'matchMedia').mockImplementation(
      createMatchMedia(window.innerWidth)
    );

    const { result } = renderHook(() => useIsMobile());
    expect(result.current).toBe(false);

    act(() => {
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 500,
      });

      vi.spyOn(window, 'matchMedia').mockImplementation(
        createMatchMedia(window.innerWidth)
      );

      window.dispatchEvent(new Event('resize'));

      mediaQueryListeners.forEach((listener) => {
        listener(new Event('change') as Event);
      });
    });

    expect(result.current).toBe(true);
  });

  afterAll(() => {
    Object.defineProperty(window, 'innerWidth', {
      value: originalInnerWidth,
    });
    vi.restoreAllMocks();
  });
});
