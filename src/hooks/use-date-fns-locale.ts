import { format, isToday, Locale as DateFnsLocale } from 'date-fns';
import { ar } from 'date-fns/locale/ar';
import { de } from 'date-fns/locale/de';
import { enUS } from 'date-fns/locale/en-US';
import { fr } from 'date-fns/locale/fr';
import { it } from 'date-fns/locale/it';
import { useLocale } from 'next-intl';

export const dateFnsLocales = {
  en: enUS,
  fr: fr,
  it: it,
  de: de,
  ar: ar,
} as const;

export type SupportedDateFnsLocales = keyof typeof dateFnsLocales;

export function useDateFnsLocale(): DateFnsLocale {
  const locale = useLocale() as SupportedDateFnsLocales;
  return dateFnsLocales[locale] || enUS;
}

export function useFormatDate() {
  const locale = useDateFnsLocale();

  return {
    format: (date: Date | number, formatStr: string) => {
      return format(date, formatStr, { locale });
    },
    isToday: (date: Date | number) => {
      return isToday(date);
    },
  };
}
