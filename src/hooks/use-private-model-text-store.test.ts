import { test, expect, describe, beforeEach } from 'vitest';

import { usePrivateModelTextStore } from '@/stores/private-model-text-store';
const mockData = {
  Tags: ['tag1', 'tag2'],
  CompanyId: 'company-123',
  BrandId: 'brand-456',
  UserEmail: '<EMAIL>',
  ModelName: 'Test Model',
  Description: 'Test Description',
  SocialMediaData: [
    { platform: 'facebook' as const, pageName: 'TestPage' },
    { platform: 'instagram' as const, pageName: 'TestInsta' },
  ],
};

describe('PrivateModelTextStore', () => {
  beforeEach(() => {
    usePrivateModelTextStore.getState().clearTextFormValues();
  });

  test('initial state should be null', () => {
    const state = usePrivateModelTextStore.getState();
    expect(state.textFormValues).toBeNull();
  });

  test('setTextFormValues should update the state', () => {
    const { setTextFormValues, getTextFormValues } =
      usePrivateModelTextStore.getState();

    setTextFormValues(mockData);

    expect(getTextFormValues()).toEqual(mockData);
  });

  test('clearTextFormValues should reset state to null', () => {
    const { setTextFormValues, clearTextFormValues, getTextFormValues } =
      usePrivateModelTextStore.getState();

    setTextFormValues(mockData);
    expect(getTextFormValues()).toEqual(mockData);

    clearTextFormValues();
    expect(getTextFormValues()).toBeNull();
  });

  test('setTextFormValues should handle null values', () => {
    const { setTextFormValues, getTextFormValues } =
      usePrivateModelTextStore.getState();

    setTextFormValues(mockData);
    setTextFormValues(null);

    expect(getTextFormValues()).toBeNull();
  });
});
