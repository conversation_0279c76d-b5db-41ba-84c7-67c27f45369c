# Testing

---

## Table of Contents <!-- omit in toc -->


- [Introduction](#introduction)
- [Installation](#installation)
    - [Working with playwright](#working-with-playwright)
    - [Working with vitest](#working-with-vitest)
- [Running tests](#running-tests)

---

## Introduction

Our project uses [Playwright](https://playwright.dev/) for E2E testing and [Vitest](https://vitest.dev/) for unit testing.

## Installation

```bash
pnpm dlx playwright install
```

## Running tests

1. Run development server

   ```bash
   pnpm dev
   ```

2. Run Playwright

    > This command lets you run e2e testing in the browser.

   ```bash
    pnpm test:e2e --ui
    ```

   or

    ```bash
    npx test:e2e
    ```
   
3. In case 

---

<div align="center">

Previous: [Tables](tables.md)

</div>