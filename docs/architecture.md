# Architecture of the project

## Table of Contents <!-- omit in toc -->

---

- [Architecture of the project](#architecture-of-the-project)
  - [Introduction](#introduction)
  - [Folder structure](#folder-structure)
  - [Pages](#pages)

---

## Introduction

As far as this project uses [Next.js](https://nextjs.org/) framework for building React applications, the folders are used as routes. This means the more folders you add to your app folder, the more routes you will get. Additionally, if you create a new folder inside another folder, you will get nested routes. To better understand these concepts, we suggest looking at the image below.

<img src="assets/images/folder-routes.png" alt="routes" width="450"/>

## Folder structure

```txt
.
├── .github <-- Here are your github actions
├── .husky <-- Here are your husky config files (git hooks)
├── .storybook <-- Here are your storybook config files
├── e2e <-- Here are your E2E tests with playwright
├── public <-- Here are your public assets (static files)
│   ├── favicon
│   ├── flags
│   └── images
└── src
    ├── api <-- Here are where your API calls are located (axios + react query)
    │   ├── config <-- Here are where the axios config (interceptors, endpoints, query keys, core API client, etc. ) are located
    │   ├── helpers <-- Common helpers for API calls
    │   ├── hooks <-- Custom hooks for API calls
    │   ├── models
    │   │   ├── dtos <-- Data Transfer Objects (API responses)
    │   │   └── schemas <-- Schemas for API form validation
    │   └── services <-- API services
    ├── app
    │   └── [locale] <-- Here are your pages (routes)
    │       ├── (auth)
    │       │   ├── login
    │       │   └── signup
    │       └── dashboard
    │           ├── campaigns
    │           ├── calendar
    │           ├── knowledge
    │           ├── private-model
    │           ├── posts
    │           └── ...
    ├── assets <-- Here are your dynamic assets (Images, etc.)
    ├── components <-- Here are your components (pages content, UI components, shared components, etc.)
    │   ├── auth
    │   ├── campaigns
    │   ├── dashboard
    │   ├── shared
    │   ├── ui
    │   └── ...
    ├── config <-- Here are your config files (sidebar, language, etc.)
    ├── constants <-- Here are your constants files (sidebar, language, etc.)
    ├── hooks <-- Here are your global hooks
    ├── i18n <-- Here are your internationalization files
    │   └── locales
    │       ├── ar
    │       ├── de
    │       ├── en
    │       ├── fr
    │       └── it
    ├── lib <-- Here are your global utils
    ├── providers <-- Here are your global providers (react query, next intl, etc.)
    ├── styles <-- Here are your global styles (tailwind, etc.)
    └── stories <-- Here are your stories for storybook
```

## Pages

Pages are located in the `src/app/[locale]` folder. We use `[locale]` directory to support internationalization with ability generate static website.

---

<div align="center">

Previous: [Installing and Running](installing-and-running.md) | Next: [Internationalization](i18n.md)

</div>
