# Auth

## Table of Contents <!-- omit in toc -->

---

- [General info](#general-info)
  - [Auth via email flow](#auth-via-email-flow)
  - [Auth via external services or social networks flow](#auth-via-external-services-or-social-networks-flow)
- [Configure auth](#configure-auth)
- [Auth via Google](#auth-via-google)
- [Auth via Linkedin](#auth-via-linkedin)
- [Auth via Microsoft](#auth-via-microsoft)
- [Logout](#logout)

---

## General info

### Auth via email flow

By default, our project used sign-in and sign-up via email and password.

```mermaid
sequenceDiagram
    participant A as Fronted App (Web, Desktop)
    participant B as Backend App

    A->>B: 1. Sign up via email and password
    A->>B: 2. Sign in via email and password
    B->>A: 3. Get a JWT token
    A->>B: 4. Make any requests using a JWT token
```

### Auth via external services or social networks flow

Also, you can sign up via other external services or social networks like Google, Microsoft and LinkedIn.

```mermaid
sequenceDiagram
    participant B as External Auth Services (Google, LinkedIn, etc)
    participant A as Fronted App (Web, Desktop)
    participant C as Backend App

    A->>B: 1. Sign in through an external service
    B->>A: 2. Get Access Token
    A->>C: 3. Send Access Token to auth endpoint
    C->>A: 4. Get a JWT token
    A->>C: 5. Make any requests using a JWT token
```

For auth with external services or social networks you need:

1. Sign in through an external service and get access token(s).

2. Call one of endpoints with access token received in frontend app on 1-st step and get JWT token from the backend app.

   ```text
   POST /api/auth/register/google

   POST /api/v1/auth/register/facebook

   POST /api/v1/auth/register/microsoft
   ```

3. Make any requests using a JWT token

## Configure Auth

1. Generate secret keys for `access token` and `refresh token`:

   ```bash
   node -e "console.log('\nAUTH_JWT_SECRET=' + require('crypto').randomBytes(256).toString('base64') + '\n\nAUTH_REFRESH_SECRET=' + require('crypto').randomBytes(256).toString('base64') + '\n\nAUTH_FORGOT_SECRET=' + require('crypto').randomBytes(256).toString('base64') + '\n\nAUTH_CONFIRM_EMAIL_SECRET=' + require('crypto').randomBytes(256).toString('base64'));"
   ```

2. Go to `.env.*` and replace `AUTH_JWT_SECRET` and `AUTH_REFRESH_SECRET` with output from step 1.

   ```text
   AUTH_JWT_SECRET=HERE_SECRET_KEY_FROM_STEP_1
   AUTH_REFRESH_SECRET=HERE_SECRET_KEY_FROM_STEP_1
   ```


## Auth via Google

1. You need a `Client Id`. You can find these pieces of information by going to the [Developer Console](https://console.cloud.google.com/), clicking your project (if you don't have it create project here https://console.cloud.google.com/projectcreate) -> `APIs & services` -> `credentials`.

2. Find Client ID in the `Additional information` section and copy it.

3. Add `Client Id` to `GOOGLE_CLIENT_ID` in `.env.*`

   ```text
   GOOGLE_CLIENT_ID=abc
   ```

4. Add your domains to `Authorized JavaScript origins` like this:

   <img src="assets/images/authorized-javascript-origins.jpg" alt="routes" width="450"/>

   > For local tests or development add both `http://localhost` and `http://localhost:<port_number>`

## Logout

1. Call the following endpoint:

   ```text
   POST /api/auth/logout
   ```

2. Remove `access token` and `refresh token` from your client app (cookies, localStorage, etc.)

---

<div align="center">

Previous: [Internationalization](i18n.md) | Next: [API](api.md)

</div>