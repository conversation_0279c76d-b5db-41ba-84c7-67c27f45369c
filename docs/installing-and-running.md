# Installation

---

## Table of Contents <!-- omit in toc -->

- [Installation](#installation)
  - [Development](#development)
    - [Run locally](#run-locally)
    - [Run with docker](#run-with-docker)
  - [Production](#production)
    - [Run production locally](#run-production-locally)

---

## Development

### Run Locally

1. Clone repository

   > You can freely specify the folder name. In this case we choose `kwore-front`.

   ```bash
   git clone --depth 1 https://github.com/Kwore/kwore-front-v2.git kwore-front
   ```

2. Go to folder, and install dependencies

   ```bash
   cd kwore-front
   pnpm install
   ```

3. Copy example environment file

   ```bash
   cp env.example .env.development
   ```

4. Run development server

   ```bash
   pnpm dev
   ```

5. Open your browser and navigate to [http://localhost:3000](http://localhost:3000)

### Run with Docker

1. Clone repository

   > You can freely specify the folder name. In this case we choose `my-app`.

   ```bash
   git clone --depth 1 https://github.com/Kwore/kwore-front-v2.git my-app
   ```

2. Go to folder, and copy `env.example` as `.env.development`.

   ```bash
   cd my-app/
   cp env.example .env.development
   ```

3. Run container

   > You only need to run this command once. After that, you can use `docker compose -f docker-compose.dev.yml up` to start the container.

   ```bash
   docker compose -f docker-compose.dev.yml up --build
   ```

4. Open your browser and navigate to [http://localhost:3000](http://localhost:3000)

## Production

### Run production Locally

1. Clone repository

   > You can freely specify the folder name. In this case we choose `my-app`.

   ```bash
   git clone --depth 1 https://github.com/brocoders/extensive-react-boilerplate.git my-app
   ```

2. Go to folder, and install dependencies

   ```bash
   cd my-app
   pnpm install
   ```

3. Copy example environment file

   ```bash
   cp env.example .env.development
   ```

4. Build application

   ```bash
   pnpm build
   ```

5. Run production server

   ```bash
    pnpm start
   ```

---

<div align="center">

Previous: [Get Started](readme.md) | Next: [Architecture](architecture.md)

</div>
