# Stage 1: Build the Next.js app
FROM node:22.14.0-alpine AS builder
LABEL name="kwore-image"
WORKDIR /app

RUN npm install -g pnpm@10.3.0

COPY package.json pnpm-lock.yaml ./
RUN pnpm install --frozen-lockfile

COPY . .
RUN pnpm run build

# Stage 2: Run the app
FROM node:22.14.0-alpine AS runner
LABEL name="kwore-app"
WORKDIR /app

ARG NODE_ENV=production
ENV NODE_ENV=$NODE_ENV

RUN npm install -g pnpm@10.3.0

ENV NODE_ENV=production

# I have used this for simplicity and should be replaced with a more secure way
COPY --from=builder /app/ .

EXPOSE 3000
CMD ["pnpm", "start"]