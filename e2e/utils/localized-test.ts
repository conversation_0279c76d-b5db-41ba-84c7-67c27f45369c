import { test as base } from '@playwright/test';

export const LOCALES = [
  { code: 'en', direction: 'ltr', isRtl: false },
  { code: 'ar', direction: 'rtl', isRtl: true },
];

/**
 * Runs tests for all locales.
 * @param name The test suite name.
 * @param fn The test function that receives the locale.
 */

export function localizedTest(
  name: string,
  fn: (locale: (typeof LOCALES)[number]) => void
) {
  base.describe.parallel(name, () => {
    LOCALES.forEach((locale) => {
      base.describe(`Locale: ${locale.code}`, () => {
        fn(locale);
      });
    });
  });
}
