export function pxToRem(
  pxValue: string | number,
  rootFontSize: number = 16
): string {
  // Optional: Strict "px" suffix check for strings
  if (typeof pxValue === 'string') {
    const trimmed = pxValue.trim();
    if (!/^-?\d+(\.\d+)?px$/.test(trimmed)) {
      throw new Error(
        'pxValue must be a string ending with "px" (e.g., "16px")'
      );
    }
    pxValue = trimmed.replace('px', '');
  }

  const px = typeof pxValue === 'string' ? parseFloat(pxValue) : pxValue;
  if (isNaN(px)) {
    throw new Error('pxValue must be a valid number');
  }

  if (rootFontSize <= 0) {
    throw new Error('rootFontSize must be greater than 0');
  }

  const remValue = px / rootFontSize;
  return px === 0 ? '0' : `${remValue}rem`; // Optional: Simplify "0rem" to "0"
}
