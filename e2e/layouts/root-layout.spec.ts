import { test, expect } from '@playwright/test';

import { localizedTest } from '../utils/localized-test';

localizedTest('Root Layout', (locale) => {
  test.beforeEach(async ({ page }) => {
    await page.goto(`/${locale.code}`);
  });

  test('sets correct lang and direction attributes', async ({ page }) => {
    const html = page.locator('html');

    await expect(html).toHaveAttribute('lang', locale.code);
    await expect(html).toHaveAttribute('dir', locale.direction);
  });
});
