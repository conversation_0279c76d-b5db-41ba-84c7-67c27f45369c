import {
  SIDEBAR_WIDTH,
  SIDEBAR_WIDTH_ICON,
  SIDEBAR_COOKIE_NAME,
  SIDEBAR_KEYBOARD_SHORTCUT,
} from '@/constants/sidebar';
import { DashboardTestIds } from '@/constants/test-ids';
import { test, expect } from '@playwright/test';

import { getCookieValue } from '../utils/get-cookie-value';
import { localizedTest } from '../utils/localized-test';
import { pxToRem } from '../utils/px-to-rem';

localizedTest('Dashboard Layout Rendering', (locale) => {
  test.beforeEach(async ({ page }) => {
    await page.context().addCookies([
      {
        name: 'sidebar_state',
        value: 'false',
        domain: 'localhost',
        path: '/',
      },
    ]);

    await page.goto(`/${locale.code}/dashboard`);
  });

  test('sidebar state matches cookie on load', async ({ page }) => {
    const leftSidebar = page.getByTestId(DashboardTestIds.left_sidebar);
    const leftSidebarCookie = await getCookieValue(page, SIDEBAR_COOKIE_NAME);

    const expectedState =
      leftSidebarCookie === 'true' ? SIDEBAR_WIDTH : SIDEBAR_WIDTH_ICON;

    const width = await leftSidebar.evaluate(
      (el) => getComputedStyle(el).width
    );

    expect(pxToRem(width)).toBe(expectedState);
  });

  test('renders SidebarLeft on the correct side', async ({ page }) => {
    const sidebar = page.getByTestId(DashboardTestIds.left_sidebar);
    await expect(sidebar).toHaveAttribute(
      'data-side',
      locale.isRtl ? 'right' : 'left'
    );
  });

  test('renders Header and children', async ({ page }) => {
    await expect(page.getByTestId(DashboardTestIds.header)).toBeVisible();
    await expect(page.getByTestId(DashboardTestIds.children)).toBeVisible();
  });

  test('toggling sidebar updates cookie and UI', async ({ page }) => {
    const sidebar = page.getByTestId(DashboardTestIds.left_sidebar);
    const sidebarToggle = page.getByTestId(DashboardTestIds.sidebar_rail);

    const initialWidth = await sidebar.evaluate(
      (el) => getComputedStyle(el).width
    );

    await sidebarToggle.click();

    const newWidth = await sidebar.evaluate((el) => getComputedStyle(el).width);
    const newCookieValue = await getCookieValue(page, SIDEBAR_COOKIE_NAME);

    const wasOpen = pxToRem(initialWidth) === SIDEBAR_WIDTH;
    const expectedWidth = wasOpen ? SIDEBAR_WIDTH_ICON : SIDEBAR_WIDTH;
    const expectedCookie = wasOpen ? 'false' : 'true';

    expect(pxToRem(newWidth)).toBe(expectedWidth);
    expect(newCookieValue).toBe(expectedCookie);
  });

  test('keyboard shortcut toggles sidebar', async ({ page }) => {
    const sidebar = page.getByTestId(DashboardTestIds.left_sidebar);

    const initialWidth = await sidebar.evaluate(
      (el) => getComputedStyle(el).width
    );
    const isInitiallyOpen = pxToRem(initialWidth) === SIDEBAR_WIDTH;

    const isMac = await page.evaluate(() => navigator.platform.includes('Mac'));
    const modifier = isMac ? 'Meta' : 'Control';

    const shortcutRail = page.getByTestId(DashboardTestIds.sidebar_rail);
    await expect(shortcutRail).toHaveAttribute(
      'title',
      isMac
        ? `⌘+${SIDEBAR_KEYBOARD_SHORTCUT}`
        : `Ctrl+${SIDEBAR_KEYBOARD_SHORTCUT}`
    );

    await page.keyboard.press(`${modifier}+${SIDEBAR_KEYBOARD_SHORTCUT}`);

    await page.waitForTimeout(200);

    const finalWidth = await sidebar.evaluate(
      (el) => getComputedStyle(el).width
    );
    const finalCookie = await getCookieValue(page, SIDEBAR_COOKIE_NAME);

    expect(pxToRem(finalWidth)).toBe(
      isInitiallyOpen ? SIDEBAR_WIDTH_ICON : SIDEBAR_WIDTH
    );
    expect(finalCookie).toBe(isInitiallyOpen ? 'false' : 'true');
  });
});
