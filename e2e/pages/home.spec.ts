import { test, expect } from '@playwright/test';

import { localizedTest } from '../utils/localized-test';

localizedTest('Home Page', (locale) => {
  test.beforeEach(async ({ page }) => {
    await page.goto(`/${locale.code}`);
  });

  test('should have correct metadata', async ({ page }) => {
    await expect(page).toHaveTitle('Kwore | Home');

    const description = page.locator('meta[name="description"]');
    await expect(description).toHaveAttribute('content', 'Welcome to Kwore');
  });

  test('should have two buttons: Login and Dashboard', async ({ page }) => {
    const loginButton = page.getByRole('button', { name: 'Login' });
    await expect(loginButton).toBeVisible();

    const dashboardButton = page.getByRole('button', { name: 'Dashboard' });
    await expect(dashboardButton).toBeVisible();
  });

  test('should redirect to the login page when clicking the Login button', async ({
    page,
  }) => {
    await page.getByRole('link', { name: 'Login' }).click();

    await expect(page).toHaveURL(`/${locale.code}/login`);
  });

  test('should redirect to the dashboard page when clicking the Dashboard button', async ({
    page,
  }) => {
    await page.getByRole('link', { name: 'Dashboard' }).click();

    await expect(page).toHaveURL(`/${locale.code}/dashboard`);
  });
});
