{"name": "kwore", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "test": "vitest run && playwright test", "test:unit": "vitest", "test:unit --ui": "vitest --ui", "test:unit --coverage": "vitest --coverage", "test:e2e": "playwright test", "test:e2e --ui": "playwright test --ui", "test:codegen": "pnpm dlx playwright codegen localhost:3000", "test:e2e:report": "playwright show-report", "type-check": "tsc -b --noEmit", "lint": "next lint", "lint:fix": "next lint --fix", "lint:project": "eslint . --report-unused-disable-directives", "format": "prettier --check \"src/**/*.{ts,tsx}\"", "format:fix": "prettier --write \"src/**/*.{ts,tsx}\"", "format:project": "pnpm exec prettier . --write", "commitlint": "commitlint --edit", "commit": "cz", "prepare": "husky", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "lint-staged": {"src/**/*.{ts,tsx}": ["eslint --fix --max-warnings=0", "prettier --write --list-different"]}, "config": {"commitizen": {"path": "@commitlint/cz-commitlint"}}, "dependencies": {"@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^4.1.2", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@reduxjs/toolkit": "^2.6.0", "@remixicon/react": "^4.6.0", "@svgr/webpack": "^8.1.0", "@tanstack/react-form": "^1.1.0", "@tanstack/react-query": "^5.68.0", "@tanstack/react-query-devtools": "^5.68.0", "@tanstack/react-table": "^8.21.2", "@tanstack/zod-form-adapter": "^0.42.1", "apify-client": "^2.12.1", "axios": "^1.8.3", "babel-plugin-react-compiler": "19.0.0-beta-714736e-20250131", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.0", "date-fns": "^4.1.0", "embla-carousel-react": "^8.5.2", "embla-carousel-wheel-gestures": "^8.0.2", "emblor": "^1.4.8", "firebase": "^11.4.0", "framer-motion": "^12.4.7", "input-otp": "^1.4.2", "lucide-react": "^0.474.0", "next": "15.2.2", "next-auth": "^4.24.11", "next-intl": "^3.26.5", "next-themes": "^0.4.4", "node-fetch": "^3.3.2", "nuqs": "^2.4.1", "puppeteer": "^24.8.0", "radix-ui": "^1.1.3", "react": "^19.0.0", "react-color": "^2.19.3", "react-colorful": "^5.6.1", "react-day-picker": "8.10.1", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.54.2", "react-pdf": "^9.2.1", "react-redux": "^9.2.0", "react-resizable-panels": "^2.1.7", "recharts": "^2.15.1", "rtl-detect": "^1.1.2", "sharp": "^0.34.1", "sonner": "^1.7.4", "svgson": "^5.3.1", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "usehooks-ts": "^3.1.1", "vaul": "^1.1.2", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@chromatic-com/storybook": "3.2.4", "@commitlint/cli": "^19.7.1", "@commitlint/config-conventional": "^19.7.1", "@commitlint/cz-commitlint": "^19.6.1", "@commitlint/format": "^19.5.0", "@commitlint/types": "^19.5.0", "@eslint/eslintrc": "^3.3.0", "@next/eslint-plugin-next": "^15.1.7", "@playwright/test": "^1.50.1", "@storybook/addon-actions": "^8.6.0", "@storybook/addon-essentials": "8.5.3", "@storybook/addon-interactions": "8.5.3", "@storybook/addon-onboarding": "8.5.3", "@storybook/blocks": "8.5.3", "@storybook/nextjs": "8.5.3", "@storybook/react": "8.5.3", "@storybook/test": "8.5.3", "@tanstack/eslint-plugin-query": "^5.68.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@types/node": "^20.17.19", "@types/react": "^19.0.10", "@types/react-color": "^3.0.13", "@types/react-dom": "^19.0.4", "@types/react-dropzone": "^5.1.0", "@types/rtl-detect": "^1.0.3", "@vitejs/plugin-react": "^4.3.4", "@vitest/coverage-v8": "^3.0.9", "@vitest/ui": "^3.0.9", "commitizen": "^4.3.1", "conventional-changelog-atom": "^5.0.0", "eslint": "^9.21.0", "eslint-config-next": "15.1.6", "eslint-config-prettier": "^10.0.1", "eslint-plugin-perfectionist": "^4.9.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-storybook": "^0.11.3", "husky": "^9.1.7", "jsdom": "^26.0.0", "lint-staged": "^15.4.3", "postcss": "^8.5.3", "prettier": "^3.5.2", "prettier-plugin-tailwindcss": "^0.6.11", "react-scan": "^0.2.9", "storybook": "8.5.3", "storybook-next-intl": "^1.2.5", "tailwindcss": "^3.4.17", "typescript": "^5.7.3", "typescript-eslint": "^8.25.0", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.0.9"}, "eslintConfig": {"extends": ["plugin:storybook/recommended"]}}